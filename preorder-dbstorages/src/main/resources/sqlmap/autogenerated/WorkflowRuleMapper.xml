<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.WorkflowRuleMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRulePO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="product_line_code" property="productLineCode" jdbcType="VARCHAR" />
    <result column="process_code" property="processCode" jdbcType="VARCHAR" />
    <result column="current_node" property="currentNode" jdbcType="VARCHAR" />
    <result column="next_node" property="nextNode" jdbcType="VARCHAR" />
    <result column="action" property="action" jdbcType="VARCHAR" />
    <result column="condition" property="condition" jdbcType="VARCHAR" />
    <result column="assgin" property="assgin" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, product_line_code, process_code, current_node, next_node, `action`, `condition`, 
    assgin
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRuleExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_workflow_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_workflow_rule
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_workflow_rule
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRuleExample" >
    delete from tb_workflow_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRulePO" >
    insert into tb_workflow_rule (id, product_line_code, process_code, 
      current_node, next_node, `action`, 
      `condition`, assgin)
    values (#{id,jdbcType=VARCHAR}, #{productLineCode,jdbcType=VARCHAR}, #{processCode,jdbcType=VARCHAR}, 
      #{currentNode,jdbcType=VARCHAR}, #{nextNode,jdbcType=VARCHAR}, #{action,jdbcType=VARCHAR}, 
      #{condition,jdbcType=VARCHAR}, #{assgin,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRulePO" >
    insert into tb_workflow_rule
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="productLineCode != null" >
        product_line_code,
      </if>
      <if test="processCode != null" >
        process_code,
      </if>
      <if test="currentNode != null" >
        current_node,
      </if>
      <if test="nextNode != null" >
        next_node,
      </if>
      <if test="action != null" >
        `action`,
      </if>
      <if test="condition != null" >
        `condition`,
      </if>
      <if test="assgin != null" >
        assgin,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="productLineCode != null" >
        #{productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="processCode != null" >
        #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="currentNode != null" >
        #{currentNode,jdbcType=VARCHAR},
      </if>
      <if test="nextNode != null" >
        #{nextNode,jdbcType=VARCHAR},
      </if>
      <if test="action != null" >
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="condition != null" >
        #{condition,jdbcType=VARCHAR},
      </if>
      <if test="assgin != null" >
        #{assgin,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRuleExample" resultType="java.lang.Integer" >
    select count(*) from tb_workflow_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_workflow_rule
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.productLineCode != null" >
        product_line_code = #{record.productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="record.processCode != null" >
        process_code = #{record.processCode,jdbcType=VARCHAR},
      </if>
      <if test="record.currentNode != null" >
        current_node = #{record.currentNode,jdbcType=VARCHAR},
      </if>
      <if test="record.nextNode != null" >
        next_node = #{record.nextNode,jdbcType=VARCHAR},
      </if>
      <if test="record.action != null" >
        `action` = #{record.action,jdbcType=VARCHAR},
      </if>
      <if test="record.condition != null" >
        `condition` = #{record.condition,jdbcType=VARCHAR},
      </if>
      <if test="record.assgin != null" >
        assgin = #{record.assgin,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_workflow_rule
    set id = #{record.id,jdbcType=VARCHAR},
      product_line_code = #{record.productLineCode,jdbcType=VARCHAR},
      process_code = #{record.processCode,jdbcType=VARCHAR},
      current_node = #{record.currentNode,jdbcType=VARCHAR},
      next_node = #{record.nextNode,jdbcType=VARCHAR},
      `action` = #{record.action,jdbcType=VARCHAR},
      `condition` = #{record.condition,jdbcType=VARCHAR},
      assgin = #{record.assgin,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRulePO" >
    update tb_workflow_rule
    <set >
      <if test="productLineCode != null" >
        product_line_code = #{productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="processCode != null" >
        process_code = #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="currentNode != null" >
        current_node = #{currentNode,jdbcType=VARCHAR},
      </if>
      <if test="nextNode != null" >
        next_node = #{nextNode,jdbcType=VARCHAR},
      </if>
      <if test="action != null" >
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="condition != null" >
        `condition` = #{condition,jdbcType=VARCHAR},
      </if>
      <if test="assgin != null" >
        assgin = #{assgin,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.WorkflowRulePO" >
    update tb_workflow_rule
    set product_line_code = #{productLineCode,jdbcType=VARCHAR},
      process_code = #{processCode,jdbcType=VARCHAR},
      current_node = #{currentNode,jdbcType=VARCHAR},
      next_node = #{nextNode,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      `condition` = #{condition,jdbcType=VARCHAR},
      assgin = #{assgin,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>