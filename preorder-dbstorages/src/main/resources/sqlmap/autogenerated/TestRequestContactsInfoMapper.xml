<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.TestRequestContactsInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="VARCHAR" />
    <result column="contacts_type" property="contactsType" jdbcType="TINYINT" />
    <result column="deliver_to" property="deliverTo" jdbcType="VARCHAR" />
    <result column="deliver_others" property="deliverOthers" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, order_id, contacts_type, deliver_to, deliver_others, created_date, created_by, 
    modified_date, modified_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_request_contacts
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_request_contacts
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_request_contacts
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoExample" >
    delete from tb_test_request_contacts
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoPO" >
    insert into tb_test_request_contacts (id, order_id, contacts_type, 
      deliver_to, deliver_others, created_date, 
      created_by, modified_date, modified_by
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{contactsType,jdbcType=TINYINT}, 
      #{deliverTo,jdbcType=VARCHAR}, #{deliverOthers,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoPO" >
    insert into tb_test_request_contacts
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="contactsType != null" >
        contacts_type,
      </if>
      <if test="deliverTo != null" >
        deliver_to,
      </if>
      <if test="deliverOthers != null" >
        deliver_others,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="contactsType != null" >
        #{contactsType,jdbcType=TINYINT},
      </if>
      <if test="deliverTo != null" >
        #{deliverTo,jdbcType=VARCHAR},
      </if>
      <if test="deliverOthers != null" >
        #{deliverOthers,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_request_contacts
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_request_contacts
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null" >
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.contactsType != null" >
        contacts_type = #{record.contactsType,jdbcType=TINYINT},
      </if>
      <if test="record.deliverTo != null" >
        deliver_to = #{record.deliverTo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverOthers != null" >
        deliver_others = #{record.deliverOthers,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_request_contacts
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      contacts_type = #{record.contactsType,jdbcType=TINYINT},
      deliver_to = #{record.deliverTo,jdbcType=VARCHAR},
      deliver_others = #{record.deliverOthers,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoPO" >
    update tb_test_request_contacts
    <set >
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="contactsType != null" >
        contacts_type = #{contactsType,jdbcType=TINYINT},
      </if>
      <if test="deliverTo != null" >
        deliver_to = #{deliverTo,jdbcType=VARCHAR},
      </if>
      <if test="deliverOthers != null" >
        deliver_others = #{deliverOthers,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestContactsInfoPO" >
    update tb_test_request_contacts
    set order_id = #{orderId,jdbcType=VARCHAR},
      contacts_type = #{contactsType,jdbcType=TINYINT},
      deliver_to = #{deliverTo,jdbcType=VARCHAR},
      deliver_others = #{deliverOthers,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>