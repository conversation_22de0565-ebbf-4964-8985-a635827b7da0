<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.ReportFieldConfigMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.ReportFieldConfigPO" >
    <result column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="customer_id" property="customer_id" jdbcType="VARCHAR" />
    <result column="customer_group_code" property="customer_group_code" jdbcType="VARCHAR" />
    <result column="customer_group_name" property="customer_group_name" jdbcType="VARCHAR" />
    <result column="customer_name_cn" property="customer_name_cn" jdbcType="VARCHAR" />
    <result column="customer_name_en" property="customer_name_en" jdbcType="VARCHAR" />
    <result column="field_preorder_id" property="field_preorder_id" jdbcType="VARCHAR" />
    <result column="field_ots_id" property="field_ots_id" jdbcType="VARCHAR" />
    <result column="preorder_field_name" property="preorder_field_name" jdbcType="VARCHAR" />
    <result column="ots_field_name" property="ots_field_name" jdbcType="VARCHAR" />
    <result column="active_indicator" property="active_indicator" jdbcType="TINYINT" />
    <result column="created_by" property="created_by" jdbcType="VARCHAR" />
    <result column="created_date" property="created_date" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modified_by" jdbcType="VARCHAR" />
    <result column="modified_date" property="modified_date" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, customer_id, customer_group_code, customer_group_name, customer_name_cn, customer_name_en, 
    field_preorder_id, field_ots_id, preorder_field_name, ots_field_name, active_indicator, 
    created_by, created_date, modified_by, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ReportFieldConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_field_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ReportFieldConfigExample" >
    delete from tb_report_field_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ReportFieldConfigPO" >
    insert into tb_report_field_config (ID, customer_id, customer_group_code, 
      customer_group_name, customer_name_cn, 
      customer_name_en, field_preorder_id, field_ots_id, 
      preorder_field_name, ots_field_name, active_indicator, 
      created_by, created_date, modified_by, 
      modified_date)
    values (#{ID,jdbcType=VARCHAR}, #{customer_id,jdbcType=VARCHAR}, #{customer_group_code,jdbcType=VARCHAR}, 
      #{customer_group_name,jdbcType=VARCHAR}, #{customer_name_cn,jdbcType=VARCHAR}, 
      #{customer_name_en,jdbcType=VARCHAR}, #{field_preorder_id,jdbcType=VARCHAR}, #{field_ots_id,jdbcType=VARCHAR}, 
      #{preorder_field_name,jdbcType=VARCHAR}, #{ots_field_name,jdbcType=VARCHAR}, #{active_indicator,jdbcType=TINYINT}, 
      #{created_by,jdbcType=VARCHAR}, #{created_date,jdbcType=TIMESTAMP}, #{modified_by,jdbcType=VARCHAR}, 
      #{modified_date,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ReportFieldConfigPO" >
    insert into tb_report_field_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="customer_id != null" >
        customer_id,
      </if>
      <if test="customer_group_code != null" >
        customer_group_code,
      </if>
      <if test="customer_group_name != null" >
        customer_group_name,
      </if>
      <if test="customer_name_cn != null" >
        customer_name_cn,
      </if>
      <if test="customer_name_en != null" >
        customer_name_en,
      </if>
      <if test="field_preorder_id != null" >
        field_preorder_id,
      </if>
      <if test="field_ots_id != null" >
        field_ots_id,
      </if>
      <if test="preorder_field_name != null" >
        preorder_field_name,
      </if>
      <if test="ots_field_name != null" >
        ots_field_name,
      </if>
      <if test="active_indicator != null" >
        active_indicator,
      </if>
      <if test="created_by != null" >
        created_by,
      </if>
      <if test="created_date != null" >
        created_date,
      </if>
      <if test="modified_by != null" >
        modified_by,
      </if>
      <if test="modified_date != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="customer_id != null" >
        #{customer_id,jdbcType=VARCHAR},
      </if>
      <if test="customer_group_code != null" >
        #{customer_group_code,jdbcType=VARCHAR},
      </if>
      <if test="customer_group_name != null" >
        #{customer_group_name,jdbcType=VARCHAR},
      </if>
      <if test="customer_name_cn != null" >
        #{customer_name_cn,jdbcType=VARCHAR},
      </if>
      <if test="customer_name_en != null" >
        #{customer_name_en,jdbcType=VARCHAR},
      </if>
      <if test="field_preorder_id != null" >
        #{field_preorder_id,jdbcType=VARCHAR},
      </if>
      <if test="field_ots_id != null" >
        #{field_ots_id,jdbcType=VARCHAR},
      </if>
      <if test="preorder_field_name != null" >
        #{preorder_field_name,jdbcType=VARCHAR},
      </if>
      <if test="ots_field_name != null" >
        #{ots_field_name,jdbcType=VARCHAR},
      </if>
      <if test="active_indicator != null" >
        #{active_indicator,jdbcType=TINYINT},
      </if>
      <if test="created_by != null" >
        #{created_by,jdbcType=VARCHAR},
      </if>
      <if test="created_date != null" >
        #{created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="modified_by != null" >
        #{modified_by,jdbcType=VARCHAR},
      </if>
      <if test="modified_date != null" >
        #{modified_date,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ReportFieldConfigExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_field_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_field_config
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.customer_id != null" >
        customer_id = #{record.customer_id,jdbcType=VARCHAR},
      </if>
      <if test="record.customer_group_code != null" >
        customer_group_code = #{record.customer_group_code,jdbcType=VARCHAR},
      </if>
      <if test="record.customer_group_name != null" >
        customer_group_name = #{record.customer_group_name,jdbcType=VARCHAR},
      </if>
      <if test="record.customer_name_cn != null" >
        customer_name_cn = #{record.customer_name_cn,jdbcType=VARCHAR},
      </if>
      <if test="record.customer_name_en != null" >
        customer_name_en = #{record.customer_name_en,jdbcType=VARCHAR},
      </if>
      <if test="record.field_preorder_id != null" >
        field_preorder_id = #{record.field_preorder_id,jdbcType=VARCHAR},
      </if>
      <if test="record.field_ots_id != null" >
        field_ots_id = #{record.field_ots_id,jdbcType=VARCHAR},
      </if>
      <if test="record.preorder_field_name != null" >
        preorder_field_name = #{record.preorder_field_name,jdbcType=VARCHAR},
      </if>
      <if test="record.ots_field_name != null" >
        ots_field_name = #{record.ots_field_name,jdbcType=VARCHAR},
      </if>
      <if test="record.active_indicator != null" >
        active_indicator = #{record.active_indicator,jdbcType=TINYINT},
      </if>
      <if test="record.created_by != null" >
        created_by = #{record.created_by,jdbcType=VARCHAR},
      </if>
      <if test="record.created_date != null" >
        created_date = #{record.created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modified_by != null" >
        modified_by = #{record.modified_by,jdbcType=VARCHAR},
      </if>
      <if test="record.modified_date != null" >
        modified_date = #{record.modified_date,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_field_config
    set ID = #{record.ID,jdbcType=VARCHAR},
      customer_id = #{record.customer_id,jdbcType=VARCHAR},
      customer_group_code = #{record.customer_group_code,jdbcType=VARCHAR},
      customer_group_name = #{record.customer_group_name,jdbcType=VARCHAR},
      customer_name_cn = #{record.customer_name_cn,jdbcType=VARCHAR},
      customer_name_en = #{record.customer_name_en,jdbcType=VARCHAR},
      field_preorder_id = #{record.field_preorder_id,jdbcType=VARCHAR},
      field_ots_id = #{record.field_ots_id,jdbcType=VARCHAR},
      preorder_field_name = #{record.preorder_field_name,jdbcType=VARCHAR},
      ots_field_name = #{record.ots_field_name,jdbcType=VARCHAR},
      active_indicator = #{record.active_indicator,jdbcType=TINYINT},
      created_by = #{record.created_by,jdbcType=VARCHAR},
      created_date = #{record.created_date,jdbcType=TIMESTAMP},
      modified_by = #{record.modified_by,jdbcType=VARCHAR},
      modified_date = #{record.modified_date,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>