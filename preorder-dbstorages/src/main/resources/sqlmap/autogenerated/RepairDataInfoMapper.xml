<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.RepairDataInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.RepairDataInfoPO" >
    <result column="OrderID" property="orderID" jdbcType="VARCHAR" />
    <result column="TRFNo" property="TRFNo" jdbcType="VARCHAR" />
    <result column="OrderNO" property="orderNO" jdbcType="VARCHAR" />
    <result column="DffFlag" property="dffFlag" jdbcType="VARCHAR" />
    <result column="DffFormID" property="dffFormID" jdbcType="VARCHAR" />
    <result column="FieldCode" property="fieldCode" jdbcType="VARCHAR" />
    <result column="Value" property="value" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    OrderID, TRFNo, OrderNO, DffFlag, DffFormID, FieldCode, `Value`
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.RepairDataInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tmp_repair_data
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.RepairDataInfoExample" >
    delete from tmp_repair_data
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.RepairDataInfoPO" >
    insert into tmp_repair_data (OrderID, TRFNo, OrderNO, 
      DffFlag, DffFormID, FieldCode, 
      `Value`)
    values (#{orderID,jdbcType=VARCHAR}, #{TRFNo,jdbcType=VARCHAR}, #{orderNO,jdbcType=VARCHAR}, 
      #{dffFlag,jdbcType=VARCHAR}, #{dffFormID,jdbcType=VARCHAR}, #{fieldCode,jdbcType=VARCHAR}, 
      #{value,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.RepairDataInfoPO" >
    insert into tmp_repair_data
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderID != null" >
        OrderID,
      </if>
      <if test="TRFNo != null" >
        TRFNo,
      </if>
      <if test="orderNO != null" >
        OrderNO,
      </if>
      <if test="dffFlag != null" >
        DffFlag,
      </if>
      <if test="dffFormID != null" >
        DffFormID,
      </if>
      <if test="fieldCode != null" >
        FieldCode,
      </if>
      <if test="value != null" >
        `Value`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderID != null" >
        #{orderID,jdbcType=VARCHAR},
      </if>
      <if test="TRFNo != null" >
        #{TRFNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNO != null" >
        #{orderNO,jdbcType=VARCHAR},
      </if>
      <if test="dffFlag != null" >
        #{dffFlag,jdbcType=VARCHAR},
      </if>
      <if test="dffFormID != null" >
        #{dffFormID,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        #{value,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.RepairDataInfoExample" resultType="java.lang.Integer" >
    select count(*) from tmp_repair_data
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tmp_repair_data
    <set >
      <if test="record.orderID != null" >
        OrderID = #{record.orderID,jdbcType=VARCHAR},
      </if>
      <if test="record.TRFNo != null" >
        TRFNo = #{record.TRFNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNO != null" >
        OrderNO = #{record.orderNO,jdbcType=VARCHAR},
      </if>
      <if test="record.dffFlag != null" >
        DffFlag = #{record.dffFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.dffFormID != null" >
        DffFormID = #{record.dffFormID,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldCode != null" >
        FieldCode = #{record.fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null" >
        `Value` = #{record.value,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tmp_repair_data
    set OrderID = #{record.orderID,jdbcType=VARCHAR},
      TRFNo = #{record.TRFNo,jdbcType=VARCHAR},
      OrderNO = #{record.orderNO,jdbcType=VARCHAR},
      DffFlag = #{record.dffFlag,jdbcType=VARCHAR},
      DffFormID = #{record.dffFormID,jdbcType=VARCHAR},
      FieldCode = #{record.fieldCode,jdbcType=VARCHAR},
      `Value` = #{record.value,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>