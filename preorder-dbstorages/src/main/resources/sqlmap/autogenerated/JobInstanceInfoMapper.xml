<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.JobInstanceInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoPO" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="GeneralOrderInstanceID" property="generalorderinstanceid" jdbcType="VARCHAR" />
    <result column="LabSectionId" property="labsectionid" jdbcType="INTEGER" />
    <result column="OrderNo" property="orderno" jdbcType="VARCHAR" />
    <result column="JobNo" property="jobno" jdbcType="VARCHAR" />
    <result column="JobStatus" property="jobstatus" jdbcType="INTEGER" />
    <result column="LabInDate" property="labindate" jdbcType="TIMESTAMP" />
    <result column="LabOutDate" property="laboutdate" jdbcType="TIMESTAMP" />
    <result column="Remark" property="remark" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeindicator" jdbcType="TINYINT" />
    <result column="CreatedDate" property="createddate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdby" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifieddate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedby" jdbcType="VARCHAR" />
    <result column="LabSectionName" property="labsectionname" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, GeneralOrderInstanceID, LabSectionId, OrderNo, JobNo, JobStatus, LabInDate, LabOutDate, 
    Remark, ActiveIndicator, CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, LabSectionName
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_job_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_job_instance
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_job_instance
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoExample" >
    delete from tb_job_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoPO" >
    insert into tb_job_instance (ID, GeneralOrderInstanceID, LabSectionId, 
      OrderNo, JobNo, JobStatus, 
      LabInDate, LabOutDate, Remark, 
      ActiveIndicator, CreatedDate, CreatedBy, 
      ModifiedDate, ModifiedBy, LabSectionName)
    values (#{id,jdbcType=VARCHAR}, #{generalorderinstanceid,jdbcType=VARCHAR}, #{labsectionid,jdbcType=INTEGER},
      #{orderno,jdbcType=VARCHAR}, #{jobno,jdbcType=VARCHAR}, #{jobstatus,jdbcType=INTEGER},
      #{labindate,jdbcType=TIMESTAMP}, #{laboutdate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR},
      #{activeindicator,jdbcType=TINYINT}, #{createddate,jdbcType=TIMESTAMP}, #{createdby,jdbcType=VARCHAR},
      #{modifieddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, #{labsectionname,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoPO" >
    insert into tb_job_instance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="generalorderinstanceid != null" >
        GeneralOrderInstanceID,
      </if>
      <if test="labsectionid != null" >
        LabSectionId,
      </if>
      <if test="orderno != null" >
        OrderNo,
      </if>
      <if test="jobno != null" >
        JobNo,
      </if>
      <if test="jobstatus != null" >
        JobStatus,
      </if>
      <if test="labindate != null" >
        LabInDate,
      </if>
      <if test="laboutdate != null" >
        LabOutDate,
      </if>
      <if test="remark != null" >
        Remark,
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator,
      </if>
      <if test="createddate != null" >
        CreatedDate,
      </if>
      <if test="createdby != null" >
        CreatedBy,
      </if>
      <if test="modifieddate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedby != null" >
        ModifiedBy,
      </if>
      <if test="labsectionname != null" >
        LabSectionName,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="generalorderinstanceid != null" >
        #{generalorderinstanceid,jdbcType=VARCHAR},
      </if>
      <if test="labsectionid != null" >
        #{labsectionid,jdbcType=INTEGER},
      </if>
      <if test="orderno != null" >
        #{orderno,jdbcType=VARCHAR},
      </if>
      <if test="jobno != null" >
        #{jobno,jdbcType=VARCHAR},
      </if>
      <if test="jobstatus != null" >
        #{jobstatus,jdbcType=INTEGER},
      </if>
      <if test="labindate != null" >
        #{labindate,jdbcType=TIMESTAMP},
      </if>
      <if test="laboutdate != null" >
        #{laboutdate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        #{activeindicator,jdbcType=TINYINT},
      </if>
      <if test="createddate != null" >
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null" >
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="labsectionname != null" >
        #{labsectionname,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_job_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_job_instance
    <set >
      <if test="record.id != null" >
        ID = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.generalorderinstanceid != null" >
        GeneralOrderInstanceID = #{record.generalorderinstanceid,jdbcType=VARCHAR},
      </if>
      <if test="record.labsectionid != null" >
        LabSectionId = #{record.labsectionid,jdbcType=INTEGER},
      </if>
      <if test="record.orderno != null" >
        OrderNo = #{record.orderno,jdbcType=VARCHAR},
      </if>
      <if test="record.jobno != null" >
        JobNo = #{record.jobno,jdbcType=VARCHAR},
      </if>
      <if test="record.jobstatus != null" >
        JobStatus = #{record.jobstatus,jdbcType=INTEGER},
      </if>
      <if test="record.labindate != null" >
        LabInDate = #{record.labindate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.laboutdate != null" >
        LabOutDate = #{record.laboutdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        Remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.activeindicator != null" >
        ActiveIndicator = #{record.activeindicator,jdbcType=TINYINT},
      </if>
      <if test="record.createddate != null" >
        CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdby != null" >
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.modifieddate != null" >
        ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="record.labsectionname != null" >
        LabSectionName = #{record.labsectionname,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_job_instance
    set ID = #{record.id,jdbcType=VARCHAR},
      GeneralOrderInstanceID = #{record.generalorderinstanceid,jdbcType=VARCHAR},
      LabSectionId = #{record.labsectionid,jdbcType=INTEGER},
      OrderNo = #{record.orderno,jdbcType=VARCHAR},
      JobNo = #{record.jobno,jdbcType=VARCHAR},
      JobStatus = #{record.jobstatus,jdbcType=INTEGER},
      LabInDate = #{record.labindate,jdbcType=TIMESTAMP},
      LabOutDate = #{record.laboutdate,jdbcType=TIMESTAMP},
      Remark = #{record.remark,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeindicator,jdbcType=TINYINT},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      LabSectionName = #{record.labsectionname,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoPO" >
    update tb_job_instance
    <set >
      <if test="generalorderinstanceid != null" >
        GeneralOrderInstanceID = #{generalorderinstanceid,jdbcType=VARCHAR},
      </if>
      <if test="labsectionid != null" >
        LabSectionId = #{labsectionid,jdbcType=INTEGER},
      </if>
      <if test="orderno != null" >
        OrderNo = #{orderno,jdbcType=VARCHAR},
      </if>
      <if test="jobno != null" >
        JobNo = #{jobno,jdbcType=VARCHAR},
      </if>
      <if test="jobstatus != null" >
        JobStatus = #{jobstatus,jdbcType=INTEGER},
      </if>
      <if test="labindate != null" >
        LabInDate = #{labindate,jdbcType=TIMESTAMP},
      </if>
      <if test="laboutdate != null" >
        LabOutDate = #{laboutdate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator = #{activeindicator,jdbcType=TINYINT},
      </if>
      <if test="createddate != null" >
        CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null" >
        CreatedBy = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="labsectionname != null" >
        LabSectionName = #{labsectionname,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.JobInstanceInfoPO" >
    update tb_job_instance
    set GeneralOrderInstanceID = #{generalorderinstanceid,jdbcType=VARCHAR},
      LabSectionId = #{labsectionid,jdbcType=INTEGER},
      OrderNo = #{orderno,jdbcType=VARCHAR},
      JobNo = #{jobno,jdbcType=VARCHAR},
      JobStatus = #{jobstatus,jdbcType=INTEGER},
      LabInDate = #{labindate,jdbcType=TIMESTAMP},
      LabOutDate = #{laboutdate,jdbcType=TIMESTAMP},
      Remark = #{remark,jdbcType=VARCHAR},
      ActiveIndicator = #{activeindicator,jdbcType=TINYINT},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      LabSectionName = #{labsectionname,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>