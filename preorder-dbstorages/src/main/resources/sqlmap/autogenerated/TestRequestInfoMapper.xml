<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.TestRequestInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="GeneralOrderID" property="generalOrderID" jdbcType="VARCHAR" />
    <result column="EvaluationBasis" property="evaluationBasis" jdbcType="VARCHAR" />
    <result column="OtherRequirements" property="otherRequirements" jdbcType="VARCHAR" />
    <result column="Qualification" property="qualification" jdbcType="VARCHAR" />
    <result column="ReoprtDate" property="reoprtDate" jdbcType="TIMESTAMP" />
    <result column="ReportLanguage" property="reportLanguage" jdbcType="VARCHAR" />
    <result column="ReportManner" property="reportManner" jdbcType="INTEGER" />
    <result column="ReportType" property="reportType" jdbcType="VARCHAR" />
    <result column="ResultJudgingFlag" property="resultJudgingFlag" jdbcType="TINYINT" />
    <result column="ServiceType" property="serviceType" jdbcType="INTEGER" />
    <result column="DisplaySupplierFlag" property="displaySupplierFlag" jdbcType="TINYINT" />
    <result column="CommentFlag" property="commentFlag" jdbcType="TINYINT" />
    <result column="HardCopyFlag" property="hardCopyFlag" jdbcType="TINYINT" />
    <result column="ChineseReportFlag" property="chineseReportFlag" jdbcType="TINYINT" />
    <result column="TakePhotoFlag" property="takePhotoFlag" jdbcType="TINYINT" />
    <result column="ConfirmCoverPageFlag" property="confirmCoverPageFlag" jdbcType="TINYINT" />
    <result column="PackageIndicator" property="packageIndicator" jdbcType="VARCHAR" />
    <result column="TakePhotoRemark" property="takePhotoRemark" jdbcType="VARCHAR" />
    <result column="ReturnResidueSampleFlag" property="returnResidueSampleFlag" jdbcType="TINYINT" />
    <result column="ReturnTestedSampleFlag" property="returnTestedSampleFlag" jdbcType="TINYINT" />
    <result column="ReturnResidueSampleRemark" property="returnResidueSampleRemark" jdbcType="VARCHAR" />
    <result column="ReturnTestedSampleRemark" property="returnTestedSampleRemark" jdbcType="VARCHAR" />
    <result column="ReportAccreditationNeededFlag" property="reportAccreditationNeededFlag" jdbcType="TINYINT" />
    <result column="HardCopyToApplicantFlag" property="hardCopyToApplicantFlag" jdbcType="TINYINT" />
    <result column="HardCopyToPayertFlag" property="hardCopyToPayertFlag" jdbcType="TINYINT" />
    <result column="HardCopyToOther" property="hardCopyToOther" jdbcType="VARCHAR" />
    <result column="SoftCopyToApplicantFlag" property="softCopyToApplicantFlag" jdbcType="TINYINT" />
    <result column="SoftCopyToPayerFlag" property="softCopyToPayerFlag" jdbcType="TINYINT" />
    <result column="SoftCopyToOther" property="softCopyToOther" jdbcType="VARCHAR" />
    <result column="HtmlString" property="htmlString" jdbcType="VARCHAR" />
    <result column="PdfReportSecurity" property="pdfReportSecurity" jdbcType="TINYINT" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="PaymentRemark" property="paymentRemark" jdbcType="VARCHAR" />
    <result column="QualificationType" property="qualificationType" jdbcType="VARCHAR" />
    <result column="DraftReportRequired" property="draftReportRequired" jdbcType="TINYINT" />
    <result column="ProformaInvoice" property="proformaInvoice" jdbcType="TINYINT" />
    <result column="LiquidTestSample" property="liquidTestSample" jdbcType="TINYINT" />
    <result column="VatType" property="vatType" jdbcType="TINYINT" />
    <result column="PhotoRequest" property="photoRequest" jdbcType="VARCHAR" />
    <result column="ReportRequirement" property="reportRequirement" jdbcType="VARCHAR" />
    <result column="NeedConclusion" property="needConclusion" jdbcType="TINYINT" />
    <result column="HardCopyReportDeliverWay" property="hardCopyReportDeliverWay" jdbcType="VARCHAR" />
    <result column="InvoiceDeliverWay" property="invoiceDeliverWay" jdbcType="VARCHAR" />
    <result column="QrcodeFlag" property="qrcodeFlag" jdbcType="VARCHAR" />
    <result column="SealFlag" property="sealFlag" jdbcType="TINYINT" />
    <result column="SealCode" property="sealCode" jdbcType="VARCHAR" />
    <result column="SampleSaveDuration" property="sampleSaveDuration" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, GeneralOrderID, EvaluationBasis, OtherRequirements, Qualification, ReoprtDate, 
    ReportLanguage, ReportManner, ReportType, ResultJudgingFlag, ServiceType, DisplaySupplierFlag, 
    CommentFlag, HardCopyFlag, ChineseReportFlag, TakePhotoFlag, ConfirmCoverPageFlag, 
    PackageIndicator, TakePhotoRemark, ReturnResidueSampleFlag, ReturnTestedSampleFlag, 
    ReturnResidueSampleRemark, ReturnTestedSampleRemark, ReportAccreditationNeededFlag, 
    HardCopyToApplicantFlag, HardCopyToPayertFlag, HardCopyToOther, SoftCopyToApplicantFlag, 
    SoftCopyToPayerFlag, SoftCopyToOther, HtmlString, PdfReportSecurity, ActiveIndicator, 
    CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, PaymentRemark,
    QualificationType, DraftReportRequired, ProformaInvoice, LiquidTestSample, VatType, 
    PhotoRequest, ReportRequirement, NeedConclusion, HardCopyReportDeliverWay, InvoiceDeliverWay, 
    QrcodeFlag, SealFlag, SealCode,SampleSaveDuration
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_request
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_request
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoExample" >
    delete from tb_test_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoPO" >
    insert into tb_test_request (ID, GeneralOrderID, EvaluationBasis, 
      OtherRequirements, Qualification, ReoprtDate, 
      ReportLanguage, ReportManner, ReportType, 
      ResultJudgingFlag, ServiceType, DisplaySupplierFlag, 
      CommentFlag, HardCopyFlag, ChineseReportFlag, 
      TakePhotoFlag, ConfirmCoverPageFlag, PackageIndicator, 
      TakePhotoRemark, ReturnResidueSampleFlag, ReturnTestedSampleFlag, 
      ReturnResidueSampleRemark, ReturnTestedSampleRemark, 
      ReportAccreditationNeededFlag, HardCopyToApplicantFlag, 
      HardCopyToPayertFlag, HardCopyToOther, SoftCopyToApplicantFlag, 
      SoftCopyToPayerFlag, SoftCopyToOther, HtmlString, 
      PdfReportSecurity, ActiveIndicator, CreatedBy, 
      CreatedDate, ModifiedBy, ModifiedDate, 
      PaymentRemark,
      QualificationType, DraftReportRequired, 
      ProformaInvoice, LiquidTestSample, VatType, 
      PhotoRequest, ReportRequirement, NeedConclusion, 
      HardCopyReportDeliverWay, InvoiceDeliverWay, 
      QrcodeFlag, SealFlag, SealCode, SampleSaveDuration
      )
    values (#{ID,jdbcType=VARCHAR}, #{generalOrderID,jdbcType=VARCHAR}, #{evaluationBasis,jdbcType=VARCHAR}, 
      #{otherRequirements,jdbcType=VARCHAR}, #{qualification,jdbcType=VARCHAR}, #{reoprtDate,jdbcType=TIMESTAMP}, 
      #{reportLanguage,jdbcType=VARCHAR}, #{reportManner,jdbcType=INTEGER}, #{reportType,jdbcType=VARCHAR}, 
      #{resultJudgingFlag,jdbcType=TINYINT}, #{serviceType,jdbcType=INTEGER}, #{displaySupplierFlag,jdbcType=TINYINT}, 
      #{commentFlag,jdbcType=TINYINT}, #{hardCopyFlag,jdbcType=TINYINT}, #{chineseReportFlag,jdbcType=TINYINT}, 
      #{takePhotoFlag,jdbcType=TINYINT}, #{confirmCoverPageFlag,jdbcType=TINYINT}, #{packageIndicator,jdbcType=VARCHAR}, 
      #{takePhotoRemark,jdbcType=VARCHAR}, #{returnResidueSampleFlag,jdbcType=TINYINT}, 
      #{returnTestedSampleFlag,jdbcType=TINYINT}, #{returnResidueSampleRemark,jdbcType=VARCHAR}, 
      #{returnTestedSampleRemark,jdbcType=VARCHAR}, #{reportAccreditationNeededFlag,jdbcType=TINYINT}, 
      #{hardCopyToApplicantFlag,jdbcType=TINYINT}, #{hardCopyToPayertFlag,jdbcType=TINYINT}, 
      #{hardCopyToOther,jdbcType=VARCHAR}, #{softCopyToApplicantFlag,jdbcType=TINYINT}, 
      #{softCopyToPayerFlag,jdbcType=TINYINT}, #{softCopyToOther,jdbcType=VARCHAR}, #{htmlString,jdbcType=VARCHAR}, 
      #{pdfReportSecurity,jdbcType=TINYINT}, #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{paymentRemark,jdbcType=VARCHAR},
      #{qualificationType,jdbcType=VARCHAR}, #{draftReportRequired,jdbcType=TINYINT},
      #{proformaInvoice,jdbcType=TINYINT}, #{liquidTestSample,jdbcType=TINYINT}, #{vatType,jdbcType=TINYINT}, 
      #{photoRequest,jdbcType=VARCHAR}, #{reportRequirement,jdbcType=VARCHAR}, #{needConclusion,jdbcType=TINYINT}, 
      #{hardCopyReportDeliverWay,jdbcType=VARCHAR}, #{invoiceDeliverWay,jdbcType=VARCHAR}, 
      #{qrcodeFlag,jdbcType=VARCHAR}, #{sealFlag,jdbcType=TINYINT}, #{sealCode,jdbcType=VARCHAR}, #{sampleSaveDuration,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoPO" >
    insert into tb_test_request
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID,
      </if>
      <if test="evaluationBasis != null" >
        EvaluationBasis,
      </if>
      <if test="otherRequirements != null" >
        OtherRequirements,
      </if>
      <if test="qualification != null" >
        Qualification,
      </if>
      <if test="reoprtDate != null" >
        ReoprtDate,
      </if>
      <if test="reportLanguage != null" >
        ReportLanguage,
      </if>
      <if test="reportManner != null" >
        ReportManner,
      </if>
      <if test="reportType != null" >
        ReportType,
      </if>
      <if test="resultJudgingFlag != null" >
        ResultJudgingFlag,
      </if>
      <if test="serviceType != null" >
        ServiceType,
      </if>
      <if test="displaySupplierFlag != null" >
        DisplaySupplierFlag,
      </if>
      <if test="commentFlag != null" >
        CommentFlag,
      </if>
      <if test="hardCopyFlag != null" >
        HardCopyFlag,
      </if>
      <if test="chineseReportFlag != null" >
        ChineseReportFlag,
      </if>
      <if test="takePhotoFlag != null" >
        TakePhotoFlag,
      </if>
      <if test="confirmCoverPageFlag != null" >
        ConfirmCoverPageFlag,
      </if>
      <if test="packageIndicator != null" >
        PackageIndicator,
      </if>
      <if test="takePhotoRemark != null" >
        TakePhotoRemark,
      </if>
      <if test="returnResidueSampleFlag != null" >
        ReturnResidueSampleFlag,
      </if>
      <if test="returnTestedSampleFlag != null" >
        ReturnTestedSampleFlag,
      </if>
      <if test="returnResidueSampleRemark != null" >
        ReturnResidueSampleRemark,
      </if>
      <if test="returnTestedSampleRemark != null" >
        ReturnTestedSampleRemark,
      </if>
      <if test="reportAccreditationNeededFlag != null" >
        ReportAccreditationNeededFlag,
      </if>
      <if test="hardCopyToApplicantFlag != null" >
        HardCopyToApplicantFlag,
      </if>
      <if test="hardCopyToPayertFlag != null" >
        HardCopyToPayertFlag,
      </if>
      <if test="hardCopyToOther != null" >
        HardCopyToOther,
      </if>
      <if test="softCopyToApplicantFlag != null" >
        SoftCopyToApplicantFlag,
      </if>
      <if test="softCopyToPayerFlag != null" >
        SoftCopyToPayerFlag,
      </if>
      <if test="softCopyToOther != null" >
        SoftCopyToOther,
      </if>
      <if test="htmlString != null" >
        HtmlString,
      </if>
      <if test="pdfReportSecurity != null" >
        PdfReportSecurity,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="paymentRemark != null" >
        PaymentRemark,
      </if>
      <if test="qualificationType != null" >
        QualificationType,
      </if>
      <if test="draftReportRequired != null" >
        DraftReportRequired,
      </if>
      <if test="proformaInvoice != null" >
        ProformaInvoice,
      </if>
      <if test="liquidTestSample != null" >
        LiquidTestSample,
      </if>
      <if test="vatType != null" >
        VatType,
      </if>
      <if test="photoRequest != null" >
        PhotoRequest,
      </if>
      <if test="reportRequirement != null" >
        ReportRequirement,
      </if>
      <if test="needConclusion != null" >
        NeedConclusion,
      </if>
      <if test="hardCopyReportDeliverWay != null" >
        HardCopyReportDeliverWay,
      </if>
      <if test="invoiceDeliverWay != null" >
        InvoiceDeliverWay,
      </if>
      <if test="qrcodeFlag != null" >
        QrcodeFlag,
      </if>
      <if test="sealFlag != null" >
        SealFlag,
      </if>
      <if test="sealCode != null" >
        SealCode,
      </if>
      <if test="sampleSaveDuration != null" >
        SampleSaveDuration,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderID != null" >
        #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="evaluationBasis != null" >
        #{evaluationBasis,jdbcType=VARCHAR},
      </if>
      <if test="otherRequirements != null" >
        #{otherRequirements,jdbcType=VARCHAR},
      </if>
      <if test="qualification != null" >
        #{qualification,jdbcType=VARCHAR},
      </if>
      <if test="reoprtDate != null" >
        #{reoprtDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportLanguage != null" >
        #{reportLanguage,jdbcType=VARCHAR},
      </if>
      <if test="reportManner != null" >
        #{reportManner,jdbcType=INTEGER},
      </if>
      <if test="reportType != null" >
        #{reportType,jdbcType=VARCHAR},
      </if>
      <if test="resultJudgingFlag != null" >
        #{resultJudgingFlag,jdbcType=TINYINT},
      </if>
      <if test="serviceType != null" >
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="displaySupplierFlag != null" >
        #{displaySupplierFlag,jdbcType=TINYINT},
      </if>
      <if test="commentFlag != null" >
        #{commentFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyFlag != null" >
        #{hardCopyFlag,jdbcType=TINYINT},
      </if>
      <if test="chineseReportFlag != null" >
        #{chineseReportFlag,jdbcType=TINYINT},
      </if>
      <if test="takePhotoFlag != null" >
        #{takePhotoFlag,jdbcType=TINYINT},
      </if>
      <if test="confirmCoverPageFlag != null" >
        #{confirmCoverPageFlag,jdbcType=TINYINT},
      </if>
      <if test="packageIndicator != null" >
        #{packageIndicator,jdbcType=VARCHAR},
      </if>
      <if test="takePhotoRemark != null" >
        #{takePhotoRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnResidueSampleFlag != null" >
        #{returnResidueSampleFlag,jdbcType=TINYINT},
      </if>
      <if test="returnTestedSampleFlag != null" >
        #{returnTestedSampleFlag,jdbcType=TINYINT},
      </if>
      <if test="returnResidueSampleRemark != null" >
        #{returnResidueSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnTestedSampleRemark != null" >
        #{returnTestedSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="reportAccreditationNeededFlag != null" >
        #{reportAccreditationNeededFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyToApplicantFlag != null" >
        #{hardCopyToApplicantFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyToPayertFlag != null" >
        #{hardCopyToPayertFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyToOther != null" >
        #{hardCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="softCopyToApplicantFlag != null" >
        #{softCopyToApplicantFlag,jdbcType=TINYINT},
      </if>
      <if test="softCopyToPayerFlag != null" >
        #{softCopyToPayerFlag,jdbcType=TINYINT},
      </if>
      <if test="softCopyToOther != null" >
        #{softCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="htmlString != null" >
        #{htmlString,jdbcType=VARCHAR},
      </if>
      <if test="pdfReportSecurity != null" >
        #{pdfReportSecurity,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentRemark != null" >
        #{paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="qualificationType != null" >
        #{qualificationType,jdbcType=VARCHAR},
      </if>
      <if test="draftReportRequired != null" >
        #{draftReportRequired,jdbcType=TINYINT},
      </if>
      <if test="proformaInvoice != null" >
        #{proformaInvoice,jdbcType=TINYINT},
      </if>
      <if test="liquidTestSample != null" >
        #{liquidTestSample,jdbcType=TINYINT},
      </if>
      <if test="vatType != null" >
        #{vatType,jdbcType=TINYINT},
      </if>
      <if test="photoRequest != null" >
        #{photoRequest,jdbcType=VARCHAR},
      </if>
      <if test="reportRequirement != null" >
        #{reportRequirement,jdbcType=VARCHAR},
      </if>
      <if test="needConclusion != null" >
        #{needConclusion,jdbcType=TINYINT},
      </if>
      <if test="hardCopyReportDeliverWay != null" >
        #{hardCopyReportDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDeliverWay != null" >
        #{invoiceDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="qrcodeFlag != null" >
        #{qrcodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="sealFlag != null" >
        #{sealFlag,jdbcType=TINYINT},
      </if>
      <if test="sealCode != null" >
        #{sealCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleSaveDuration != null" >
        #{SampleSaveDuration,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_request
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.generalOrderID != null" >
        GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluationBasis != null" >
        EvaluationBasis = #{record.evaluationBasis,jdbcType=VARCHAR},
      </if>
      <if test="record.otherRequirements != null" >
        OtherRequirements = #{record.otherRequirements,jdbcType=VARCHAR},
      </if>
      <if test="record.qualification != null" >
        Qualification = #{record.qualification,jdbcType=VARCHAR},
      </if>
      <if test="record.reoprtDate != null" >
        ReoprtDate = #{record.reoprtDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportLanguage != null" >
        ReportLanguage = #{record.reportLanguage,jdbcType=VARCHAR},
      </if>
      <if test="record.reportManner != null" >
        ReportManner = #{record.reportManner,jdbcType=INTEGER},
      </if>
      <if test="record.reportType != null" >
        ReportType = #{record.reportType,jdbcType=VARCHAR},
      </if>
      <if test="record.resultJudgingFlag != null" >
        ResultJudgingFlag = #{record.resultJudgingFlag,jdbcType=TINYINT},
      </if>
      <if test="record.serviceType != null" >
        ServiceType = #{record.serviceType,jdbcType=INTEGER},
      </if>
      <if test="record.displaySupplierFlag != null" >
        DisplaySupplierFlag = #{record.displaySupplierFlag,jdbcType=TINYINT},
      </if>
      <if test="record.commentFlag != null" >
        CommentFlag = #{record.commentFlag,jdbcType=TINYINT},
      </if>
      <if test="record.hardCopyFlag != null" >
        HardCopyFlag = #{record.hardCopyFlag,jdbcType=TINYINT},
      </if>
      <if test="record.chineseReportFlag != null" >
        ChineseReportFlag = #{record.chineseReportFlag,jdbcType=TINYINT},
      </if>
      <if test="record.takePhotoFlag != null" >
        TakePhotoFlag = #{record.takePhotoFlag,jdbcType=TINYINT},
      </if>
      <if test="record.confirmCoverPageFlag != null" >
        ConfirmCoverPageFlag = #{record.confirmCoverPageFlag,jdbcType=TINYINT},
      </if>
      <if test="record.packageIndicator != null" >
        PackageIndicator = #{record.packageIndicator,jdbcType=VARCHAR},
      </if>
      <if test="record.takePhotoRemark != null" >
        TakePhotoRemark = #{record.takePhotoRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.returnResidueSampleFlag != null" >
        ReturnResidueSampleFlag = #{record.returnResidueSampleFlag,jdbcType=TINYINT},
      </if>
      <if test="record.returnTestedSampleFlag != null" >
        ReturnTestedSampleFlag = #{record.returnTestedSampleFlag,jdbcType=TINYINT},
      </if>
      <if test="record.returnResidueSampleRemark != null" >
        ReturnResidueSampleRemark = #{record.returnResidueSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.returnTestedSampleRemark != null" >
        ReturnTestedSampleRemark = #{record.returnTestedSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAccreditationNeededFlag != null" >
        ReportAccreditationNeededFlag = #{record.reportAccreditationNeededFlag,jdbcType=TINYINT},
      </if>
      <if test="record.hardCopyToApplicantFlag != null" >
        HardCopyToApplicantFlag = #{record.hardCopyToApplicantFlag,jdbcType=TINYINT},
      </if>
      <if test="record.hardCopyToPayertFlag != null" >
        HardCopyToPayertFlag = #{record.hardCopyToPayertFlag,jdbcType=TINYINT},
      </if>
      <if test="record.hardCopyToOther != null" >
        HardCopyToOther = #{record.hardCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="record.softCopyToApplicantFlag != null" >
        SoftCopyToApplicantFlag = #{record.softCopyToApplicantFlag,jdbcType=TINYINT},
      </if>
      <if test="record.softCopyToPayerFlag != null" >
        SoftCopyToPayerFlag = #{record.softCopyToPayerFlag,jdbcType=TINYINT},
      </if>
      <if test="record.softCopyToOther != null" >
        SoftCopyToOther = #{record.softCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="record.htmlString != null" >
        HtmlString = #{record.htmlString,jdbcType=VARCHAR},
      </if>
      <if test="record.pdfReportSecurity != null" >
        PdfReportSecurity = #{record.pdfReportSecurity,jdbcType=TINYINT},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paymentRemark != null" >
        PaymentRemark = #{record.paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.qualificationType != null" >
        QualificationType = #{record.qualificationType,jdbcType=VARCHAR},
      </if>
      <if test="record.draftReportRequired != null" >
        DraftReportRequired = #{record.draftReportRequired,jdbcType=TINYINT},
      </if>
      <if test="record.proformaInvoice != null" >
        ProformaInvoice = #{record.proformaInvoice,jdbcType=TINYINT},
      </if>
      <if test="record.liquidTestSample != null" >
        LiquidTestSample = #{record.liquidTestSample,jdbcType=TINYINT},
      </if>
      <if test="record.vatType != null" >
        VatType = #{record.vatType,jdbcType=TINYINT},
      </if>
      <if test="record.photoRequest != null" >
        PhotoRequest = #{record.photoRequest,jdbcType=VARCHAR},
      </if>
      <if test="record.reportRequirement != null" >
        ReportRequirement = #{record.reportRequirement,jdbcType=VARCHAR},
      </if>
      <if test="record.needConclusion != null" >
        NeedConclusion = #{record.needConclusion,jdbcType=TINYINT},
      </if>
      <if test="record.hardCopyReportDeliverWay != null" >
        HardCopyReportDeliverWay = #{record.hardCopyReportDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceDeliverWay != null" >
        InvoiceDeliverWay = #{record.invoiceDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="record.qrcodeFlag != null" >
        QrcodeFlag = #{record.qrcodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.sealFlag != null" >
        SealFlag = #{record.sealFlag,jdbcType=TINYINT},
      </if>
      <if test="record.sealCode != null" >
        SealCode = #{record.sealCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleSaveDuration != null" >
        SampleSaveDuration = #{record.sampleSaveDuration,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_request
    set ID = #{record.ID,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      EvaluationBasis = #{record.evaluationBasis,jdbcType=VARCHAR},
      OtherRequirements = #{record.otherRequirements,jdbcType=VARCHAR},
      Qualification = #{record.qualification,jdbcType=VARCHAR},
      ReoprtDate = #{record.reoprtDate,jdbcType=TIMESTAMP},
      ReportLanguage = #{record.reportLanguage,jdbcType=VARCHAR},
      ReportManner = #{record.reportManner,jdbcType=INTEGER},
      ReportType = #{record.reportType,jdbcType=VARCHAR},
      ResultJudgingFlag = #{record.resultJudgingFlag,jdbcType=TINYINT},
      ServiceType = #{record.serviceType,jdbcType=INTEGER},
      DisplaySupplierFlag = #{record.displaySupplierFlag,jdbcType=TINYINT},
      CommentFlag = #{record.commentFlag,jdbcType=TINYINT},
      HardCopyFlag = #{record.hardCopyFlag,jdbcType=TINYINT},
      ChineseReportFlag = #{record.chineseReportFlag,jdbcType=TINYINT},
      TakePhotoFlag = #{record.takePhotoFlag,jdbcType=TINYINT},
      ConfirmCoverPageFlag = #{record.confirmCoverPageFlag,jdbcType=TINYINT},
      PackageIndicator = #{record.packageIndicator,jdbcType=VARCHAR},
      TakePhotoRemark = #{record.takePhotoRemark,jdbcType=VARCHAR},
      ReturnResidueSampleFlag = #{record.returnResidueSampleFlag,jdbcType=TINYINT},
      ReturnTestedSampleFlag = #{record.returnTestedSampleFlag,jdbcType=TINYINT},
      ReturnResidueSampleRemark = #{record.returnResidueSampleRemark,jdbcType=VARCHAR},
      ReturnTestedSampleRemark = #{record.returnTestedSampleRemark,jdbcType=VARCHAR},
      ReportAccreditationNeededFlag = #{record.reportAccreditationNeededFlag,jdbcType=TINYINT},
      HardCopyToApplicantFlag = #{record.hardCopyToApplicantFlag,jdbcType=TINYINT},
      HardCopyToPayertFlag = #{record.hardCopyToPayertFlag,jdbcType=TINYINT},
      HardCopyToOther = #{record.hardCopyToOther,jdbcType=VARCHAR},
      SoftCopyToApplicantFlag = #{record.softCopyToApplicantFlag,jdbcType=TINYINT},
      SoftCopyToPayerFlag = #{record.softCopyToPayerFlag,jdbcType=TINYINT},
      SoftCopyToOther = #{record.softCopyToOther,jdbcType=VARCHAR},
      HtmlString = #{record.htmlString,jdbcType=VARCHAR},
      PdfReportSecurity = #{record.pdfReportSecurity,jdbcType=TINYINT},
      ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      PaymentRemark = #{record.paymentRemark,jdbcType=VARCHAR},
      QualificationType = #{record.qualificationType,jdbcType=VARCHAR},
      DraftReportRequired = #{record.draftReportRequired,jdbcType=TINYINT},
      ProformaInvoice = #{record.proformaInvoice,jdbcType=TINYINT},
      LiquidTestSample = #{record.liquidTestSample,jdbcType=TINYINT},
      VatType = #{record.vatType,jdbcType=TINYINT},
      PhotoRequest = #{record.photoRequest,jdbcType=VARCHAR},
      ReportRequirement = #{record.reportRequirement,jdbcType=VARCHAR},
      NeedConclusion = #{record.needConclusion,jdbcType=TINYINT},
      HardCopyReportDeliverWay = #{record.hardCopyReportDeliverWay,jdbcType=VARCHAR},
      InvoiceDeliverWay = #{record.invoiceDeliverWay,jdbcType=VARCHAR},
      QrcodeFlag = #{record.qrcodeFlag,jdbcType=VARCHAR},
      SealFlag = #{record.sealFlag,jdbcType=TINYINT},
      SealCode = #{record.sealCode,jdbcType=VARCHAR},
      SampleSaveDuration = #{record.sampleSaveDuration,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoPO" >
    update tb_test_request
    <set >
      <if test="generalOrderID != null" >
        GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="evaluationBasis != null" >
        EvaluationBasis = #{evaluationBasis,jdbcType=VARCHAR},
      </if>
      <if test="otherRequirements != null" >
        OtherRequirements = #{otherRequirements,jdbcType=VARCHAR},
      </if>
      <if test="qualification != null" >
        Qualification = #{qualification,jdbcType=VARCHAR},
      </if>
      <if test="reoprtDate != null" >
        ReoprtDate = #{reoprtDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportLanguage != null" >
        ReportLanguage = #{reportLanguage,jdbcType=VARCHAR},
      </if>
      <if test="reportManner != null" >
        ReportManner = #{reportManner,jdbcType=INTEGER},
      </if>
      <if test="reportType != null" >
        ReportType = #{reportType,jdbcType=VARCHAR},
      </if>
      <if test="resultJudgingFlag != null" >
        ResultJudgingFlag = #{resultJudgingFlag,jdbcType=TINYINT},
      </if>
      <if test="serviceType != null" >
        ServiceType = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="displaySupplierFlag != null" >
        DisplaySupplierFlag = #{displaySupplierFlag,jdbcType=TINYINT},
      </if>
      <if test="commentFlag != null" >
        CommentFlag = #{commentFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyFlag != null" >
        HardCopyFlag = #{hardCopyFlag,jdbcType=TINYINT},
      </if>
      <if test="chineseReportFlag != null" >
        ChineseReportFlag = #{chineseReportFlag,jdbcType=TINYINT},
      </if>
      <if test="takePhotoFlag != null" >
        TakePhotoFlag = #{takePhotoFlag,jdbcType=TINYINT},
      </if>
      <if test="confirmCoverPageFlag != null" >
        ConfirmCoverPageFlag = #{confirmCoverPageFlag,jdbcType=TINYINT},
      </if>
      <if test="packageIndicator != null" >
        PackageIndicator = #{packageIndicator,jdbcType=VARCHAR},
      </if>
      <if test="takePhotoRemark != null" >
        TakePhotoRemark = #{takePhotoRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnResidueSampleFlag != null" >
        ReturnResidueSampleFlag = #{returnResidueSampleFlag,jdbcType=TINYINT},
      </if>
      <if test="returnTestedSampleFlag != null" >
        ReturnTestedSampleFlag = #{returnTestedSampleFlag,jdbcType=TINYINT},
      </if>
      <if test="returnResidueSampleRemark != null" >
        ReturnResidueSampleRemark = #{returnResidueSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnTestedSampleRemark != null" >
        ReturnTestedSampleRemark = #{returnTestedSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="reportAccreditationNeededFlag != null" >
        ReportAccreditationNeededFlag = #{reportAccreditationNeededFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyToApplicantFlag != null" >
        HardCopyToApplicantFlag = #{hardCopyToApplicantFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyToPayertFlag != null" >
        HardCopyToPayertFlag = #{hardCopyToPayertFlag,jdbcType=TINYINT},
      </if>
      <if test="hardCopyToOther != null" >
        HardCopyToOther = #{hardCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="softCopyToApplicantFlag != null" >
        SoftCopyToApplicantFlag = #{softCopyToApplicantFlag,jdbcType=TINYINT},
      </if>
      <if test="softCopyToPayerFlag != null" >
        SoftCopyToPayerFlag = #{softCopyToPayerFlag,jdbcType=TINYINT},
      </if>
      <if test="softCopyToOther != null" >
        SoftCopyToOther = #{softCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="htmlString != null" >
        HtmlString = #{htmlString,jdbcType=VARCHAR},
      </if>
      <if test="pdfReportSecurity != null" >
        PdfReportSecurity = #{pdfReportSecurity,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentRemark != null" >
        PaymentRemark = #{paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="qualificationType != null" >
        QualificationType = #{qualificationType,jdbcType=VARCHAR},
      </if>
      <if test="draftReportRequired != null" >
        DraftReportRequired = #{draftReportRequired,jdbcType=TINYINT},
      </if>
      <if test="proformaInvoice != null" >
        ProformaInvoice = #{proformaInvoice,jdbcType=TINYINT},
      </if>
      <if test="liquidTestSample != null" >
        LiquidTestSample = #{liquidTestSample,jdbcType=TINYINT},
      </if>
      <if test="vatType != null" >
        VatType = #{vatType,jdbcType=TINYINT},
      </if>
      <if test="photoRequest != null" >
        PhotoRequest = #{photoRequest,jdbcType=VARCHAR},
      </if>
      <if test="reportRequirement != null" >
        ReportRequirement = #{reportRequirement,jdbcType=VARCHAR},
      </if>
      <if test="needConclusion != null" >
        NeedConclusion = #{needConclusion,jdbcType=TINYINT},
      </if>
      <if test="hardCopyReportDeliverWay != null" >
        HardCopyReportDeliverWay = #{hardCopyReportDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDeliverWay != null" >
        InvoiceDeliverWay = #{invoiceDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="qrcodeFlag != null" >
        QrcodeFlag = #{qrcodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="sealFlag != null" >
        SealFlag = #{sealFlag,jdbcType=TINYINT},
      </if>
      <if test="sealCode != null" >
        SealCode = #{sealCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleSaveDuration != null" >
        SampleSaveDuration = #{sampleSaveDuration,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestRequestInfoPO" >
    update tb_test_request
    set GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      EvaluationBasis = #{evaluationBasis,jdbcType=VARCHAR},
      OtherRequirements = #{otherRequirements,jdbcType=VARCHAR},
      Qualification = #{qualification,jdbcType=VARCHAR},
      ReoprtDate = #{reoprtDate,jdbcType=TIMESTAMP},
      ReportLanguage = #{reportLanguage,jdbcType=VARCHAR},
      ReportManner = #{reportManner,jdbcType=INTEGER},
      ReportType = #{reportType,jdbcType=VARCHAR},
      ResultJudgingFlag = #{resultJudgingFlag,jdbcType=TINYINT},
      ServiceType = #{serviceType,jdbcType=INTEGER},
      DisplaySupplierFlag = #{displaySupplierFlag,jdbcType=TINYINT},
      CommentFlag = #{commentFlag,jdbcType=TINYINT},
      HardCopyFlag = #{hardCopyFlag,jdbcType=TINYINT},
      ChineseReportFlag = #{chineseReportFlag,jdbcType=TINYINT},
      TakePhotoFlag = #{takePhotoFlag,jdbcType=TINYINT},
      ConfirmCoverPageFlag = #{confirmCoverPageFlag,jdbcType=TINYINT},
      PackageIndicator = #{packageIndicator,jdbcType=VARCHAR},
      TakePhotoRemark = #{takePhotoRemark,jdbcType=VARCHAR},
      ReturnResidueSampleFlag = #{returnResidueSampleFlag,jdbcType=TINYINT},
      ReturnTestedSampleFlag = #{returnTestedSampleFlag,jdbcType=TINYINT},
      ReturnResidueSampleRemark = #{returnResidueSampleRemark,jdbcType=VARCHAR},
      ReturnTestedSampleRemark = #{returnTestedSampleRemark,jdbcType=VARCHAR},
      ReportAccreditationNeededFlag = #{reportAccreditationNeededFlag,jdbcType=TINYINT},
      HardCopyToApplicantFlag = #{hardCopyToApplicantFlag,jdbcType=TINYINT},
      HardCopyToPayertFlag = #{hardCopyToPayertFlag,jdbcType=TINYINT},
      HardCopyToOther = #{hardCopyToOther,jdbcType=VARCHAR},
      SoftCopyToApplicantFlag = #{softCopyToApplicantFlag,jdbcType=TINYINT},
      SoftCopyToPayerFlag = #{softCopyToPayerFlag,jdbcType=TINYINT},
      SoftCopyToOther = #{softCopyToOther,jdbcType=VARCHAR},
      HtmlString = #{htmlString,jdbcType=VARCHAR},
      PdfReportSecurity = #{pdfReportSecurity,jdbcType=TINYINT},
      ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      PaymentRemark = #{paymentRemark,jdbcType=VARCHAR},
      QualificationType = #{qualificationType,jdbcType=VARCHAR},
      DraftReportRequired = #{draftReportRequired,jdbcType=TINYINT},
      ProformaInvoice = #{proformaInvoice,jdbcType=TINYINT},
      LiquidTestSample = #{liquidTestSample,jdbcType=TINYINT},
      VatType = #{vatType,jdbcType=TINYINT},
      PhotoRequest = #{photoRequest,jdbcType=VARCHAR},
      ReportRequirement = #{reportRequirement,jdbcType=VARCHAR},
      NeedConclusion = #{needConclusion,jdbcType=TINYINT},
      HardCopyReportDeliverWay = #{hardCopyReportDeliverWay,jdbcType=VARCHAR},
      InvoiceDeliverWay = #{invoiceDeliverWay,jdbcType=VARCHAR},
      QrcodeFlag = #{qrcodeFlag,jdbcType=VARCHAR},
      SealFlag = #{sealFlag,jdbcType=TINYINT},
      SealCode = #{sealCode,jdbcType=VARCHAR},
      SampleSaveDuration = #{sampleSaveDuration,jdbcType=INTEGER}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>