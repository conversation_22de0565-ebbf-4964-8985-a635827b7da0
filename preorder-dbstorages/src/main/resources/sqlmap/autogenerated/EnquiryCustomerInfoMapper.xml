<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EnquiryCustomerInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="enquiry_id" property="enquiryId" jdbcType="VARCHAR" />
    <result column="customer_id" property="customerId" jdbcType="VARCHAR" />
    <result column="customer_group_id" property="customerGroupId" jdbcType="VARCHAR" />
    <result column="customer_address_cn" property="customerAddressCn" jdbcType="VARCHAR" />
    <result column="customer_address_en" property="customerAddressEn" jdbcType="VARCHAR" />
    <result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR" />
    <result column="customer_name_en" property="customerNameEn" jdbcType="VARCHAR" />
    <result column="contact_person_email" property="contactPersonEmail" jdbcType="VARCHAR" />
    <result column="contact_person_fax" property="contactPersonFax" jdbcType="VARCHAR" />
    <result column="contact_person_phone1" property="contactPersonPhone1" jdbcType="VARCHAR" />
    <result column="contact_person_name" property="contactPersonName" jdbcType="VARCHAR" />
    <result column="contact_person_remark" property="contactPersonRemark" jdbcType="VARCHAR" />
    <result column="contact_person_phone2" property="contactPersonPhone2" jdbcType="VARCHAR" />
    <result column="customer_credit" property="customerCredit" jdbcType="VARCHAR" />
    <result column="customer_usage" property="customerUsage" jdbcType="VARCHAR" />
    <result column="account_id" property="accountId" jdbcType="BIGINT" />
    <result column="boss_number" property="bossNumber" jdbcType="BIGINT" />
    <result column="contact_address_id" property="contactAddressId" jdbcType="VARCHAR" />
    <result column="buyer_group" property="buyerGroup" jdbcType="VARCHAR" />
    <result column="buyer_group_name" property="buyerGroupName" jdbcType="VARCHAR" />
    <result column="supplier_no" property="supplierNo" jdbcType="VARCHAR" />
    <result column="logo_cloud_id" property="logoCloudId" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="organization_name" property="organizationName" jdbcType="VARCHAR" />
    <result column="is_as_applicant" property="isAsApplicant" jdbcType="INTEGER" />
    <result column="report_delivered_to" property="reportDeliveredTo" jdbcType="VARCHAR" />
    <result column="failed_report_delivered_to" property="failedReportDeliveredTo" jdbcType="VARCHAR" />
    <result column="boss_site_user_id" property="bossSiteUserId" jdbcType="BIGINT" />
    <result column="boss_contact_id" property="bossContactId" jdbcType="BIGINT" />
    <result column="primary_flag" property="primaryFlag" jdbcType="VARCHAR" />
    <result column="monthly_payment" property="monthlyPayment" jdbcType="VARCHAR" />
    <result column="boss_location_code" property="bossLocationCode" jdbcType="VARCHAR" />
    <result column="payment_term_name" property="paymentTermName" jdbcType="VARCHAR" />
    <result column="sgsmart_account" property="sgsMartAccount" jdbcType="VARCHAR" />
    <result column="sgsmart_user_id" property="sgsMartUserId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, enquiry_id, customer_id, customer_group_id, customer_address_cn, customer_address_en, 
    customer_name_cn, customer_name_en, contact_person_email, contact_person_fax, contact_person_phone1, 
    contact_person_name, contact_person_remark, contact_person_phone2, customer_credit, 
    customer_usage, account_id, boss_number, contact_address_id, buyer_group, buyer_group_name, 
    supplier_no, logo_cloud_id, active_indicator, created_by, created_date, modified_by, 
    modified_date, organization_name, is_as_applicant, report_delivered_to, failed_report_delivered_to, 
    boss_site_user_id, boss_contact_id, primary_flag, monthly_payment, boss_location_code, 
    payment_term_name,sgsmart_account,sgsmart_user_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_enquiry_customer
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_enquiry_customer
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_enquiry_customer
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoExample" >
    delete from tb_enquiry_customer
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoPO" >
    insert into tb_enquiry_customer (id, enquiry_id, customer_id, 
      customer_group_id, customer_address_cn, customer_address_en, 
      customer_name_cn, customer_name_en, contact_person_email, 
      contact_person_fax, contact_person_phone1, 
      contact_person_name, contact_person_remark, 
      contact_person_phone2, customer_credit, customer_usage, 
      account_id, boss_number, contact_address_id, 
      buyer_group, buyer_group_name, supplier_no, 
      logo_cloud_id, active_indicator, created_by, 
      created_date, modified_by, modified_date, 
      organization_name, is_as_applicant, report_delivered_to, 
      failed_report_delivered_to, boss_site_user_id, 
      boss_contact_id, primary_flag, monthly_payment, 
      boss_location_code,
      payment_term_name,sgsmart_account,sgsmart_user_id)
    values (#{id,jdbcType=VARCHAR}, #{enquiryId,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, 
      #{customerGroupId,jdbcType=VARCHAR}, #{customerAddressCn,jdbcType=VARCHAR}, #{customerAddressEn,jdbcType=VARCHAR}, 
      #{customerNameCn,jdbcType=VARCHAR}, #{customerNameEn,jdbcType=VARCHAR}, #{contactPersonEmail,jdbcType=VARCHAR}, 
      #{contactPersonFax,jdbcType=VARCHAR}, #{contactPersonPhone1,jdbcType=VARCHAR}, 
      #{contactPersonName,jdbcType=VARCHAR}, #{contactPersonRemark,jdbcType=VARCHAR}, 
      #{contactPersonPhone2,jdbcType=VARCHAR}, #{customerCredit,jdbcType=VARCHAR}, #{customerUsage,jdbcType=VARCHAR}, 
      #{accountId,jdbcType=BIGINT}, #{bossNumber,jdbcType=BIGINT}, #{contactAddressId,jdbcType=VARCHAR}, 
      #{buyerGroup,jdbcType=VARCHAR}, #{buyerGroupName,jdbcType=VARCHAR}, #{supplierNo,jdbcType=VARCHAR}, 
      #{logoCloudId,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{organizationName,jdbcType=VARCHAR}, #{isAsApplicant,jdbcType=INTEGER}, #{reportDeliveredTo,jdbcType=VARCHAR}, 
      #{failedReportDeliveredTo,jdbcType=VARCHAR}, #{bossSiteUserId,jdbcType=BIGINT}, 
      #{bossContactId,jdbcType=BIGINT}, #{primaryFlag,jdbcType=VARCHAR}, #{monthlyPayment,jdbcType=VARCHAR}, 
      #{bossLocationCode,jdbcType=VARCHAR},
      #{paymentTermName,jdbcType=VARCHAR},#{sgsMartAccount,jdbcType=VARCHAR},#{sgsMartUserId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoPO" >
    insert into tb_enquiry_customer
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="enquiryId != null" >
        enquiry_id,
      </if>
      <if test="customerId != null" >
        customer_id,
      </if>
      <if test="customerGroupId != null" >
        customer_group_id,
      </if>
      <if test="customerAddressCn != null" >
        customer_address_cn,
      </if>
      <if test="customerAddressEn != null" >
        customer_address_en,
      </if>
      <if test="customerNameCn != null" >
        customer_name_cn,
      </if>
      <if test="customerNameEn != null" >
        customer_name_en,
      </if>
      <if test="contactPersonEmail != null" >
        contact_person_email,
      </if>
      <if test="contactPersonFax != null" >
        contact_person_fax,
      </if>
      <if test="contactPersonPhone1 != null" >
        contact_person_phone1,
      </if>
      <if test="contactPersonName != null" >
        contact_person_name,
      </if>
      <if test="contactPersonRemark != null" >
        contact_person_remark,
      </if>
      <if test="contactPersonPhone2 != null" >
        contact_person_phone2,
      </if>
      <if test="customerCredit != null" >
        customer_credit,
      </if>
      <if test="customerUsage != null" >
        customer_usage,
      </if>
      <if test="accountId != null" >
        account_id,
      </if>
      <if test="bossNumber != null" >
        boss_number,
      </if>
      <if test="contactAddressId != null" >
        contact_address_id,
      </if>
      <if test="buyerGroup != null" >
        buyer_group,
      </if>
      <if test="buyerGroupName != null" >
        buyer_group_name,
      </if>
      <if test="supplierNo != null" >
        supplier_no,
      </if>
      <if test="logoCloudId != null" >
        logo_cloud_id,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="organizationName != null" >
        organization_name,
      </if>
      <if test="isAsApplicant != null" >
        is_as_applicant,
      </if>
      <if test="reportDeliveredTo != null" >
        report_delivered_to,
      </if>
      <if test="failedReportDeliveredTo != null" >
        failed_report_delivered_to,
      </if>
      <if test="bossSiteUserId != null" >
        boss_site_user_id,
      </if>
      <if test="bossContactId != null" >
        boss_contact_id,
      </if>
      <if test="primaryFlag != null" >
        primary_flag,
      </if>
      <if test="monthlyPayment != null" >
        monthly_payment,
      </if>
      <if test="bossLocationCode != null" >
        boss_location_code,
      </if>
      <if test="paymentTermName != null" >
        payment_term_name,
      </if>
      <if test="sgsMartAccount != null" >
        sgsmart_account,
      </if>
      <if test="sgsMartUserId != null" >
        sgsmart_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="enquiryId != null" >
        #{enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null" >
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupId != null" >
        #{customerGroupId,jdbcType=VARCHAR},
      </if>
      <if test="customerAddressCn != null" >
        #{customerAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="customerAddressEn != null" >
        #{customerAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="customerNameCn != null" >
        #{customerNameCn,jdbcType=VARCHAR},
      </if>
      <if test="customerNameEn != null" >
        #{customerNameEn,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonEmail != null" >
        #{contactPersonEmail,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonFax != null" >
        #{contactPersonFax,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonPhone1 != null" >
        #{contactPersonPhone1,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonName != null" >
        #{contactPersonName,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonRemark != null" >
        #{contactPersonRemark,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonPhone2 != null" >
        #{contactPersonPhone2,jdbcType=VARCHAR},
      </if>
      <if test="customerCredit != null" >
        #{customerCredit,jdbcType=VARCHAR},
      </if>
      <if test="customerUsage != null" >
        #{customerUsage,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null" >
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="bossNumber != null" >
        #{bossNumber,jdbcType=BIGINT},
      </if>
      <if test="contactAddressId != null" >
        #{contactAddressId,jdbcType=VARCHAR},
      </if>
      <if test="buyerGroup != null" >
        #{buyerGroup,jdbcType=VARCHAR},
      </if>
      <if test="buyerGroupName != null" >
        #{buyerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null" >
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="logoCloudId != null" >
        #{logoCloudId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="organizationName != null" >
        #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="isAsApplicant != null" >
        #{isAsApplicant,jdbcType=INTEGER},
      </if>
      <if test="reportDeliveredTo != null" >
        #{reportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="failedReportDeliveredTo != null" >
        #{failedReportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="bossSiteUserId != null" >
        #{bossSiteUserId,jdbcType=BIGINT},
      </if>
      <if test="bossContactId != null" >
        #{bossContactId,jdbcType=BIGINT},
      </if>
      <if test="primaryFlag != null" >
        #{primaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="monthlyPayment != null" >
        #{monthlyPayment,jdbcType=VARCHAR},
      </if>
      <if test="bossLocationCode != null" >
        #{bossLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentTermName != null" >
        #{paymentTermName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_enquiry_customer
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_enquiry_customer
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryId != null" >
        enquiry_id = #{record.enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null" >
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupId != null" >
        customer_group_id = #{record.customerGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerAddressCn != null" >
        customer_address_cn = #{record.customerAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="record.customerAddressEn != null" >
        customer_address_en = #{record.customerAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="record.customerNameCn != null" >
        customer_name_cn = #{record.customerNameCn,jdbcType=VARCHAR},
      </if>
      <if test="record.customerNameEn != null" >
        customer_name_en = #{record.customerNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPersonEmail != null" >
        contact_person_email = #{record.contactPersonEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPersonFax != null" >
        contact_person_fax = #{record.contactPersonFax,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPersonPhone1 != null" >
        contact_person_phone1 = #{record.contactPersonPhone1,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPersonName != null" >
        contact_person_name = #{record.contactPersonName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPersonRemark != null" >
        contact_person_remark = #{record.contactPersonRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPersonPhone2 != null" >
        contact_person_phone2 = #{record.contactPersonPhone2,jdbcType=VARCHAR},
      </if>
      <if test="record.customerCredit != null" >
        customer_credit = #{record.customerCredit,jdbcType=VARCHAR},
      </if>
      <if test="record.customerUsage != null" >
        customer_usage = #{record.customerUsage,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null" >
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.bossNumber != null" >
        boss_number = #{record.bossNumber,jdbcType=BIGINT},
      </if>
      <if test="record.contactAddressId != null" >
        contact_address_id = #{record.contactAddressId,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerGroup != null" >
        buyer_group = #{record.buyerGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerGroupName != null" >
        buyer_group_name = #{record.buyerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null" >
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.logoCloudId != null" >
        logo_cloud_id = #{record.logoCloudId,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.organizationName != null" >
        organization_name = #{record.organizationName,jdbcType=VARCHAR},
      </if>
      <if test="record.isAsApplicant != null" >
        is_as_applicant = #{record.isAsApplicant,jdbcType=INTEGER},
      </if>
      <if test="record.reportDeliveredTo != null" >
        report_delivered_to = #{record.reportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="record.failedReportDeliveredTo != null" >
        failed_report_delivered_to = #{record.failedReportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="record.bossSiteUserId != null" >
        boss_site_user_id = #{record.bossSiteUserId,jdbcType=BIGINT},
      </if>
      <if test="record.bossContactId != null" >
        boss_contact_id = #{record.bossContactId,jdbcType=BIGINT},
      </if>
      <if test="record.primaryFlag != null" >
        primary_flag = #{record.primaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.monthlyPayment != null" >
        monthly_payment = #{record.monthlyPayment,jdbcType=VARCHAR},
      </if>
      <if test="record.bossLocationCode != null" >
        boss_location_code = #{record.bossLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentTermName != null" >
        payment_term_name = #{record.paymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="record.sgsMartAccount != null" >
        sgsmart_account = #{record.sgsMartAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.sgsMartUserId != null" >
        sgsmart_user_id = #{record.sgsMartUserId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_enquiry_customer
    set id = #{record.id,jdbcType=VARCHAR},
      enquiry_id = #{record.enquiryId,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      customer_group_id = #{record.customerGroupId,jdbcType=VARCHAR},
      customer_address_cn = #{record.customerAddressCn,jdbcType=VARCHAR},
      customer_address_en = #{record.customerAddressEn,jdbcType=VARCHAR},
      customer_name_cn = #{record.customerNameCn,jdbcType=VARCHAR},
      customer_name_en = #{record.customerNameEn,jdbcType=VARCHAR},
      contact_person_email = #{record.contactPersonEmail,jdbcType=VARCHAR},
      contact_person_fax = #{record.contactPersonFax,jdbcType=VARCHAR},
      contact_person_phone1 = #{record.contactPersonPhone1,jdbcType=VARCHAR},
      contact_person_name = #{record.contactPersonName,jdbcType=VARCHAR},
      contact_person_remark = #{record.contactPersonRemark,jdbcType=VARCHAR},
      contact_person_phone2 = #{record.contactPersonPhone2,jdbcType=VARCHAR},
      customer_credit = #{record.customerCredit,jdbcType=VARCHAR},
      customer_usage = #{record.customerUsage,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=BIGINT},
      boss_number = #{record.bossNumber,jdbcType=BIGINT},
      contact_address_id = #{record.contactAddressId,jdbcType=VARCHAR},
      buyer_group = #{record.buyerGroup,jdbcType=VARCHAR},
      buyer_group_name = #{record.buyerGroupName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      logo_cloud_id = #{record.logoCloudId,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      organization_name = #{record.organizationName,jdbcType=VARCHAR},
      is_as_applicant = #{record.isAsApplicant,jdbcType=INTEGER},
      report_delivered_to = #{record.reportDeliveredTo,jdbcType=VARCHAR},
      failed_report_delivered_to = #{record.failedReportDeliveredTo,jdbcType=VARCHAR},
      boss_site_user_id = #{record.bossSiteUserId,jdbcType=BIGINT},
      boss_contact_id = #{record.bossContactId,jdbcType=BIGINT},
      primary_flag = #{record.primaryFlag,jdbcType=VARCHAR},
      monthly_payment = #{record.monthlyPayment,jdbcType=VARCHAR},
      boss_location_code = #{record.bossLocationCode,jdbcType=VARCHAR},
      payment_term_name = #{record.paymentTermName,jdbcType=VARCHAR},
      sgsmart_account = #{record.sgsMartAccount,jdbcType=VARCHAR},
      sgsmart_user_id = #{record.sgsMartUserId,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoPO" >
    update tb_enquiry_customer
    <set >
      <if test="enquiryId != null" >
        enquiry_id = #{enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null" >
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupId != null" >
        customer_group_id = #{customerGroupId,jdbcType=VARCHAR},
      </if>
      <if test="customerAddressCn != null" >
        customer_address_cn = #{customerAddressCn,jdbcType=VARCHAR},
      </if>
      <if test="customerAddressEn != null" >
        customer_address_en = #{customerAddressEn,jdbcType=VARCHAR},
      </if>
      <if test="customerNameCn != null" >
        customer_name_cn = #{customerNameCn,jdbcType=VARCHAR},
      </if>
      <if test="customerNameEn != null" >
        customer_name_en = #{customerNameEn,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonEmail != null" >
        contact_person_email = #{contactPersonEmail,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonFax != null" >
        contact_person_fax = #{contactPersonFax,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonPhone1 != null" >
        contact_person_phone1 = #{contactPersonPhone1,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonName != null" >
        contact_person_name = #{contactPersonName,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonRemark != null" >
        contact_person_remark = #{contactPersonRemark,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonPhone2 != null" >
        contact_person_phone2 = #{contactPersonPhone2,jdbcType=VARCHAR},
      </if>
      <if test="customerCredit != null" >
        customer_credit = #{customerCredit,jdbcType=VARCHAR},
      </if>
      <if test="customerUsage != null" >
        customer_usage = #{customerUsage,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null" >
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="bossNumber != null" >
        boss_number = #{bossNumber,jdbcType=BIGINT},
      </if>
      <if test="contactAddressId != null" >
        contact_address_id = #{contactAddressId,jdbcType=VARCHAR},
      </if>
      <if test="buyerGroup != null" >
        buyer_group = #{buyerGroup,jdbcType=VARCHAR},
      </if>
      <if test="buyerGroupName != null" >
        buyer_group_name = #{buyerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null" >
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="logoCloudId != null" >
        logo_cloud_id = #{logoCloudId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="organizationName != null" >
        organization_name = #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="isAsApplicant != null" >
        is_as_applicant = #{isAsApplicant,jdbcType=INTEGER},
      </if>
      <if test="reportDeliveredTo != null" >
        report_delivered_to = #{reportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="failedReportDeliveredTo != null" >
        failed_report_delivered_to = #{failedReportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="bossSiteUserId != null" >
        boss_site_user_id = #{bossSiteUserId,jdbcType=BIGINT},
      </if>
      <if test="bossContactId != null" >
        boss_contact_id = #{bossContactId,jdbcType=BIGINT},
      </if>
      <if test="primaryFlag != null" >
        primary_flag = #{primaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="monthlyPayment != null" >
        monthly_payment = #{monthlyPayment,jdbcType=VARCHAR},
      </if>
      <if test="bossLocationCode != null" >
        boss_location_code = #{bossLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentTermName != null" >
        payment_term_name = #{paymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="sgsMartAccount != null" >
        sgsmart_account = #{sgsMartAccount,jdbcType=VARCHAR},
      </if>
      <if test="sgsMartUserId != null" >
        sgsmart_user_id = #{sgsMartUserId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCustomerInfoPO" >
    update tb_enquiry_customer
    set enquiry_id = #{enquiryId,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=VARCHAR},
      customer_group_id = #{customerGroupId,jdbcType=VARCHAR},
      customer_address_cn = #{customerAddressCn,jdbcType=VARCHAR},
      customer_address_en = #{customerAddressEn,jdbcType=VARCHAR},
      customer_name_cn = #{customerNameCn,jdbcType=VARCHAR},
      customer_name_en = #{customerNameEn,jdbcType=VARCHAR},
      contact_person_email = #{contactPersonEmail,jdbcType=VARCHAR},
      contact_person_fax = #{contactPersonFax,jdbcType=VARCHAR},
      contact_person_phone1 = #{contactPersonPhone1,jdbcType=VARCHAR},
      contact_person_name = #{contactPersonName,jdbcType=VARCHAR},
      contact_person_remark = #{contactPersonRemark,jdbcType=VARCHAR},
      contact_person_phone2 = #{contactPersonPhone2,jdbcType=VARCHAR},
      customer_credit = #{customerCredit,jdbcType=VARCHAR},
      customer_usage = #{customerUsage,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=BIGINT},
      boss_number = #{bossNumber,jdbcType=BIGINT},
      contact_address_id = #{contactAddressId,jdbcType=VARCHAR},
      buyer_group = #{buyerGroup,jdbcType=VARCHAR},
      buyer_group_name = #{buyerGroupName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      logo_cloud_id = #{logoCloudId,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      organization_name = #{organizationName,jdbcType=VARCHAR},
      is_as_applicant = #{isAsApplicant,jdbcType=INTEGER},
      report_delivered_to = #{reportDeliveredTo,jdbcType=VARCHAR},
      failed_report_delivered_to = #{failedReportDeliveredTo,jdbcType=VARCHAR},
      boss_site_user_id = #{bossSiteUserId,jdbcType=BIGINT},
      boss_contact_id = #{bossContactId,jdbcType=BIGINT},
      primary_flag = #{primaryFlag,jdbcType=VARCHAR},
      monthly_payment = #{monthlyPayment,jdbcType=VARCHAR},
      boss_location_code = #{bossLocationCode,jdbcType=VARCHAR},
      payment_term_name = #{paymentTermName,jdbcType=VARCHAR},
      sgsmart_account = #{sgsMartAccount,jdbcType=VARCHAR},
      sgsmart_user_id = #{sgsMartUserId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>