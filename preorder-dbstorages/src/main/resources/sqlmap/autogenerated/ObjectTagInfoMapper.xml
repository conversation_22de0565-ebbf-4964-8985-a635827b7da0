<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.ObjectTagInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="object" property="object" jdbcType="VARCHAR" />
    <result column="object_id" property="objectId" jdbcType="VARCHAR" />
    <result column="data" property="data" jdbcType="OTHER" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, `object`, object_id, `data`, active_indicator, created_by, created_date, modified_by, 
    modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_object_tags
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_object_tags
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_object_tags
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoExample" >
    delete from tb_object_tags
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoPO" >
    insert into tb_object_tags (id, `object`, object_id, 
      `data`, active_indicator, created_by, 
      created_date, modified_by, modified_date
      )
    values (#{id,jdbcType=VARCHAR}, #{object,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, 
      #{data,jdbcType=OTHER}, #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoPO" >
    insert into tb_object_tags
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="object != null" >
        `object`,
      </if>
      <if test="objectId != null" >
        object_id,
      </if>
      <if test="data != null" >
        `data`,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="object != null" >
        #{object,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="data != null" >
        #{data,jdbcType=OTHER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_object_tags
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_object_tags
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.object != null" >
        `object` = #{record.object,jdbcType=VARCHAR},
      </if>
      <if test="record.objectId != null" >
        object_id = #{record.objectId,jdbcType=VARCHAR},
      </if>
      <if test="record.data != null" >
        `data` = #{record.data,jdbcType=OTHER},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_object_tags
    set id = #{record.id,jdbcType=VARCHAR},
      `object` = #{record.object,jdbcType=VARCHAR},
      object_id = #{record.objectId,jdbcType=VARCHAR},
      `data` = #{record.data,jdbcType=OTHER},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoPO" >
    update tb_object_tags
    <set >
      <if test="object != null" >
        `object` = #{object,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        object_id = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="data != null" >
        `data` = #{data,jdbcType=OTHER},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectTagInfoPO" >
    update tb_object_tags
    set `object` = #{object,jdbcType=VARCHAR},
      object_id = #{objectId,jdbcType=VARCHAR},
      `data` = #{data,jdbcType=OTHER},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>