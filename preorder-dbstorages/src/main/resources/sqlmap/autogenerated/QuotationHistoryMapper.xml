<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.QuotationHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryPO" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="GeneralOrderID" property="generalorderid" jdbcType="VARCHAR" />
    <result column="VersionID" property="versionid" jdbcType="INTEGER" />
    <result column="ReportNos" property="reportnos" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeindicator" jdbcType="TINYINT" />
    <result column="CreatedBy" property="createdby" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createddate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedby" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifieddate" jdbcType="TIMESTAMP" />
    <result column="Forma_Invoice_Number" property="formaInvoiceNumber" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryPO" extends="BaseResultMap" >
    <result column="QuotationFormInfo" property="quotationforminfo" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, GeneralOrderID, VersionID, ReportNos, ActiveIndicator, CreatedBy, CreatedDate, 
    ModifiedBy, ModifiedDate, Forma_Invoice_Number
  </sql>
  <sql id="Blob_Column_List" >
    QuotationFormInfo
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_quotation_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_quotation_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_quotation_history
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_quotation_history
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryExample" >
    delete from tb_quotation_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryPO" >
    insert into tb_quotation_history (ID, GeneralOrderID, VersionID, 
      ReportNos, ActiveIndicator, CreatedBy, 
      CreatedDate, ModifiedBy, ModifiedDate, 
      Forma_Invoice_Number,
      QuotationFormInfo)
    values (#{id,jdbcType=VARCHAR}, #{generalorderid,jdbcType=VARCHAR}, #{versionid,jdbcType=INTEGER}, 
      #{reportnos,jdbcType=VARCHAR}, #{activeindicator,jdbcType=TINYINT}, #{createdby,jdbcType=VARCHAR}, 
      #{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, #{modifieddate,jdbcType=TIMESTAMP}, 
      #{formaInvoiceNumber,jdbcType=VARCHAR},
      #{quotationforminfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryPO" >
    insert into tb_quotation_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="generalorderid != null" >
        GeneralOrderID,
      </if>
      <if test="versionid != null" >
        VersionID,
      </if>
      <if test="reportnos != null" >
        ReportNos,
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdby != null" >
        CreatedBy,
      </if>
      <if test="createddate != null" >
        CreatedDate,
      </if>
      <if test="modifiedby != null" >
        ModifiedBy,
      </if>
      <if test="modifieddate != null" >
        ModifiedDate,
      </if>
      <if test="formaInvoiceNumber != null" >
        Forma_Invoice_Number,
      </if>
      <if test="quotationforminfo != null" >
        QuotationFormInfo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="generalorderid != null" >
        #{generalorderid,jdbcType=VARCHAR},
      </if>
      <if test="versionid != null" >
        #{versionid,jdbcType=INTEGER},
      </if>
      <if test="reportnos != null" >
        #{reportnos,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        #{activeindicator,jdbcType=TINYINT},
      </if>
      <if test="createdby != null" >
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="formaInvoiceNumber != null" >
        #{formaInvoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="quotationforminfo != null" >
        #{quotationforminfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryExample" resultType="java.lang.Integer" >
    select count(*) from tb_quotation_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_quotation_history
    <set >
      <if test="record.id != null" >
        ID = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.generalorderid != null" >
        GeneralOrderID = #{record.generalorderid,jdbcType=VARCHAR},
      </if>
      <if test="record.versionid != null" >
        VersionID = #{record.versionid,jdbcType=INTEGER},
      </if>
      <if test="record.reportnos != null" >
        ReportNos = #{record.reportnos,jdbcType=VARCHAR},
      </if>
      <if test="record.activeindicator != null" >
        ActiveIndicator = #{record.activeindicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdby != null" >
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.createddate != null" >
        CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="record.modifieddate != null" >
        ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.formaInvoiceNumber != null" >
        Forma_Invoice_Number = #{record.formaInvoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.quotationforminfo != null" >
        QuotationFormInfo = #{record.quotationforminfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_quotation_history
    set ID = #{record.id,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalorderid,jdbcType=VARCHAR},
      VersionID = #{record.versionid,jdbcType=INTEGER},
      ReportNos = #{record.reportnos,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeindicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      Forma_Invoice_Number = #{record.formaInvoiceNumber,jdbcType=VARCHAR},
      QuotationFormInfo = #{record.quotationforminfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_quotation_history
    set ID = #{record.id,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalorderid,jdbcType=VARCHAR},
      VersionID = #{record.versionid,jdbcType=INTEGER},
      ReportNos = #{record.reportnos,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeindicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      Forma_Invoice_Number = #{record.formaInvoiceNumber,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryPO" >
    update tb_quotation_history
    <set >
      <if test="generalorderid != null" >
        GeneralOrderID = #{generalorderid,jdbcType=VARCHAR},
      </if>
      <if test="versionid != null" >
        VersionID = #{versionid,jdbcType=INTEGER},
      </if>
      <if test="reportnos != null" >
        ReportNos = #{reportnos,jdbcType=VARCHAR},
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator = #{activeindicator,jdbcType=TINYINT},
      </if>
      <if test="createdby != null" >
        CreatedBy = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="formaInvoiceNumber != null" >
        Forma_Invoice_Number = #{formaInvoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="quotationforminfo != null" >
        QuotationFormInfo = #{quotationforminfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryPO" >
    update tb_quotation_history
    set GeneralOrderID = #{generalorderid,jdbcType=VARCHAR},
      VersionID = #{versionid,jdbcType=INTEGER},
      ReportNos = #{reportnos,jdbcType=VARCHAR},
      ActiveIndicator = #{activeindicator,jdbcType=TINYINT},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      Forma_Invoice_Number = #{formaInvoiceNumber,jdbcType=VARCHAR},
      QuotationFormInfo = #{quotationforminfo,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.QuotationHistoryPO" >
    update tb_quotation_history
    set GeneralOrderID = #{generalorderid,jdbcType=VARCHAR},
      VersionID = #{versionid,jdbcType=INTEGER},
      ReportNos = #{reportnos,jdbcType=VARCHAR},
      ActiveIndicator = #{activeindicator,jdbcType=TINYINT},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      Forma_Invoice_Number = #{formaInvoiceNumber,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>