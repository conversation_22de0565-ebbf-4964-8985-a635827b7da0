<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.NotifyMessageMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessagePO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="request_id" property="requestId" jdbcType="VARCHAR" />
    <result column="object_no" property="objectNo" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="to_system_id" property="toSystemId" jdbcType="VARCHAR" />
    <result column="notify_method" property="notifyMethod" jdbcType="VARCHAR" />
    <result column="notify_params" property="notifyParams" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="BIT" />
    <result column="retry_count" property="retryCount" jdbcType="INTEGER" />
    <result column="notify_time" property="notifyTime" jdbcType="TIMESTAMP" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageWithBLOBs" extends="BaseResultMap" >
    <result column="request_body" property="requestBody" jdbcType="LONGVARCHAR" />
    <result column="response_body" property="responseBody" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, request_id, object_no, `status`, to_system_id, notify_method, notify_params, 
    active_indicator, retry_count, notify_time, created_date, modified_date
  </sql>
  <sql id="Blob_Column_List" >
    request_body, response_body
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_notify_message
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_notify_message
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_notify_message
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_notify_message
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageExample" >
    delete from tb_notify_message
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageWithBLOBs" >
    insert into tb_notify_message (id, request_id, object_no, 
      `status`, to_system_id, notify_method, 
      notify_params, active_indicator, retry_count, 
      notify_time, created_date, modified_date, 
      request_body, response_body)
    values (#{id,jdbcType=BIGINT}, #{requestId,jdbcType=VARCHAR}, #{objectNo,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{toSystemId,jdbcType=VARCHAR}, #{notifyMethod,jdbcType=VARCHAR}, 
      #{notifyParams,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=BIT}, #{retryCount,jdbcType=INTEGER},
      #{notifyTime,jdbcType=TIMESTAMP}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{requestBody,jdbcType=LONGVARCHAR}, #{responseBody,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageWithBLOBs" >
    insert into tb_notify_message
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="requestId != null" >
        request_id,
      </if>
      <if test="objectNo != null" >
        object_no,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="toSystemId != null" >
        to_system_id,
      </if>
      <if test="notifyMethod != null" >
        notify_method,
      </if>
      <if test="notifyParams != null" >
        notify_params,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="retryCount != null" >
        retry_count,
      </if>
      <if test="notifyTime != null" >
        notify_time,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="requestBody != null" >
        request_body,
      </if>
      <if test="responseBody != null" >
        response_body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="requestId != null" >
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="toSystemId != null" >
        #{toSystemId,jdbcType=VARCHAR},
      </if>
      <if test="notifyMethod != null" >
        #{notifyMethod,jdbcType=VARCHAR},
      </if>
      <if test="notifyParams != null" >
        #{notifyParams,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="retryCount != null" >
        #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="notifyTime != null" >
        #{notifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="requestBody != null" >
        #{requestBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseBody != null" >
        #{responseBody,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageExample" resultType="java.lang.Integer" >
    select count(*) from tb_notify_message
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_notify_message
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.requestId != null" >
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.objectNo != null" >
        object_no = #{record.objectNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.toSystemId != null" >
        to_system_id = #{record.toSystemId,jdbcType=VARCHAR},
      </if>
      <if test="record.notifyMethod != null" >
        notify_method = #{record.notifyMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.notifyParams != null" >
        notify_params = #{record.notifyParams,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.retryCount != null" >
        retry_count = #{record.retryCount,jdbcType=INTEGER},
      </if>
      <if test="record.notifyTime != null" >
        notify_time = #{record.notifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.requestBody != null" >
        request_body = #{record.requestBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.responseBody != null" >
        response_body = #{record.responseBody,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_notify_message
    set id = #{record.id,jdbcType=BIGINT},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      object_no = #{record.objectNo,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      to_system_id = #{record.toSystemId,jdbcType=VARCHAR},
      notify_method = #{record.notifyMethod,jdbcType=VARCHAR},
      notify_params = #{record.notifyParams,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=BIT},
      retry_count = #{record.retryCount,jdbcType=INTEGER},
      notify_time = #{record.notifyTime,jdbcType=TIMESTAMP},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      request_body = #{record.requestBody,jdbcType=LONGVARCHAR},
      response_body = #{record.responseBody,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_notify_message
    set id = #{record.id,jdbcType=BIGINT},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      object_no = #{record.objectNo,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      to_system_id = #{record.toSystemId,jdbcType=VARCHAR},
      notify_method = #{record.notifyMethod,jdbcType=VARCHAR},
      notify_params = #{record.notifyParams,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=BIT},
      retry_count = #{record.retryCount,jdbcType=INTEGER},
      notify_time = #{record.notifyTime,jdbcType=TIMESTAMP},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageWithBLOBs" >
    update tb_notify_message
    <set >
      <if test="requestId != null" >
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        object_no = #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="toSystemId != null" >
        to_system_id = #{toSystemId,jdbcType=VARCHAR},
      </if>
      <if test="notifyMethod != null" >
        notify_method = #{notifyMethod,jdbcType=VARCHAR},
      </if>
      <if test="notifyParams != null" >
        notify_params = #{notifyParams,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="retryCount != null" >
        retry_count = #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="notifyTime != null" >
        notify_time = #{notifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="requestBody != null" >
        request_body = #{requestBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseBody != null" >
        response_body = #{responseBody,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessageWithBLOBs" >
    update tb_notify_message
    set request_id = #{requestId,jdbcType=VARCHAR},
      object_no = #{objectNo,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      to_system_id = #{toSystemId,jdbcType=VARCHAR},
      notify_method = #{notifyMethod,jdbcType=VARCHAR},
      notify_params = #{notifyParams,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=BIT},
      retry_count = #{retryCount,jdbcType=INTEGER},
      notify_time = #{notifyTime,jdbcType=TIMESTAMP},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      request_body = #{requestBody,jdbcType=LONGVARCHAR},
      response_body = #{responseBody,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.NotifyMessagePO" >
    update tb_notify_message
    set request_id = #{requestId,jdbcType=VARCHAR},
      object_no = #{objectNo,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      to_system_id = #{toSystemId,jdbcType=VARCHAR},
      notify_method = #{notifyMethod,jdbcType=VARCHAR},
      notify_params = #{notifyParams,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=BIT},
      retry_count = #{retryCount,jdbcType=INTEGER},
      notify_time = #{notifyTime,jdbcType=TIMESTAMP},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>