<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EventNotifyConfigMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="system_id" property="systemId" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="VARCHAR" />
    <result column="event_type" property="eventType" jdbcType="VARCHAR" />
    <result column="to_system_id" property="toSystemId" jdbcType="VARCHAR" />
    <result column="notify_method" property="notifyMethod" jdbcType="VARCHAR" />
    <result column="notify_params" property="notifyParams" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, system_id, object_type, event_type, to_system_id, notify_method, notify_params, 
    active_indicator
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_event_notify_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_event_notify_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_event_notify_config
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigExample" >
    delete from tb_event_notify_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigPO" >
    insert into tb_event_notify_config (id, system_id, object_type, 
      event_type, to_system_id, notify_method, 
      notify_params, active_indicator)
    values (#{id,jdbcType=VARCHAR}, #{systemId,jdbcType=VARCHAR}, #{objectType,jdbcType=VARCHAR}, 
      #{eventType,jdbcType=VARCHAR}, #{toSystemId,jdbcType=VARCHAR}, #{notifyMethod,jdbcType=VARCHAR}, 
      #{notifyParams,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigPO" >
    insert into tb_event_notify_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="objectType != null" >
        object_type,
      </if>
      <if test="eventType != null" >
        event_type,
      </if>
      <if test="toSystemId != null" >
        to_system_id,
      </if>
      <if test="notifyMethod != null" >
        notify_method,
      </if>
      <if test="notifyParams != null" >
        notify_params,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null" >
        #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="toSystemId != null" >
        #{toSystemId,jdbcType=VARCHAR},
      </if>
      <if test="notifyMethod != null" >
        #{notifyMethod,jdbcType=VARCHAR},
      </if>
      <if test="notifyParams != null" >
        #{notifyParams,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigExample" resultType="java.lang.Integer" >
    select count(*) from tb_event_notify_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_event_notify_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=VARCHAR},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=VARCHAR},
      </if>
      <if test="record.eventType != null" >
        event_type = #{record.eventType,jdbcType=VARCHAR},
      </if>
      <if test="record.toSystemId != null" >
        to_system_id = #{record.toSystemId,jdbcType=VARCHAR},
      </if>
      <if test="record.notifyMethod != null" >
        notify_method = #{record.notifyMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.notifyParams != null" >
        notify_params = #{record.notifyParams,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_event_notify_config
    set id = #{record.id,jdbcType=VARCHAR},
      system_id = #{record.systemId,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      event_type = #{record.eventType,jdbcType=VARCHAR},
      to_system_id = #{record.toSystemId,jdbcType=VARCHAR},
      notify_method = #{record.notifyMethod,jdbcType=VARCHAR},
      notify_params = #{record.notifyParams,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigPO" >
    update tb_event_notify_config
    <set >
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null" >
        event_type = #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="toSystemId != null" >
        to_system_id = #{toSystemId,jdbcType=VARCHAR},
      </if>
      <if test="notifyMethod != null" >
        notify_method = #{notifyMethod,jdbcType=VARCHAR},
      </if>
      <if test="notifyParams != null" >
        notify_params = #{notifyParams,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EventNotifyConfigPO" >
    update tb_event_notify_config
    set system_id = #{systemId,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      event_type = #{eventType,jdbcType=VARCHAR},
      to_system_id = #{toSystemId,jdbcType=VARCHAR},
      notify_method = #{notifyMethod,jdbcType=VARCHAR},
      notify_params = #{notifyParams,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>