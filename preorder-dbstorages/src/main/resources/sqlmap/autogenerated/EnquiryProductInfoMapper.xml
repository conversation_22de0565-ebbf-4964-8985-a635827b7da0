<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EnquiryProductInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="enquiry_id" property="enquiryId" jdbcType="VARCHAR" />
    <result column="product_library_id" property="productLibraryId" jdbcType="VARCHAR" />
    <result column="careLabel_instance_id" property="carelabelInstanceId" jdbcType="VARCHAR" />
    <result column="communication_log_id" property="communicationLogId" jdbcType="VARCHAR" />
    <result column="buyer_organnization1" property="buyerOrgannization1" jdbcType="VARCHAR" />
    <result column="buyer_organnization2" property="buyerOrgannization2" jdbcType="VARCHAR" />
    <result column="buyer_organnization3" property="buyerOrgannization3" jdbcType="VARCHAR" />
    <result column="buyer_organnization_code1" property="buyerOrgannizationCode1" jdbcType="VARCHAR" />
    <result column="buyer_organnization_code2" property="buyerOrgannizationCode2" jdbcType="VARCHAR" />
    <result column="buyer_organnization_code3" property="buyerOrgannizationCode3" jdbcType="VARCHAR" />
    <result column="buyer_aliase" property="buyerAliase" jdbcType="VARCHAR" />
    <result column="buyer_sourcing_office" property="buyerSourcingOffice" jdbcType="VARCHAR" />
    <result column="country_of_origin" property="countryOfOrigin" jdbcType="VARCHAR" />
    <result column="country_of_destination" property="countryOfDestination" jdbcType="VARCHAR" />
    <result column="dff_form_id" property="dffFormID" jdbcType="VARCHAR" />
    <result column="supplier" property="supplier" jdbcType="VARCHAR" />
    <result column="supplier_no" property="supplierNo" jdbcType="VARCHAR" />
    <result column="factory_id" property="factoryId" jdbcType="VARCHAR" />
    <result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
    <result column="first_fpu_no" property="firstFpuNo" jdbcType="VARCHAR" />
    <result column="first_pass_fpu_no" property="firstPassFpuNo" jdbcType="VARCHAR" />
    <result column="first_time_application_flag" property="firstTimeApplicationFlag" jdbcType="VARCHAR" />
    <result column="fpu_no" property="fpuNo" jdbcType="VARCHAR" />
    <result column="fpu_report_no" property="fpuReportNo" jdbcType="VARCHAR" />
    <result column="gpu_no" property="gpuNo" jdbcType="VARCHAR" />
    <result column="lot_no" property="lotNo" jdbcType="VARCHAR" />
    <result column="no_of_sample" property="noOfSample" jdbcType="INTEGER" />
    <result column="other_sample_information" property="otherSampleInformation" jdbcType="VARCHAR" />
    <result column="peformance_code" property="peformanceCode" jdbcType="VARCHAR" />
    <result column="po_no" property="poNo" jdbcType="VARCHAR" />
    <result column="previous_report_no" property="previousReportNo" jdbcType="VARCHAR" />
    <result column="trim_report_no" property="trimReportNo" jdbcType="VARCHAR" />
    <result column="fabric_report" property="fabricReport" jdbcType="VARCHAR" />
    <result column="product_category1" property="productCategory1" jdbcType="VARCHAR" />
    <result column="product_category2" property="productCategory2" jdbcType="VARCHAR" />
    <result column="style_no" property="styleNo" jdbcType="VARCHAR" />
    <result column="ref_code1" property="refCode1" jdbcType="VARCHAR" />
    <result column="ref_code2" property="refCode2" jdbcType="VARCHAR" />
    <result column="ref_code3" property="refCode3" jdbcType="VARCHAR" />
    <result column="ref_code4" property="refCode4" jdbcType="VARCHAR" />
    <result column="ref_code5" property="refCode5" jdbcType="VARCHAR" />
    <result column="product_color" property="productColor" jdbcType="VARCHAR" />
    <result column="product_description" property="productDescription" jdbcType="VARCHAR" />
    <result column="production_stage" property="productionStage" jdbcType="VARCHAR" />
    <result column="sample_id" property="sampleId" jdbcType="VARCHAR" />
    <result column="sample_received_date" property="sampleReceivedDate" jdbcType="TIMESTAMP" />
    <result column="age_group" property="ageGroup" jdbcType="VARCHAR" />
    <result column="end_use1" property="endUse1" jdbcType="VARCHAR" />
    <result column="special_product_attribute1" property="specialProductAttribute1" jdbcType="VARCHAR" />
    <result column="special_product_attribute2" property="specialProductAttribute2" jdbcType="VARCHAR" />
    <result column="special_product_attribute3" property="specialProductAttribute3" jdbcType="VARCHAR" />
    <result column="construction" property="construction" jdbcType="VARCHAR" />
    <result column="yarn_count" property="yarnCount" jdbcType="VARCHAR" />
    <result column="thread_count" property="threadCount" jdbcType="VARCHAR" />
    <result column="fiber_composition" property="fiberComposition" jdbcType="VARCHAR" />
    <result column="fiber_weight" property="fiberWeight" jdbcType="VARCHAR" />
    <result column="fabric_width" property="fabricWidth" jdbcType="VARCHAR" />
    <result column="season" property="season" jdbcType="VARCHAR" />
    <result column="size" property="size" jdbcType="VARCHAR" />
    <result column="special_finishing" property="specialFinishing" jdbcType="VARCHAR" />
    <result column="collection" property="collection" jdbcType="VARCHAR" />
    <result column="care_label_flag" property="careLabelFlag" jdbcType="VARCHAR" />
    <result column="care_label" property="careLabel" jdbcType="VARCHAR" />
    <result column="care_label_wording" property="careLabelWording" jdbcType="VARCHAR" />
    <result column="header_id" property="headerId" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="VARCHAR" />
    <result column="product_item_no" property="productItemNo" jdbcType="VARCHAR" />
    <result column="cancel_flag" property="cancelFlag" jdbcType="TINYINT" />
    <result column="ref_sample_id" property="refSampleId" jdbcType="VARCHAR" />
    <result column="language_id" property="languageID" jdbcType="INTEGER" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ax_sample_flag" property="axSampleFlag" jdbcType="INTEGER" />
    <result column="external_sample_id" property="externalSampleId" jdbcType="VARCHAR" />

  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoWithBLOBs" extends="BaseResultMap" >
    <result column="ref_code6" property="refCode6" jdbcType="LONGVARCHAR" />
    <result column="ref_code7" property="refCode7" jdbcType="LONGVARCHAR" />
    <result column="ref_code8" property="refCode8" jdbcType="LONGVARCHAR" />
    <result column="ref_code9" property="refCode9" jdbcType="LONGVARCHAR" />
    <result column="ref_code10" property="refCode10" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute1" property="specialCustomerAttribute1" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute2" property="specialCustomerAttribute2" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute3" property="specialCustomerAttribute3" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute4" property="specialCustomerAttribute4" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute5" property="specialCustomerAttribute5" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute6" property="specialCustomerAttribute6" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute7" property="specialCustomerAttribute7" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute8" property="specialCustomerAttribute8" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute9" property="specialCustomerAttribute9" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute10" property="specialCustomerAttribute10" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute11" property="specialCustomerAttribute11" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute12" property="specialCustomerAttribute12" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute13" property="specialCustomerAttribute13" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute14" property="specialCustomerAttribute14" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute15" property="specialCustomerAttribute15" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute16" property="specialCustomerAttribute16" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute17" property="specialCustomerAttribute17" jdbcType="LONGVARCHAR" />
    <result column="special_customer_attribute18" property="specialCustomerAttribute18" jdbcType="LONGVARCHAR" />
    <result column="special_product_attribute4" property="specialProductAttribute4" jdbcType="LONGVARCHAR" />
    <result column="special_product_attribute5" property="specialProductAttribute5" jdbcType="LONGVARCHAR" />
    <result column="special_product_attribute6" property="specialProductAttribute6" jdbcType="LONGVARCHAR" />
    <result column="special_product_attribute7" property="specialProductAttribute7" jdbcType="LONGVARCHAR" />
    <result column="special_product_attribute8" property="specialProductAttribute8" jdbcType="LONGVARCHAR" />
    <result column="special_product_attribute9" property="specialProductAttribute9" jdbcType="LONGVARCHAR" />
    <result column="special_product_attribute10" property="specialProductAttribute10" jdbcType="LONGVARCHAR" />
    <result column="Item_No" property="itemNo" jdbcType="LONGVARCHAR" />
    <result column="Vendor_No" property="vendorNo" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, enquiry_id, product_library_id, careLabel_instance_id, communication_log_id, 
    buyer_organnization1, buyer_organnization2, buyer_organnization3, buyer_organnization_code1, 
    buyer_organnization_code2, buyer_organnization_code3, buyer_aliase, buyer_sourcing_office, 
    country_of_origin, country_of_destination, dff_form_id, supplier, supplier_no, factory_id, 
    factory_name, first_fpu_no, first_pass_fpu_no, first_time_application_flag, fpu_no, 
    fpu_report_no, gpu_no, lot_no, no_of_sample, other_sample_information, peformance_code, 
    po_no, previous_report_no, trim_report_no, fabric_report, product_category1, product_category2, 
    style_no, ref_code1, ref_code2, ref_code3, ref_code4, ref_code5, product_color, product_description, 
    production_stage, sample_id, sample_received_date, age_group, end_use1, special_product_attribute1, 
    special_product_attribute2, special_product_attribute3, construction, yarn_count, 
    thread_count, fiber_composition, fiber_weight, fabric_width, season, `size`, special_finishing, 
    `collection`, care_label_flag, care_label, care_label_wording, header_id, product_type, 
    product_item_no, cancel_flag, ref_sample_id, language_id,
    created_date, modified_by, active_indicator, created_by, modified_date,ax_sample_flag,external_sample_id
  </sql>
  <sql id="Blob_Column_List" >
    ref_code6, ref_code7, ref_code8, ref_code9, ref_code10, special_customer_attribute1, 
    special_customer_attribute2, special_customer_attribute3, special_customer_attribute4, 
    special_customer_attribute5, special_customer_attribute6, special_customer_attribute7, 
    special_customer_attribute8, special_customer_attribute9, special_customer_attribute10, 
    special_customer_attribute11, special_customer_attribute12, special_customer_attribute13, 
    special_customer_attribute14, special_customer_attribute15, special_customer_attribute16, 
    special_customer_attribute17, special_customer_attribute18, special_product_attribute4, 
    special_product_attribute5, special_product_attribute6, special_product_attribute7, 
    special_product_attribute8, special_product_attribute9, special_product_attribute10, 
    Item_No, Vendor_No
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_enquiry_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_enquiry_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_enquiry_product
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_enquiry_product
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoExample" >
    delete from tb_enquiry_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoWithBLOBs" >
    insert into tb_enquiry_product (id, enquiry_id, product_library_id, 
      careLabel_instance_id, communication_log_id, 
      buyer_organnization1, buyer_organnization2, 
      buyer_organnization3, buyer_organnization_code1, 
      buyer_organnization_code2, buyer_organnization_code3, 
      buyer_aliase, buyer_sourcing_office, country_of_origin, 
      country_of_destination, dff_form_id, supplier, 
      supplier_no, factory_id, factory_name, 
      first_fpu_no, first_pass_fpu_no, first_time_application_flag, 
      fpu_no, fpu_report_no, gpu_no, 
      lot_no, no_of_sample, other_sample_information, 
      peformance_code, po_no, previous_report_no, 
      trim_report_no, fabric_report, product_category1, 
      product_category2, style_no, ref_code1, 
      ref_code2, ref_code3, ref_code4, 
      ref_code5, product_color, product_description, 
      production_stage, sample_id, sample_received_date, 
      age_group, end_use1, special_product_attribute1, 
      special_product_attribute2, special_product_attribute3, 
      construction, yarn_count, thread_count, 
      fiber_composition, fiber_weight, fabric_width, 
      season, `size`, special_finishing, 
      `collection`, care_label_flag, care_label, 
      care_label_wording, header_id, product_type, 
      product_item_no, cancel_flag,
      ref_sample_id, language_id, created_date, 
      modified_by, active_indicator, created_by, 
      modified_date, ref_code6, ref_code7, 
      ref_code8, ref_code9, ref_code10, 
      special_customer_attribute1, special_customer_attribute2, 
      special_customer_attribute3, special_customer_attribute4, 
      special_customer_attribute5, special_customer_attribute6, 
      special_customer_attribute7, special_customer_attribute8, 
      special_customer_attribute9, special_customer_attribute10, 
      special_customer_attribute11, special_customer_attribute12, 
      special_customer_attribute13, special_customer_attribute14, 
      special_customer_attribute15, special_customer_attribute16, 
      special_customer_attribute17, special_customer_attribute18, 
      special_product_attribute4, special_product_attribute5, 
      special_product_attribute6, special_product_attribute7, 
      special_product_attribute8, special_product_attribute9, 
      special_product_attribute10, Item_No, 
      Vendor_No,ax_sample_flag,external_sample_id)
    values (#{id,jdbcType=VARCHAR}, #{enquiryId,jdbcType=VARCHAR}, #{productLibraryId,jdbcType=VARCHAR}, 
      #{carelabelInstanceId,jdbcType=VARCHAR}, #{communicationLogId,jdbcType=VARCHAR}, 
      #{buyerOrgannization1,jdbcType=VARCHAR}, #{buyerOrgannization2,jdbcType=VARCHAR}, 
      #{buyerOrgannization3,jdbcType=VARCHAR}, #{buyerOrgannizationCode1,jdbcType=VARCHAR}, 
      #{buyerOrgannizationCode2,jdbcType=VARCHAR}, #{buyerOrgannizationCode3,jdbcType=VARCHAR}, 
      #{buyerAliase,jdbcType=VARCHAR}, #{buyerSourcingOffice,jdbcType=VARCHAR}, #{countryOfOrigin,jdbcType=VARCHAR}, 
      #{countryOfDestination,jdbcType=VARCHAR}, #{dffFormID,jdbcType=VARCHAR}, #{supplier,jdbcType=VARCHAR},
      #{supplierNo,jdbcType=VARCHAR}, #{factoryId,jdbcType=VARCHAR}, #{factoryName,jdbcType=VARCHAR}, 
      #{firstFpuNo,jdbcType=VARCHAR}, #{firstPassFpuNo,jdbcType=VARCHAR}, #{firstTimeApplicationFlag,jdbcType=VARCHAR},
      #{fpuNo,jdbcType=VARCHAR}, #{fpuReportNo,jdbcType=VARCHAR}, #{gpuNo,jdbcType=VARCHAR}, 
      #{lotNo,jdbcType=VARCHAR}, #{noOfSample,jdbcType=INTEGER}, #{otherSampleInformation,jdbcType=VARCHAR}, 
      #{peformanceCode,jdbcType=VARCHAR}, #{poNo,jdbcType=VARCHAR}, #{previousReportNo,jdbcType=VARCHAR}, 
      #{trimReportNo,jdbcType=VARCHAR}, #{fabricReport,jdbcType=VARCHAR}, #{productCategory1,jdbcType=VARCHAR}, 
      #{productCategory2,jdbcType=VARCHAR}, #{styleNo,jdbcType=VARCHAR}, #{refCode1,jdbcType=VARCHAR}, 
      #{refCode2,jdbcType=VARCHAR}, #{refCode3,jdbcType=VARCHAR}, #{refCode4,jdbcType=VARCHAR}, 
      #{refCode5,jdbcType=VARCHAR}, #{productColor,jdbcType=VARCHAR}, #{productDescription,jdbcType=VARCHAR}, 
      #{productionStage,jdbcType=VARCHAR}, #{sampleId,jdbcType=VARCHAR}, #{sampleReceivedDate,jdbcType=TIMESTAMP}, 
      #{ageGroup,jdbcType=VARCHAR}, #{endUse1,jdbcType=VARCHAR}, #{specialProductAttribute1,jdbcType=VARCHAR}, 
      #{specialProductAttribute2,jdbcType=VARCHAR}, #{specialProductAttribute3,jdbcType=VARCHAR}, 
      #{construction,jdbcType=VARCHAR}, #{yarnCount,jdbcType=VARCHAR}, #{threadCount,jdbcType=VARCHAR}, 
      #{fiberComposition,jdbcType=VARCHAR}, #{fiberWeight,jdbcType=VARCHAR}, #{fabricWidth,jdbcType=VARCHAR}, 
      #{season,jdbcType=VARCHAR}, #{size,jdbcType=VARCHAR}, #{specialFinishing,jdbcType=VARCHAR}, 
      #{collection,jdbcType=VARCHAR}, #{careLabelFlag,jdbcType=VARCHAR}, #{careLabel,jdbcType=VARCHAR}, 
      #{careLabelWording,jdbcType=VARCHAR}, #{headerId,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, 
      #{productItemNo,jdbcType=VARCHAR}, #{cancelFlag,jdbcType=TINYINT},
      #{refSampleId,jdbcType=VARCHAR}, #{languageID,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{refCode6,jdbcType=LONGVARCHAR}, #{refCode7,jdbcType=LONGVARCHAR}, 
      #{refCode8,jdbcType=LONGVARCHAR}, #{refCode9,jdbcType=LONGVARCHAR}, #{refCode10,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute1,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute2,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute3,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute4,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute5,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute6,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute7,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute8,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute9,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute10,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute11,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute12,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute13,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute14,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute15,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute16,jdbcType=LONGVARCHAR}, 
      #{specialCustomerAttribute17,jdbcType=LONGVARCHAR}, #{specialCustomerAttribute18,jdbcType=LONGVARCHAR}, 
      #{specialProductAttribute4,jdbcType=LONGVARCHAR}, #{specialProductAttribute5,jdbcType=LONGVARCHAR}, 
      #{specialProductAttribute6,jdbcType=LONGVARCHAR}, #{specialProductAttribute7,jdbcType=LONGVARCHAR}, 
      #{specialProductAttribute8,jdbcType=LONGVARCHAR}, #{specialProductAttribute9,jdbcType=LONGVARCHAR}, 
      #{specialProductAttribute10,jdbcType=LONGVARCHAR}, #{itemNo,jdbcType=LONGVARCHAR}, 
      #{vendorNo,jdbcType=LONGVARCHAR},#{axSampleFlag,jdbcType=INTEGER},#{externalSampleId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoWithBLOBs" >
    insert into tb_enquiry_product
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="enquiryId != null" >
        enquiry_id,
      </if>
      <if test="productLibraryId != null" >
        product_library_id,
      </if>
      <if test="carelabelInstanceId != null" >
        careLabel_instance_id,
      </if>
      <if test="communicationLogId != null" >
        communication_log_id,
      </if>
      <if test="buyerOrgannization1 != null" >
        buyer_organnization1,
      </if>
      <if test="buyerOrgannization2 != null" >
        buyer_organnization2,
      </if>
      <if test="buyerOrgannization3 != null" >
        buyer_organnization3,
      </if>
      <if test="buyerOrgannizationCode1 != null" >
        buyer_organnization_code1,
      </if>
      <if test="buyerOrgannizationCode2 != null" >
        buyer_organnization_code2,
      </if>
      <if test="buyerOrgannizationCode3 != null" >
        buyer_organnization_code3,
      </if>
      <if test="buyerAliase != null" >
        buyer_aliase,
      </if>
      <if test="buyerSourcingOffice != null" >
        buyer_sourcing_office,
      </if>
      <if test="countryOfOrigin != null" >
        country_of_origin,
      </if>
      <if test="countryOfDestination != null" >
        country_of_destination,
      </if>
      <if test="dffFormID != null" >
        dff_form_id,
      </if>
      <if test="supplier != null" >
        supplier,
      </if>
      <if test="supplierNo != null" >
        supplier_no,
      </if>
      <if test="factoryId != null" >
        factory_id,
      </if>
      <if test="factoryName != null" >
        factory_name,
      </if>
      <if test="firstFpuNo != null" >
        first_fpu_no,
      </if>
      <if test="firstPassFpuNo != null" >
        first_pass_fpu_no,
      </if>
      <if test="firstTimeApplicationFlag != null" >
        first_time_application_flag,
      </if>
      <if test="fpuNo != null" >
        fpu_no,
      </if>
      <if test="fpuReportNo != null" >
        fpu_report_no,
      </if>
      <if test="gpuNo != null" >
        gpu_no,
      </if>
      <if test="lotNo != null" >
        lot_no,
      </if>
      <if test="noOfSample != null" >
        no_of_sample,
      </if>
      <if test="otherSampleInformation != null" >
        other_sample_information,
      </if>
      <if test="peformanceCode != null" >
        peformance_code,
      </if>
      <if test="poNo != null" >
        po_no,
      </if>
      <if test="previousReportNo != null" >
        previous_report_no,
      </if>
      <if test="trimReportNo != null" >
        trim_report_no,
      </if>
      <if test="fabricReport != null" >
        fabric_report,
      </if>
      <if test="productCategory1 != null" >
        product_category1,
      </if>
      <if test="productCategory2 != null" >
        product_category2,
      </if>
      <if test="styleNo != null" >
        style_no,
      </if>
      <if test="refCode1 != null" >
        ref_code1,
      </if>
      <if test="refCode2 != null" >
        ref_code2,
      </if>
      <if test="refCode3 != null" >
        ref_code3,
      </if>
      <if test="refCode4 != null" >
        ref_code4,
      </if>
      <if test="refCode5 != null" >
        ref_code5,
      </if>
      <if test="productColor != null" >
        product_color,
      </if>
      <if test="productDescription != null" >
        product_description,
      </if>
      <if test="productionStage != null" >
        production_stage,
      </if>
      <if test="sampleId != null" >
        sample_id,
      </if>
      <if test="sampleReceivedDate != null" >
        sample_received_date,
      </if>
      <if test="ageGroup != null" >
        age_group,
      </if>
      <if test="endUse1 != null" >
        end_use1,
      </if>
      <if test="specialProductAttribute1 != null" >
        special_product_attribute1,
      </if>
      <if test="specialProductAttribute2 != null" >
        special_product_attribute2,
      </if>
      <if test="specialProductAttribute3 != null" >
        special_product_attribute3,
      </if>
      <if test="construction != null" >
        construction,
      </if>
      <if test="yarnCount != null" >
        yarn_count,
      </if>
      <if test="threadCount != null" >
        thread_count,
      </if>
      <if test="fiberComposition != null" >
        fiber_composition,
      </if>
      <if test="fiberWeight != null" >
        fiber_weight,
      </if>
      <if test="fabricWidth != null" >
        fabric_width,
      </if>
      <if test="season != null" >
        season,
      </if>
      <if test="size != null" >
        `size`,
      </if>
      <if test="specialFinishing != null" >
        special_finishing,
      </if>
      <if test="collection != null" >
        `collection`,
      </if>
      <if test="careLabelFlag != null" >
        care_label_flag,
      </if>
      <if test="careLabel != null" >
        care_label,
      </if>
      <if test="careLabelWording != null" >
        care_label_wording,
      </if>
      <if test="headerId != null" >
        header_id,
      </if>
      <if test="productType != null" >
        product_type,
      </if>
      <if test="productItemNo != null" >
        product_item_no,
      </if>
      <if test="cancelFlag != null" >
        cancel_flag,
      </if>
      <if test="refSampleId != null" >
        ref_sample_id,
      </if>
      <if test="languageID != null" >
        language_id,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="refCode6 != null" >
        ref_code6,
      </if>
      <if test="refCode7 != null" >
        ref_code7,
      </if>
      <if test="refCode8 != null" >
        ref_code8,
      </if>
      <if test="refCode9 != null" >
        ref_code9,
      </if>
      <if test="refCode10 != null" >
        ref_code10,
      </if>
      <if test="specialCustomerAttribute1 != null" >
        special_customer_attribute1,
      </if>
      <if test="specialCustomerAttribute2 != null" >
        special_customer_attribute2,
      </if>
      <if test="specialCustomerAttribute3 != null" >
        special_customer_attribute3,
      </if>
      <if test="specialCustomerAttribute4 != null" >
        special_customer_attribute4,
      </if>
      <if test="specialCustomerAttribute5 != null" >
        special_customer_attribute5,
      </if>
      <if test="specialCustomerAttribute6 != null" >
        special_customer_attribute6,
      </if>
      <if test="specialCustomerAttribute7 != null" >
        special_customer_attribute7,
      </if>
      <if test="specialCustomerAttribute8 != null" >
        special_customer_attribute8,
      </if>
      <if test="specialCustomerAttribute9 != null" >
        special_customer_attribute9,
      </if>
      <if test="specialCustomerAttribute10 != null" >
        special_customer_attribute10,
      </if>
      <if test="specialCustomerAttribute11 != null" >
        special_customer_attribute11,
      </if>
      <if test="specialCustomerAttribute12 != null" >
        special_customer_attribute12,
      </if>
      <if test="specialCustomerAttribute13 != null" >
        special_customer_attribute13,
      </if>
      <if test="specialCustomerAttribute14 != null" >
        special_customer_attribute14,
      </if>
      <if test="specialCustomerAttribute15 != null" >
        special_customer_attribute15,
      </if>
      <if test="specialCustomerAttribute16 != null" >
        special_customer_attribute16,
      </if>
      <if test="specialCustomerAttribute17 != null" >
        special_customer_attribute17,
      </if>
      <if test="specialCustomerAttribute18 != null" >
        special_customer_attribute18,
      </if>
      <if test="specialProductAttribute4 != null" >
        special_product_attribute4,
      </if>
      <if test="specialProductAttribute5 != null" >
        special_product_attribute5,
      </if>
      <if test="specialProductAttribute6 != null" >
        special_product_attribute6,
      </if>
      <if test="specialProductAttribute7 != null" >
        special_product_attribute7,
      </if>
      <if test="specialProductAttribute8 != null" >
        special_product_attribute8,
      </if>
      <if test="specialProductAttribute9 != null" >
        special_product_attribute9,
      </if>
      <if test="specialProductAttribute10 != null" >
        special_product_attribute10,
      </if>
      <if test="itemNo != null" >
        Item_No,
      </if>
      <if test="vendorNo != null" >
        Vendor_No,
      </if>
      <if test="axSampleFlag != null" >
        ax_sample_flag
      </if>
      <if test="externalSampleId != null" >
        external_sample_id
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="enquiryId != null" >
        #{enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="productLibraryId != null" >
        #{productLibraryId,jdbcType=VARCHAR},
      </if>
      <if test="carelabelInstanceId != null" >
        #{carelabelInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="communicationLogId != null" >
        #{communicationLogId,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannization1 != null" >
        #{buyerOrgannization1,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannization2 != null" >
        #{buyerOrgannization2,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannization3 != null" >
        #{buyerOrgannization3,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannizationCode1 != null" >
        #{buyerOrgannizationCode1,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannizationCode2 != null" >
        #{buyerOrgannizationCode2,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannizationCode3 != null" >
        #{buyerOrgannizationCode3,jdbcType=VARCHAR},
      </if>
      <if test="buyerAliase != null" >
        #{buyerAliase,jdbcType=VARCHAR},
      </if>
      <if test="buyerSourcingOffice != null" >
        #{buyerSourcingOffice,jdbcType=VARCHAR},
      </if>
      <if test="countryOfOrigin != null" >
        #{countryOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="countryOfDestination != null" >
        #{countryOfDestination,jdbcType=VARCHAR},
      </if>
      <if test="dffFormID != null" >
        #{dffFormID,jdbcType=VARCHAR},
      </if>
      <if test="supplier != null" >
        #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null" >
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null" >
        #{factoryId,jdbcType=VARCHAR},
      </if>
      <if test="factoryName != null" >
        #{factoryName,jdbcType=VARCHAR},
      </if>
      <if test="firstFpuNo != null" >
        #{firstFpuNo,jdbcType=VARCHAR},
      </if>
      <if test="firstPassFpuNo != null" >
        #{firstPassFpuNo,jdbcType=VARCHAR},
      </if>
      <if test="firstTimeApplicationFlag != null" >
        #{firstTimeApplicationFlag,jdbcType=VARCHAR},
      </if>
      <if test="fpuNo != null" >
        #{fpuNo,jdbcType=VARCHAR},
      </if>
      <if test="fpuReportNo != null" >
        #{fpuReportNo,jdbcType=VARCHAR},
      </if>
      <if test="gpuNo != null" >
        #{gpuNo,jdbcType=VARCHAR},
      </if>
      <if test="lotNo != null" >
        #{lotNo,jdbcType=VARCHAR},
      </if>
      <if test="noOfSample != null" >
        #{noOfSample,jdbcType=INTEGER},
      </if>
      <if test="otherSampleInformation != null" >
        #{otherSampleInformation,jdbcType=VARCHAR},
      </if>
      <if test="peformanceCode != null" >
        #{peformanceCode,jdbcType=VARCHAR},
      </if>
      <if test="poNo != null" >
        #{poNo,jdbcType=VARCHAR},
      </if>
      <if test="previousReportNo != null" >
        #{previousReportNo,jdbcType=VARCHAR},
      </if>
      <if test="trimReportNo != null" >
        #{trimReportNo,jdbcType=VARCHAR},
      </if>
      <if test="fabricReport != null" >
        #{fabricReport,jdbcType=VARCHAR},
      </if>
      <if test="productCategory1 != null" >
        #{productCategory1,jdbcType=VARCHAR},
      </if>
      <if test="productCategory2 != null" >
        #{productCategory2,jdbcType=VARCHAR},
      </if>
      <if test="styleNo != null" >
        #{styleNo,jdbcType=VARCHAR},
      </if>
      <if test="refCode1 != null" >
        #{refCode1,jdbcType=VARCHAR},
      </if>
      <if test="refCode2 != null" >
        #{refCode2,jdbcType=VARCHAR},
      </if>
      <if test="refCode3 != null" >
        #{refCode3,jdbcType=VARCHAR},
      </if>
      <if test="refCode4 != null" >
        #{refCode4,jdbcType=VARCHAR},
      </if>
      <if test="refCode5 != null" >
        #{refCode5,jdbcType=VARCHAR},
      </if>
      <if test="productColor != null" >
        #{productColor,jdbcType=VARCHAR},
      </if>
      <if test="productDescription != null" >
        #{productDescription,jdbcType=VARCHAR},
      </if>
      <if test="productionStage != null" >
        #{productionStage,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null" >
        #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="sampleReceivedDate != null" >
        #{sampleReceivedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ageGroup != null" >
        #{ageGroup,jdbcType=VARCHAR},
      </if>
      <if test="endUse1 != null" >
        #{endUse1,jdbcType=VARCHAR},
      </if>
      <if test="specialProductAttribute1 != null" >
        #{specialProductAttribute1,jdbcType=VARCHAR},
      </if>
      <if test="specialProductAttribute2 != null" >
        #{specialProductAttribute2,jdbcType=VARCHAR},
      </if>
      <if test="specialProductAttribute3 != null" >
        #{specialProductAttribute3,jdbcType=VARCHAR},
      </if>
      <if test="construction != null" >
        #{construction,jdbcType=VARCHAR},
      </if>
      <if test="yarnCount != null" >
        #{yarnCount,jdbcType=VARCHAR},
      </if>
      <if test="threadCount != null" >
        #{threadCount,jdbcType=VARCHAR},
      </if>
      <if test="fiberComposition != null" >
        #{fiberComposition,jdbcType=VARCHAR},
      </if>
      <if test="fiberWeight != null" >
        #{fiberWeight,jdbcType=VARCHAR},
      </if>
      <if test="fabricWidth != null" >
        #{fabricWidth,jdbcType=VARCHAR},
      </if>
      <if test="season != null" >
        #{season,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="specialFinishing != null" >
        #{specialFinishing,jdbcType=VARCHAR},
      </if>
      <if test="collection != null" >
        #{collection,jdbcType=VARCHAR},
      </if>
      <if test="careLabelFlag != null" >
        #{careLabelFlag,jdbcType=VARCHAR},
      </if>
      <if test="careLabel != null" >
        #{careLabel,jdbcType=VARCHAR},
      </if>
      <if test="careLabelWording != null" >
        #{careLabelWording,jdbcType=VARCHAR},
      </if>
      <if test="headerId != null" >
        #{headerId,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        #{productType,jdbcType=VARCHAR},
      </if>
      <if test="productItemNo != null" >
        #{productItemNo,jdbcType=VARCHAR},
      </if>
      <if test="cancelFlag != null" >
        #{cancelFlag,jdbcType=TINYINT},
      </if>
      <if test="refSampleId != null" >
        #{refSampleId,jdbcType=VARCHAR},
      </if>
      <if test="languageID != null" >
        #{languageID,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="refCode6 != null" >
        #{refCode6,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode7 != null" >
        #{refCode7,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode8 != null" >
        #{refCode8,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode9 != null" >
        #{refCode9,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode10 != null" >
        #{refCode10,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute1 != null" >
        #{specialCustomerAttribute1,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute2 != null" >
        #{specialCustomerAttribute2,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute3 != null" >
        #{specialCustomerAttribute3,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute4 != null" >
        #{specialCustomerAttribute4,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute5 != null" >
        #{specialCustomerAttribute5,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute6 != null" >
        #{specialCustomerAttribute6,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute7 != null" >
        #{specialCustomerAttribute7,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute8 != null" >
        #{specialCustomerAttribute8,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute9 != null" >
        #{specialCustomerAttribute9,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute10 != null" >
        #{specialCustomerAttribute10,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute11 != null" >
        #{specialCustomerAttribute11,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute12 != null" >
        #{specialCustomerAttribute12,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute13 != null" >
        #{specialCustomerAttribute13,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute14 != null" >
        #{specialCustomerAttribute14,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute15 != null" >
        #{specialCustomerAttribute15,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute16 != null" >
        #{specialCustomerAttribute16,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute17 != null" >
        #{specialCustomerAttribute17,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute18 != null" >
        #{specialCustomerAttribute18,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute4 != null" >
        #{specialProductAttribute4,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute5 != null" >
        #{specialProductAttribute5,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute6 != null" >
        #{specialProductAttribute6,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute7 != null" >
        #{specialProductAttribute7,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute8 != null" >
        #{specialProductAttribute8,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute9 != null" >
        #{specialProductAttribute9,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute10 != null" >
        #{specialProductAttribute10,jdbcType=LONGVARCHAR},
      </if>
      <if test="itemNo != null" >
        #{itemNo,jdbcType=LONGVARCHAR},
      </if>
      <if test="vendorNo != null" >
        #{vendorNo,jdbcType=LONGVARCHAR},
      </if>
      <if test="axSampleFlag != null" >
        #{axSampleFlag,jdbcType=INTEGER},
      </if>
      <if test="externalSampleId != null" >
        #{externalSampleId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_enquiry_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_enquiry_product
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryId != null" >
        enquiry_id = #{record.enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="record.productLibraryId != null" >
        product_library_id = #{record.productLibraryId,jdbcType=VARCHAR},
      </if>
      <if test="record.carelabelInstanceId != null" >
        careLabel_instance_id = #{record.carelabelInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.communicationLogId != null" >
        communication_log_id = #{record.communicationLogId,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerOrgannization1 != null" >
        buyer_organnization1 = #{record.buyerOrgannization1,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerOrgannization2 != null" >
        buyer_organnization2 = #{record.buyerOrgannization2,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerOrgannization3 != null" >
        buyer_organnization3 = #{record.buyerOrgannization3,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerOrgannizationCode1 != null" >
        buyer_organnization_code1 = #{record.buyerOrgannizationCode1,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerOrgannizationCode2 != null" >
        buyer_organnization_code2 = #{record.buyerOrgannizationCode2,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerOrgannizationCode3 != null" >
        buyer_organnization_code3 = #{record.buyerOrgannizationCode3,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerAliase != null" >
        buyer_aliase = #{record.buyerAliase,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerSourcingOffice != null" >
        buyer_sourcing_office = #{record.buyerSourcingOffice,jdbcType=VARCHAR},
      </if>
      <if test="record.countryOfOrigin != null" >
        country_of_origin = #{record.countryOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="record.countryOfDestination != null" >
        country_of_destination = #{record.countryOfDestination,jdbcType=VARCHAR},
      </if>
      <if test="record.dffFormID != null" >
        dff_form_id = #{record.dffFormID,jdbcType=VARCHAR},
      </if>
      <if test="record.supplier != null" >
        supplier = #{record.supplier,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null" >
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryId != null" >
        factory_id = #{record.factoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.factoryName != null" >
        factory_name = #{record.factoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.firstFpuNo != null" >
        first_fpu_no = #{record.firstFpuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.firstPassFpuNo != null" >
        first_pass_fpu_no = #{record.firstPassFpuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.firstTimeApplicationFlag != null" >
        first_time_application_flag = #{record.firstTimeApplicationFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.fpuNo != null" >
        fpu_no = #{record.fpuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fpuReportNo != null" >
        fpu_report_no = #{record.fpuReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.gpuNo != null" >
        gpu_no = #{record.gpuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.lotNo != null" >
        lot_no = #{record.lotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.noOfSample != null" >
        no_of_sample = #{record.noOfSample,jdbcType=INTEGER},
      </if>
      <if test="record.otherSampleInformation != null" >
        other_sample_information = #{record.otherSampleInformation,jdbcType=VARCHAR},
      </if>
      <if test="record.peformanceCode != null" >
        peformance_code = #{record.peformanceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.poNo != null" >
        po_no = #{record.poNo,jdbcType=VARCHAR},
      </if>
      <if test="record.previousReportNo != null" >
        previous_report_no = #{record.previousReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.trimReportNo != null" >
        trim_report_no = #{record.trimReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fabricReport != null" >
        fabric_report = #{record.fabricReport,jdbcType=VARCHAR},
      </if>
      <if test="record.productCategory1 != null" >
        product_category1 = #{record.productCategory1,jdbcType=VARCHAR},
      </if>
      <if test="record.productCategory2 != null" >
        product_category2 = #{record.productCategory2,jdbcType=VARCHAR},
      </if>
      <if test="record.styleNo != null" >
        style_no = #{record.styleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.refCode1 != null" >
        ref_code1 = #{record.refCode1,jdbcType=VARCHAR},
      </if>
      <if test="record.refCode2 != null" >
        ref_code2 = #{record.refCode2,jdbcType=VARCHAR},
      </if>
      <if test="record.refCode3 != null" >
        ref_code3 = #{record.refCode3,jdbcType=VARCHAR},
      </if>
      <if test="record.refCode4 != null" >
        ref_code4 = #{record.refCode4,jdbcType=VARCHAR},
      </if>
      <if test="record.refCode5 != null" >
        ref_code5 = #{record.refCode5,jdbcType=VARCHAR},
      </if>
      <if test="record.productColor != null" >
        product_color = #{record.productColor,jdbcType=VARCHAR},
      </if>
      <if test="record.productDescription != null" >
        product_description = #{record.productDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.productionStage != null" >
        production_stage = #{record.productionStage,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleId != null" >
        sample_id = #{record.sampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleReceivedDate != null" >
        sample_received_date = #{record.sampleReceivedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ageGroup != null" >
        age_group = #{record.ageGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.endUse1 != null" >
        end_use1 = #{record.endUse1,jdbcType=VARCHAR},
      </if>
      <if test="record.specialProductAttribute1 != null" >
        special_product_attribute1 = #{record.specialProductAttribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.specialProductAttribute2 != null" >
        special_product_attribute2 = #{record.specialProductAttribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.specialProductAttribute3 != null" >
        special_product_attribute3 = #{record.specialProductAttribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.construction != null" >
        construction = #{record.construction,jdbcType=VARCHAR},
      </if>
      <if test="record.yarnCount != null" >
        yarn_count = #{record.yarnCount,jdbcType=VARCHAR},
      </if>
      <if test="record.threadCount != null" >
        thread_count = #{record.threadCount,jdbcType=VARCHAR},
      </if>
      <if test="record.fiberComposition != null" >
        fiber_composition = #{record.fiberComposition,jdbcType=VARCHAR},
      </if>
      <if test="record.fiberWeight != null" >
        fiber_weight = #{record.fiberWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.fabricWidth != null" >
        fabric_width = #{record.fabricWidth,jdbcType=VARCHAR},
      </if>
      <if test="record.season != null" >
        season = #{record.season,jdbcType=VARCHAR},
      </if>
      <if test="record.size != null" >
        `size` = #{record.size,jdbcType=VARCHAR},
      </if>
      <if test="record.specialFinishing != null" >
        special_finishing = #{record.specialFinishing,jdbcType=VARCHAR},
      </if>
      <if test="record.collection != null" >
        `collection` = #{record.collection,jdbcType=VARCHAR},
      </if>
      <if test="record.careLabelFlag != null" >
        care_label_flag = #{record.careLabelFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.careLabel != null" >
        care_label = #{record.careLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.careLabelWording != null" >
        care_label_wording = #{record.careLabelWording,jdbcType=VARCHAR},
      </if>
      <if test="record.headerId != null" >
        header_id = #{record.headerId,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.productItemNo != null" >
        product_item_no = #{record.productItemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelFlag != null" >
        cancel_flag = #{record.cancelFlag,jdbcType=TINYINT},
      </if>
      <if test="record.refSampleId != null" >
        ref_sample_id = #{record.refSampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.languageID != null" >
        language_id = #{record.languageID,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refCode6 != null" >
        ref_code6 = #{record.refCode6,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.refCode7 != null" >
        ref_code7 = #{record.refCode7,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.refCode8 != null" >
        ref_code8 = #{record.refCode8,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.refCode9 != null" >
        ref_code9 = #{record.refCode9,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.refCode10 != null" >
        ref_code10 = #{record.refCode10,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute1 != null" >
        special_customer_attribute1 = #{record.specialCustomerAttribute1,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute2 != null" >
        special_customer_attribute2 = #{record.specialCustomerAttribute2,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute3 != null" >
        special_customer_attribute3 = #{record.specialCustomerAttribute3,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute4 != null" >
        special_customer_attribute4 = #{record.specialCustomerAttribute4,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute5 != null" >
        special_customer_attribute5 = #{record.specialCustomerAttribute5,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute6 != null" >
        special_customer_attribute6 = #{record.specialCustomerAttribute6,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute7 != null" >
        special_customer_attribute7 = #{record.specialCustomerAttribute7,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute8 != null" >
        special_customer_attribute8 = #{record.specialCustomerAttribute8,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute9 != null" >
        special_customer_attribute9 = #{record.specialCustomerAttribute9,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute10 != null" >
        special_customer_attribute10 = #{record.specialCustomerAttribute10,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute11 != null" >
        special_customer_attribute11 = #{record.specialCustomerAttribute11,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute12 != null" >
        special_customer_attribute12 = #{record.specialCustomerAttribute12,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute13 != null" >
        special_customer_attribute13 = #{record.specialCustomerAttribute13,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute14 != null" >
        special_customer_attribute14 = #{record.specialCustomerAttribute14,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute15 != null" >
        special_customer_attribute15 = #{record.specialCustomerAttribute15,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute16 != null" >
        special_customer_attribute16 = #{record.specialCustomerAttribute16,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute17 != null" >
        special_customer_attribute17 = #{record.specialCustomerAttribute17,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialCustomerAttribute18 != null" >
        special_customer_attribute18 = #{record.specialCustomerAttribute18,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialProductAttribute4 != null" >
        special_product_attribute4 = #{record.specialProductAttribute4,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialProductAttribute5 != null" >
        special_product_attribute5 = #{record.specialProductAttribute5,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialProductAttribute6 != null" >
        special_product_attribute6 = #{record.specialProductAttribute6,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialProductAttribute7 != null" >
        special_product_attribute7 = #{record.specialProductAttribute7,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialProductAttribute8 != null" >
        special_product_attribute8 = #{record.specialProductAttribute8,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialProductAttribute9 != null" >
        special_product_attribute9 = #{record.specialProductAttribute9,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specialProductAttribute10 != null" >
        special_product_attribute10 = #{record.specialProductAttribute10,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.itemNo != null" >
        Item_No = #{record.itemNo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.vendorNo != null" >
        Vendor_No = #{record.vendorNo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.axSampleFlag != null" >
        ax_sample_flag = #{record.axSampleFlag,jdbcType=INTEGER},
      </if>
      <if test="record.externalSampleId != null" >
        external_sample_id = #{record.externalSampleId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_enquiry_product
    set id = #{record.id,jdbcType=VARCHAR},
      enquiry_id = #{record.enquiryId,jdbcType=VARCHAR},
      product_library_id = #{record.productLibraryId,jdbcType=VARCHAR},
      careLabel_instance_id = #{record.carelabelInstanceId,jdbcType=VARCHAR},
      communication_log_id = #{record.communicationLogId,jdbcType=VARCHAR},
      buyer_organnization1 = #{record.buyerOrgannization1,jdbcType=VARCHAR},
      buyer_organnization2 = #{record.buyerOrgannization2,jdbcType=VARCHAR},
      buyer_organnization3 = #{record.buyerOrgannization3,jdbcType=VARCHAR},
      buyer_organnization_code1 = #{record.buyerOrgannizationCode1,jdbcType=VARCHAR},
      buyer_organnization_code2 = #{record.buyerOrgannizationCode2,jdbcType=VARCHAR},
      buyer_organnization_code3 = #{record.buyerOrgannizationCode3,jdbcType=VARCHAR},
      buyer_aliase = #{record.buyerAliase,jdbcType=VARCHAR},
      buyer_sourcing_office = #{record.buyerSourcingOffice,jdbcType=VARCHAR},
      country_of_origin = #{record.countryOfOrigin,jdbcType=VARCHAR},
      country_of_destination = #{record.countryOfDestination,jdbcType=VARCHAR},
      dff_form_id = #{record.dffFormID,jdbcType=VARCHAR},
      supplier = #{record.supplier,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      factory_id = #{record.factoryId,jdbcType=VARCHAR},
      factory_name = #{record.factoryName,jdbcType=VARCHAR},
      first_fpu_no = #{record.firstFpuNo,jdbcType=VARCHAR},
      first_pass_fpu_no = #{record.firstPassFpuNo,jdbcType=VARCHAR},
      first_time_application_flag = #{record.firstTimeApplicationFlag,jdbcType=VARCHAR},
      fpu_no = #{record.fpuNo,jdbcType=VARCHAR},
      fpu_report_no = #{record.fpuReportNo,jdbcType=VARCHAR},
      gpu_no = #{record.gpuNo,jdbcType=VARCHAR},
      lot_no = #{record.lotNo,jdbcType=VARCHAR},
      no_of_sample = #{record.noOfSample,jdbcType=INTEGER},
      other_sample_information = #{record.otherSampleInformation,jdbcType=VARCHAR},
      peformance_code = #{record.peformanceCode,jdbcType=VARCHAR},
      po_no = #{record.poNo,jdbcType=VARCHAR},
      previous_report_no = #{record.previousReportNo,jdbcType=VARCHAR},
      trim_report_no = #{record.trimReportNo,jdbcType=VARCHAR},
      fabric_report = #{record.fabricReport,jdbcType=VARCHAR},
      product_category1 = #{record.productCategory1,jdbcType=VARCHAR},
      product_category2 = #{record.productCategory2,jdbcType=VARCHAR},
      style_no = #{record.styleNo,jdbcType=VARCHAR},
      ref_code1 = #{record.refCode1,jdbcType=VARCHAR},
      ref_code2 = #{record.refCode2,jdbcType=VARCHAR},
      ref_code3 = #{record.refCode3,jdbcType=VARCHAR},
      ref_code4 = #{record.refCode4,jdbcType=VARCHAR},
      ref_code5 = #{record.refCode5,jdbcType=VARCHAR},
      product_color = #{record.productColor,jdbcType=VARCHAR},
      product_description = #{record.productDescription,jdbcType=VARCHAR},
      production_stage = #{record.productionStage,jdbcType=VARCHAR},
      sample_id = #{record.sampleId,jdbcType=VARCHAR},
      sample_received_date = #{record.sampleReceivedDate,jdbcType=TIMESTAMP},
      age_group = #{record.ageGroup,jdbcType=VARCHAR},
      end_use1 = #{record.endUse1,jdbcType=VARCHAR},
      special_product_attribute1 = #{record.specialProductAttribute1,jdbcType=VARCHAR},
      special_product_attribute2 = #{record.specialProductAttribute2,jdbcType=VARCHAR},
      special_product_attribute3 = #{record.specialProductAttribute3,jdbcType=VARCHAR},
      construction = #{record.construction,jdbcType=VARCHAR},
      yarn_count = #{record.yarnCount,jdbcType=VARCHAR},
      thread_count = #{record.threadCount,jdbcType=VARCHAR},
      fiber_composition = #{record.fiberComposition,jdbcType=VARCHAR},
      fiber_weight = #{record.fiberWeight,jdbcType=VARCHAR},
      fabric_width = #{record.fabricWidth,jdbcType=VARCHAR},
      season = #{record.season,jdbcType=VARCHAR},
      `size` = #{record.size,jdbcType=VARCHAR},
      special_finishing = #{record.specialFinishing,jdbcType=VARCHAR},
      `collection` = #{record.collection,jdbcType=VARCHAR},
      care_label_flag = #{record.careLabelFlag,jdbcType=VARCHAR},
      care_label = #{record.careLabel,jdbcType=VARCHAR},
      care_label_wording = #{record.careLabelWording,jdbcType=VARCHAR},
      header_id = #{record.headerId,jdbcType=VARCHAR},
      product_type = #{record.productType,jdbcType=VARCHAR},
      product_item_no = #{record.productItemNo,jdbcType=VARCHAR},
      cancel_flag = #{record.cancelFlag,jdbcType=TINYINT},
      ref_sample_id = #{record.refSampleId,jdbcType=VARCHAR},
      language_id = #{record.languageID,jdbcType=INTEGER},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ref_code6 = #{record.refCode6,jdbcType=LONGVARCHAR},
      ref_code7 = #{record.refCode7,jdbcType=LONGVARCHAR},
      ref_code8 = #{record.refCode8,jdbcType=LONGVARCHAR},
      ref_code9 = #{record.refCode9,jdbcType=LONGVARCHAR},
      ref_code10 = #{record.refCode10,jdbcType=LONGVARCHAR},
      special_customer_attribute1 = #{record.specialCustomerAttribute1,jdbcType=LONGVARCHAR},
      special_customer_attribute2 = #{record.specialCustomerAttribute2,jdbcType=LONGVARCHAR},
      special_customer_attribute3 = #{record.specialCustomerAttribute3,jdbcType=LONGVARCHAR},
      special_customer_attribute4 = #{record.specialCustomerAttribute4,jdbcType=LONGVARCHAR},
      special_customer_attribute5 = #{record.specialCustomerAttribute5,jdbcType=LONGVARCHAR},
      special_customer_attribute6 = #{record.specialCustomerAttribute6,jdbcType=LONGVARCHAR},
      special_customer_attribute7 = #{record.specialCustomerAttribute7,jdbcType=LONGVARCHAR},
      special_customer_attribute8 = #{record.specialCustomerAttribute8,jdbcType=LONGVARCHAR},
      special_customer_attribute9 = #{record.specialCustomerAttribute9,jdbcType=LONGVARCHAR},
      special_customer_attribute10 = #{record.specialCustomerAttribute10,jdbcType=LONGVARCHAR},
      special_customer_attribute11 = #{record.specialCustomerAttribute11,jdbcType=LONGVARCHAR},
      special_customer_attribute12 = #{record.specialCustomerAttribute12,jdbcType=LONGVARCHAR},
      special_customer_attribute13 = #{record.specialCustomerAttribute13,jdbcType=LONGVARCHAR},
      special_customer_attribute14 = #{record.specialCustomerAttribute14,jdbcType=LONGVARCHAR},
      special_customer_attribute15 = #{record.specialCustomerAttribute15,jdbcType=LONGVARCHAR},
      special_customer_attribute16 = #{record.specialCustomerAttribute16,jdbcType=LONGVARCHAR},
      special_customer_attribute17 = #{record.specialCustomerAttribute17,jdbcType=LONGVARCHAR},
      special_customer_attribute18 = #{record.specialCustomerAttribute18,jdbcType=LONGVARCHAR},
      special_product_attribute4 = #{record.specialProductAttribute4,jdbcType=LONGVARCHAR},
      special_product_attribute5 = #{record.specialProductAttribute5,jdbcType=LONGVARCHAR},
      special_product_attribute6 = #{record.specialProductAttribute6,jdbcType=LONGVARCHAR},
      special_product_attribute7 = #{record.specialProductAttribute7,jdbcType=LONGVARCHAR},
      special_product_attribute8 = #{record.specialProductAttribute8,jdbcType=LONGVARCHAR},
      special_product_attribute9 = #{record.specialProductAttribute9,jdbcType=LONGVARCHAR},
      special_product_attribute10 = #{record.specialProductAttribute10,jdbcType=LONGVARCHAR},
      Item_No = #{record.itemNo,jdbcType=LONGVARCHAR},
      Vendor_No = #{record.vendorNo,jdbcType=LONGVARCHAR},
      ax_sample_flag = #{record.axSampleFlag,jdbcType=INTEGER},
      external_sample_id = #{record.externalSampleId,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_enquiry_product
    set id = #{record.id,jdbcType=VARCHAR},
      enquiry_id = #{record.enquiryId,jdbcType=VARCHAR},
      product_library_id = #{record.productLibraryId,jdbcType=VARCHAR},
      careLabel_instance_id = #{record.carelabelInstanceId,jdbcType=VARCHAR},
      communication_log_id = #{record.communicationLogId,jdbcType=VARCHAR},
      buyer_organnization1 = #{record.buyerOrgannization1,jdbcType=VARCHAR},
      buyer_organnization2 = #{record.buyerOrgannization2,jdbcType=VARCHAR},
      buyer_organnization3 = #{record.buyerOrgannization3,jdbcType=VARCHAR},
      buyer_organnization_code1 = #{record.buyerOrgannizationCode1,jdbcType=VARCHAR},
      buyer_organnization_code2 = #{record.buyerOrgannizationCode2,jdbcType=VARCHAR},
      buyer_organnization_code3 = #{record.buyerOrgannizationCode3,jdbcType=VARCHAR},
      buyer_aliase = #{record.buyerAliase,jdbcType=VARCHAR},
      buyer_sourcing_office = #{record.buyerSourcingOffice,jdbcType=VARCHAR},
      country_of_origin = #{record.countryOfOrigin,jdbcType=VARCHAR},
      country_of_destination = #{record.countryOfDestination,jdbcType=VARCHAR},
      dff_form_id = #{record.dffFormID,jdbcType=VARCHAR},
      supplier = #{record.supplier,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      factory_id = #{record.factoryId,jdbcType=VARCHAR},
      factory_name = #{record.factoryName,jdbcType=VARCHAR},
      first_fpu_no = #{record.firstFpuNo,jdbcType=VARCHAR},
      first_pass_fpu_no = #{record.firstPassFpuNo,jdbcType=VARCHAR},
      first_time_application_flag = #{record.firstTimeApplicationFlag,jdbcType=VARCHAR},
      fpu_no = #{record.fpuNo,jdbcType=VARCHAR},
      fpu_report_no = #{record.fpuReportNo,jdbcType=VARCHAR},
      gpu_no = #{record.gpuNo,jdbcType=VARCHAR},
      lot_no = #{record.lotNo,jdbcType=VARCHAR},
      no_of_sample = #{record.noOfSample,jdbcType=INTEGER},
      other_sample_information = #{record.otherSampleInformation,jdbcType=VARCHAR},
      peformance_code = #{record.peformanceCode,jdbcType=VARCHAR},
      po_no = #{record.poNo,jdbcType=VARCHAR},
      previous_report_no = #{record.previousReportNo,jdbcType=VARCHAR},
      trim_report_no = #{record.trimReportNo,jdbcType=VARCHAR},
      fabric_report = #{record.fabricReport,jdbcType=VARCHAR},
      product_category1 = #{record.productCategory1,jdbcType=VARCHAR},
      product_category2 = #{record.productCategory2,jdbcType=VARCHAR},
      style_no = #{record.styleNo,jdbcType=VARCHAR},
      ref_code1 = #{record.refCode1,jdbcType=VARCHAR},
      ref_code2 = #{record.refCode2,jdbcType=VARCHAR},
      ref_code3 = #{record.refCode3,jdbcType=VARCHAR},
      ref_code4 = #{record.refCode4,jdbcType=VARCHAR},
      ref_code5 = #{record.refCode5,jdbcType=VARCHAR},
      product_color = #{record.productColor,jdbcType=VARCHAR},
      product_description = #{record.productDescription,jdbcType=VARCHAR},
      production_stage = #{record.productionStage,jdbcType=VARCHAR},
      sample_id = #{record.sampleId,jdbcType=VARCHAR},
      sample_received_date = #{record.sampleReceivedDate,jdbcType=TIMESTAMP},
      age_group = #{record.ageGroup,jdbcType=VARCHAR},
      end_use1 = #{record.endUse1,jdbcType=VARCHAR},
      special_product_attribute1 = #{record.specialProductAttribute1,jdbcType=VARCHAR},
      special_product_attribute2 = #{record.specialProductAttribute2,jdbcType=VARCHAR},
      special_product_attribute3 = #{record.specialProductAttribute3,jdbcType=VARCHAR},
      construction = #{record.construction,jdbcType=VARCHAR},
      yarn_count = #{record.yarnCount,jdbcType=VARCHAR},
      thread_count = #{record.threadCount,jdbcType=VARCHAR},
      fiber_composition = #{record.fiberComposition,jdbcType=VARCHAR},
      fiber_weight = #{record.fiberWeight,jdbcType=VARCHAR},
      fabric_width = #{record.fabricWidth,jdbcType=VARCHAR},
      season = #{record.season,jdbcType=VARCHAR},
      `size` = #{record.size,jdbcType=VARCHAR},
      special_finishing = #{record.specialFinishing,jdbcType=VARCHAR},
      `collection` = #{record.collection,jdbcType=VARCHAR},
      care_label_flag = #{record.careLabelFlag,jdbcType=VARCHAR},
      care_label = #{record.careLabel,jdbcType=VARCHAR},
      care_label_wording = #{record.careLabelWording,jdbcType=VARCHAR},
      header_id = #{record.headerId,jdbcType=VARCHAR},
      product_type = #{record.productType,jdbcType=VARCHAR},
      product_item_no = #{record.productItemNo,jdbcType=VARCHAR},
      cancel_flag = #{record.cancelFlag,jdbcType=TINYINT},
      ref_sample_id = #{record.refSampleId,jdbcType=VARCHAR},
      language_id = #{record.languageID,jdbcType=INTEGER},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoWithBLOBs" >
    update tb_enquiry_product
    <set >
      <if test="enquiryId != null" >
        enquiry_id = #{enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="productLibraryId != null" >
        product_library_id = #{productLibraryId,jdbcType=VARCHAR},
      </if>
      <if test="carelabelInstanceId != null" >
        careLabel_instance_id = #{carelabelInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="communicationLogId != null" >
        communication_log_id = #{communicationLogId,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannization1 != null" >
        buyer_organnization1 = #{buyerOrgannization1,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannization2 != null" >
        buyer_organnization2 = #{buyerOrgannization2,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannization3 != null" >
        buyer_organnization3 = #{buyerOrgannization3,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannizationCode1 != null" >
        buyer_organnization_code1 = #{buyerOrgannizationCode1,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannizationCode2 != null" >
        buyer_organnization_code2 = #{buyerOrgannizationCode2,jdbcType=VARCHAR},
      </if>
      <if test="buyerOrgannizationCode3 != null" >
        buyer_organnization_code3 = #{buyerOrgannizationCode3,jdbcType=VARCHAR},
      </if>
      <if test="buyerAliase != null" >
        buyer_aliase = #{buyerAliase,jdbcType=VARCHAR},
      </if>
      <if test="buyerSourcingOffice != null" >
        buyer_sourcing_office = #{buyerSourcingOffice,jdbcType=VARCHAR},
      </if>
      <if test="countryOfOrigin != null" >
        country_of_origin = #{countryOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="countryOfDestination != null" >
        country_of_destination = #{countryOfDestination,jdbcType=VARCHAR},
      </if>
      <if test="dffFormID != null" >
        dff_form_id = #{dffFormID,jdbcType=VARCHAR},
      </if>
      <if test="supplier != null" >
        supplier = #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null" >
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null" >
        factory_id = #{factoryId,jdbcType=VARCHAR},
      </if>
      <if test="factoryName != null" >
        factory_name = #{factoryName,jdbcType=VARCHAR},
      </if>
      <if test="firstFpuNo != null" >
        first_fpu_no = #{firstFpuNo,jdbcType=VARCHAR},
      </if>
      <if test="firstPassFpuNo != null" >
        first_pass_fpu_no = #{firstPassFpuNo,jdbcType=VARCHAR},
      </if>
      <if test="firstTimeApplicationFlag != null" >
        first_time_application_flag = #{firstTimeApplicationFlag,jdbcType=VARCHAR},
      </if>
      <if test="fpuNo != null" >
        fpu_no = #{fpuNo,jdbcType=VARCHAR},
      </if>
      <if test="fpuReportNo != null" >
        fpu_report_no = #{fpuReportNo,jdbcType=VARCHAR},
      </if>
      <if test="gpuNo != null" >
        gpu_no = #{gpuNo,jdbcType=VARCHAR},
      </if>
      <if test="lotNo != null" >
        lot_no = #{lotNo,jdbcType=VARCHAR},
      </if>
      <if test="noOfSample != null" >
        no_of_sample = #{noOfSample,jdbcType=INTEGER},
      </if>
      <if test="otherSampleInformation != null" >
        other_sample_information = #{otherSampleInformation,jdbcType=VARCHAR},
      </if>
      <if test="peformanceCode != null" >
        peformance_code = #{peformanceCode,jdbcType=VARCHAR},
      </if>
      <if test="poNo != null" >
        po_no = #{poNo,jdbcType=VARCHAR},
      </if>
      <if test="previousReportNo != null" >
        previous_report_no = #{previousReportNo,jdbcType=VARCHAR},
      </if>
      <if test="trimReportNo != null" >
        trim_report_no = #{trimReportNo,jdbcType=VARCHAR},
      </if>
      <if test="fabricReport != null" >
        fabric_report = #{fabricReport,jdbcType=VARCHAR},
      </if>
      <if test="productCategory1 != null" >
        product_category1 = #{productCategory1,jdbcType=VARCHAR},
      </if>
      <if test="productCategory2 != null" >
        product_category2 = #{productCategory2,jdbcType=VARCHAR},
      </if>
      <if test="styleNo != null" >
        style_no = #{styleNo,jdbcType=VARCHAR},
      </if>
      <if test="refCode1 != null" >
        ref_code1 = #{refCode1,jdbcType=VARCHAR},
      </if>
      <if test="refCode2 != null" >
        ref_code2 = #{refCode2,jdbcType=VARCHAR},
      </if>
      <if test="refCode3 != null" >
        ref_code3 = #{refCode3,jdbcType=VARCHAR},
      </if>
      <if test="refCode4 != null" >
        ref_code4 = #{refCode4,jdbcType=VARCHAR},
      </if>
      <if test="refCode5 != null" >
        ref_code5 = #{refCode5,jdbcType=VARCHAR},
      </if>
      <if test="productColor != null" >
        product_color = #{productColor,jdbcType=VARCHAR},
      </if>
      <if test="productDescription != null" >
        product_description = #{productDescription,jdbcType=VARCHAR},
      </if>
      <if test="productionStage != null" >
        production_stage = #{productionStage,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null" >
        sample_id = #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="sampleReceivedDate != null" >
        sample_received_date = #{sampleReceivedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ageGroup != null" >
        age_group = #{ageGroup,jdbcType=VARCHAR},
      </if>
      <if test="endUse1 != null" >
        end_use1 = #{endUse1,jdbcType=VARCHAR},
      </if>
      <if test="specialProductAttribute1 != null" >
        special_product_attribute1 = #{specialProductAttribute1,jdbcType=VARCHAR},
      </if>
      <if test="specialProductAttribute2 != null" >
        special_product_attribute2 = #{specialProductAttribute2,jdbcType=VARCHAR},
      </if>
      <if test="specialProductAttribute3 != null" >
        special_product_attribute3 = #{specialProductAttribute3,jdbcType=VARCHAR},
      </if>
      <if test="construction != null" >
        construction = #{construction,jdbcType=VARCHAR},
      </if>
      <if test="yarnCount != null" >
        yarn_count = #{yarnCount,jdbcType=VARCHAR},
      </if>
      <if test="threadCount != null" >
        thread_count = #{threadCount,jdbcType=VARCHAR},
      </if>
      <if test="fiberComposition != null" >
        fiber_composition = #{fiberComposition,jdbcType=VARCHAR},
      </if>
      <if test="fiberWeight != null" >
        fiber_weight = #{fiberWeight,jdbcType=VARCHAR},
      </if>
      <if test="fabricWidth != null" >
        fabric_width = #{fabricWidth,jdbcType=VARCHAR},
      </if>
      <if test="season != null" >
        season = #{season,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        `size` = #{size,jdbcType=VARCHAR},
      </if>
      <if test="specialFinishing != null" >
        special_finishing = #{specialFinishing,jdbcType=VARCHAR},
      </if>
      <if test="collection != null" >
        `collection` = #{collection,jdbcType=VARCHAR},
      </if>
      <if test="careLabelFlag != null" >
        care_label_flag = #{careLabelFlag,jdbcType=VARCHAR},
      </if>
      <if test="careLabel != null" >
        care_label = #{careLabel,jdbcType=VARCHAR},
      </if>
      <if test="careLabelWording != null" >
        care_label_wording = #{careLabelWording,jdbcType=VARCHAR},
      </if>
      <if test="headerId != null" >
        header_id = #{headerId,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="productItemNo != null" >
        product_item_no = #{productItemNo,jdbcType=VARCHAR},
      </if>
      <if test="cancelFlag != null" >
        cancel_flag = #{cancelFlag,jdbcType=TINYINT},
      </if>
      <if test="refSampleId != null" >
        ref_sample_id = #{refSampleId,jdbcType=VARCHAR},
      </if>
      <if test="languageID != null" >
        language_id = #{languageID,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="refCode6 != null" >
        ref_code6 = #{refCode6,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode7 != null" >
        ref_code7 = #{refCode7,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode8 != null" >
        ref_code8 = #{refCode8,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode9 != null" >
        ref_code9 = #{refCode9,jdbcType=LONGVARCHAR},
      </if>
      <if test="refCode10 != null" >
        ref_code10 = #{refCode10,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute1 != null" >
        special_customer_attribute1 = #{specialCustomerAttribute1,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute2 != null" >
        special_customer_attribute2 = #{specialCustomerAttribute2,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute3 != null" >
        special_customer_attribute3 = #{specialCustomerAttribute3,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute4 != null" >
        special_customer_attribute4 = #{specialCustomerAttribute4,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute5 != null" >
        special_customer_attribute5 = #{specialCustomerAttribute5,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute6 != null" >
        special_customer_attribute6 = #{specialCustomerAttribute6,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute7 != null" >
        special_customer_attribute7 = #{specialCustomerAttribute7,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute8 != null" >
        special_customer_attribute8 = #{specialCustomerAttribute8,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute9 != null" >
        special_customer_attribute9 = #{specialCustomerAttribute9,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute10 != null" >
        special_customer_attribute10 = #{specialCustomerAttribute10,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute11 != null" >
        special_customer_attribute11 = #{specialCustomerAttribute11,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute12 != null" >
        special_customer_attribute12 = #{specialCustomerAttribute12,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute13 != null" >
        special_customer_attribute13 = #{specialCustomerAttribute13,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute14 != null" >
        special_customer_attribute14 = #{specialCustomerAttribute14,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute15 != null" >
        special_customer_attribute15 = #{specialCustomerAttribute15,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute16 != null" >
        special_customer_attribute16 = #{specialCustomerAttribute16,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute17 != null" >
        special_customer_attribute17 = #{specialCustomerAttribute17,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialCustomerAttribute18 != null" >
        special_customer_attribute18 = #{specialCustomerAttribute18,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute4 != null" >
        special_product_attribute4 = #{specialProductAttribute4,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute5 != null" >
        special_product_attribute5 = #{specialProductAttribute5,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute6 != null" >
        special_product_attribute6 = #{specialProductAttribute6,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute7 != null" >
        special_product_attribute7 = #{specialProductAttribute7,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute8 != null" >
        special_product_attribute8 = #{specialProductAttribute8,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute9 != null" >
        special_product_attribute9 = #{specialProductAttribute9,jdbcType=LONGVARCHAR},
      </if>
      <if test="specialProductAttribute10 != null" >
        special_product_attribute10 = #{specialProductAttribute10,jdbcType=LONGVARCHAR},
      </if>
      <if test="itemNo != null" >
        Item_No = #{itemNo,jdbcType=LONGVARCHAR},
      </if>
      <if test="vendorNo != null" >
        Vendor_No = #{vendorNo,jdbcType=LONGVARCHAR},
      </if>
      <if test="axSampleFlag != null" >
        ax_sample_flag = #{axSampleFlag,jdbcType=INTEGER},
      </if>
      <if test="externalSampleId != null" >
        external_sample_id = #{externalSampleId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoWithBLOBs" >
    update tb_enquiry_product
    set enquiry_id = #{enquiryId,jdbcType=VARCHAR},
      product_library_id = #{productLibraryId,jdbcType=VARCHAR},
      careLabel_instance_id = #{carelabelInstanceId,jdbcType=VARCHAR},
      communication_log_id = #{communicationLogId,jdbcType=VARCHAR},
      buyer_organnization1 = #{buyerOrgannization1,jdbcType=VARCHAR},
      buyer_organnization2 = #{buyerOrgannization2,jdbcType=VARCHAR},
      buyer_organnization3 = #{buyerOrgannization3,jdbcType=VARCHAR},
      buyer_organnization_code1 = #{buyerOrgannizationCode1,jdbcType=VARCHAR},
      buyer_organnization_code2 = #{buyerOrgannizationCode2,jdbcType=VARCHAR},
      buyer_organnization_code3 = #{buyerOrgannizationCode3,jdbcType=VARCHAR},
      buyer_aliase = #{buyerAliase,jdbcType=VARCHAR},
      buyer_sourcing_office = #{buyerSourcingOffice,jdbcType=VARCHAR},
      country_of_origin = #{countryOfOrigin,jdbcType=VARCHAR},
      country_of_destination = #{countryOfDestination,jdbcType=VARCHAR},
      dff_form_id = #{dffFormID,jdbcType=VARCHAR},
      supplier = #{supplier,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      factory_id = #{factoryId,jdbcType=VARCHAR},
      factory_name = #{factoryName,jdbcType=VARCHAR},
      first_fpu_no = #{firstFpuNo,jdbcType=VARCHAR},
      first_pass_fpu_no = #{firstPassFpuNo,jdbcType=VARCHAR},
      first_time_application_flag = #{firstTimeApplicationFlag,jdbcType=VARCHAR},
      fpu_no = #{fpuNo,jdbcType=VARCHAR},
      fpu_report_no = #{fpuReportNo,jdbcType=VARCHAR},
      gpu_no = #{gpuNo,jdbcType=VARCHAR},
      lot_no = #{lotNo,jdbcType=VARCHAR},
      no_of_sample = #{noOfSample,jdbcType=INTEGER},
      other_sample_information = #{otherSampleInformation,jdbcType=VARCHAR},
      peformance_code = #{peformanceCode,jdbcType=VARCHAR},
      po_no = #{poNo,jdbcType=VARCHAR},
      previous_report_no = #{previousReportNo,jdbcType=VARCHAR},
      trim_report_no = #{trimReportNo,jdbcType=VARCHAR},
      fabric_report = #{fabricReport,jdbcType=VARCHAR},
      product_category1 = #{productCategory1,jdbcType=VARCHAR},
      product_category2 = #{productCategory2,jdbcType=VARCHAR},
      style_no = #{styleNo,jdbcType=VARCHAR},
      ref_code1 = #{refCode1,jdbcType=VARCHAR},
      ref_code2 = #{refCode2,jdbcType=VARCHAR},
      ref_code3 = #{refCode3,jdbcType=VARCHAR},
      ref_code4 = #{refCode4,jdbcType=VARCHAR},
      ref_code5 = #{refCode5,jdbcType=VARCHAR},
      product_color = #{productColor,jdbcType=VARCHAR},
      product_description = #{productDescription,jdbcType=VARCHAR},
      production_stage = #{productionStage,jdbcType=VARCHAR},
      sample_id = #{sampleId,jdbcType=VARCHAR},
      sample_received_date = #{sampleReceivedDate,jdbcType=TIMESTAMP},
      age_group = #{ageGroup,jdbcType=VARCHAR},
      end_use1 = #{endUse1,jdbcType=VARCHAR},
      special_product_attribute1 = #{specialProductAttribute1,jdbcType=VARCHAR},
      special_product_attribute2 = #{specialProductAttribute2,jdbcType=VARCHAR},
      special_product_attribute3 = #{specialProductAttribute3,jdbcType=VARCHAR},
      construction = #{construction,jdbcType=VARCHAR},
      yarn_count = #{yarnCount,jdbcType=VARCHAR},
      thread_count = #{threadCount,jdbcType=VARCHAR},
      fiber_composition = #{fiberComposition,jdbcType=VARCHAR},
      fiber_weight = #{fiberWeight,jdbcType=VARCHAR},
      fabric_width = #{fabricWidth,jdbcType=VARCHAR},
      season = #{season,jdbcType=VARCHAR},
      `size` = #{size,jdbcType=VARCHAR},
      special_finishing = #{specialFinishing,jdbcType=VARCHAR},
      `collection` = #{collection,jdbcType=VARCHAR},
      care_label_flag = #{careLabelFlag,jdbcType=VARCHAR},
      care_label = #{careLabel,jdbcType=VARCHAR},
      care_label_wording = #{careLabelWording,jdbcType=VARCHAR},
      header_id = #{headerId,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=VARCHAR},
      product_item_no = #{productItemNo,jdbcType=VARCHAR},
      cancel_flag = #{cancelFlag,jdbcType=TINYINT},
      ref_sample_id = #{refSampleId,jdbcType=VARCHAR},
      language_id = #{languageID,jdbcType=INTEGER},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      ref_code6 = #{refCode6,jdbcType=LONGVARCHAR},
      ref_code7 = #{refCode7,jdbcType=LONGVARCHAR},
      ref_code8 = #{refCode8,jdbcType=LONGVARCHAR},
      ref_code9 = #{refCode9,jdbcType=LONGVARCHAR},
      ref_code10 = #{refCode10,jdbcType=LONGVARCHAR},
      special_customer_attribute1 = #{specialCustomerAttribute1,jdbcType=LONGVARCHAR},
      special_customer_attribute2 = #{specialCustomerAttribute2,jdbcType=LONGVARCHAR},
      special_customer_attribute3 = #{specialCustomerAttribute3,jdbcType=LONGVARCHAR},
      special_customer_attribute4 = #{specialCustomerAttribute4,jdbcType=LONGVARCHAR},
      special_customer_attribute5 = #{specialCustomerAttribute5,jdbcType=LONGVARCHAR},
      special_customer_attribute6 = #{specialCustomerAttribute6,jdbcType=LONGVARCHAR},
      special_customer_attribute7 = #{specialCustomerAttribute7,jdbcType=LONGVARCHAR},
      special_customer_attribute8 = #{specialCustomerAttribute8,jdbcType=LONGVARCHAR},
      special_customer_attribute9 = #{specialCustomerAttribute9,jdbcType=LONGVARCHAR},
      special_customer_attribute10 = #{specialCustomerAttribute10,jdbcType=LONGVARCHAR},
      special_customer_attribute11 = #{specialCustomerAttribute11,jdbcType=LONGVARCHAR},
      special_customer_attribute12 = #{specialCustomerAttribute12,jdbcType=LONGVARCHAR},
      special_customer_attribute13 = #{specialCustomerAttribute13,jdbcType=LONGVARCHAR},
      special_customer_attribute14 = #{specialCustomerAttribute14,jdbcType=LONGVARCHAR},
      special_customer_attribute15 = #{specialCustomerAttribute15,jdbcType=LONGVARCHAR},
      special_customer_attribute16 = #{specialCustomerAttribute16,jdbcType=LONGVARCHAR},
      special_customer_attribute17 = #{specialCustomerAttribute17,jdbcType=LONGVARCHAR},
      special_customer_attribute18 = #{specialCustomerAttribute18,jdbcType=LONGVARCHAR},
      special_product_attribute4 = #{specialProductAttribute4,jdbcType=LONGVARCHAR},
      special_product_attribute5 = #{specialProductAttribute5,jdbcType=LONGVARCHAR},
      special_product_attribute6 = #{specialProductAttribute6,jdbcType=LONGVARCHAR},
      special_product_attribute7 = #{specialProductAttribute7,jdbcType=LONGVARCHAR},
      special_product_attribute8 = #{specialProductAttribute8,jdbcType=LONGVARCHAR},
      special_product_attribute9 = #{specialProductAttribute9,jdbcType=LONGVARCHAR},
      special_product_attribute10 = #{specialProductAttribute10,jdbcType=LONGVARCHAR},
      Item_No = #{itemNo,jdbcType=LONGVARCHAR},
      Vendor_No = #{vendorNo,jdbcType=LONGVARCHAR},
      ax_sample_flag = #{axSampleFlag,jdbcType=INTEGER},
      external_sample_id = #{externalSampleId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryProductInfoPO" >
    update tb_enquiry_product
    set enquiry_id = #{enquiryId,jdbcType=VARCHAR},
      product_library_id = #{productLibraryId,jdbcType=VARCHAR},
      careLabel_instance_id = #{carelabelInstanceId,jdbcType=VARCHAR},
      communication_log_id = #{communicationLogId,jdbcType=VARCHAR},
      buyer_organnization1 = #{buyerOrgannization1,jdbcType=VARCHAR},
      buyer_organnization2 = #{buyerOrgannization2,jdbcType=VARCHAR},
      buyer_organnization3 = #{buyerOrgannization3,jdbcType=VARCHAR},
      buyer_organnization_code1 = #{buyerOrgannizationCode1,jdbcType=VARCHAR},
      buyer_organnization_code2 = #{buyerOrgannizationCode2,jdbcType=VARCHAR},
      buyer_organnization_code3 = #{buyerOrgannizationCode3,jdbcType=VARCHAR},
      buyer_aliase = #{buyerAliase,jdbcType=VARCHAR},
      buyer_sourcing_office = #{buyerSourcingOffice,jdbcType=VARCHAR},
      country_of_origin = #{countryOfOrigin,jdbcType=VARCHAR},
      country_of_destination = #{countryOfDestination,jdbcType=VARCHAR},
      dff_form_id = #{dffFormID,jdbcType=VARCHAR},
      supplier = #{supplier,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      factory_id = #{factoryId,jdbcType=VARCHAR},
      factory_name = #{factoryName,jdbcType=VARCHAR},
      first_fpu_no = #{firstFpuNo,jdbcType=VARCHAR},
      first_pass_fpu_no = #{firstPassFpuNo,jdbcType=VARCHAR},
      first_time_application_flag = #{firstTimeApplicationFlag,jdbcType=VARCHAR},
      fpu_no = #{fpuNo,jdbcType=VARCHAR},
      fpu_report_no = #{fpuReportNo,jdbcType=VARCHAR},
      gpu_no = #{gpuNo,jdbcType=VARCHAR},
      lot_no = #{lotNo,jdbcType=VARCHAR},
      no_of_sample = #{noOfSample,jdbcType=INTEGER},
      other_sample_information = #{otherSampleInformation,jdbcType=VARCHAR},
      peformance_code = #{peformanceCode,jdbcType=VARCHAR},
      po_no = #{poNo,jdbcType=VARCHAR},
      previous_report_no = #{previousReportNo,jdbcType=VARCHAR},
      trim_report_no = #{trimReportNo,jdbcType=VARCHAR},
      fabric_report = #{fabricReport,jdbcType=VARCHAR},
      product_category1 = #{productCategory1,jdbcType=VARCHAR},
      product_category2 = #{productCategory2,jdbcType=VARCHAR},
      style_no = #{styleNo,jdbcType=VARCHAR},
      ref_code1 = #{refCode1,jdbcType=VARCHAR},
      ref_code2 = #{refCode2,jdbcType=VARCHAR},
      ref_code3 = #{refCode3,jdbcType=VARCHAR},
      ref_code4 = #{refCode4,jdbcType=VARCHAR},
      ref_code5 = #{refCode5,jdbcType=VARCHAR},
      product_color = #{productColor,jdbcType=VARCHAR},
      product_description = #{productDescription,jdbcType=VARCHAR},
      production_stage = #{productionStage,jdbcType=VARCHAR},
      sample_id = #{sampleId,jdbcType=VARCHAR},
      sample_received_date = #{sampleReceivedDate,jdbcType=TIMESTAMP},
      age_group = #{ageGroup,jdbcType=VARCHAR},
      end_use1 = #{endUse1,jdbcType=VARCHAR},
      special_product_attribute1 = #{specialProductAttribute1,jdbcType=VARCHAR},
      special_product_attribute2 = #{specialProductAttribute2,jdbcType=VARCHAR},
      special_product_attribute3 = #{specialProductAttribute3,jdbcType=VARCHAR},
      construction = #{construction,jdbcType=VARCHAR},
      yarn_count = #{yarnCount,jdbcType=VARCHAR},
      thread_count = #{threadCount,jdbcType=VARCHAR},
      fiber_composition = #{fiberComposition,jdbcType=VARCHAR},
      fiber_weight = #{fiberWeight,jdbcType=VARCHAR},
      fabric_width = #{fabricWidth,jdbcType=VARCHAR},
      season = #{season,jdbcType=VARCHAR},
      `size` = #{size,jdbcType=VARCHAR},
      special_finishing = #{specialFinishing,jdbcType=VARCHAR},
      `collection` = #{collection,jdbcType=VARCHAR},
      care_label_flag = #{careLabelFlag,jdbcType=VARCHAR},
      care_label = #{careLabel,jdbcType=VARCHAR},
      care_label_wording = #{careLabelWording,jdbcType=VARCHAR},
      header_id = #{headerId,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=VARCHAR},
      product_item_no = #{productItemNo,jdbcType=VARCHAR},
      cancel_flag = #{cancelFlag,jdbcType=TINYINT},
      ref_sample_id = #{refSampleId,jdbcType=VARCHAR},
      language_id = #{languageID,jdbcType=INTEGER},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      ax_sample_flag = #{axSampleFlag,jdbcType=INTEGER},
      external_sample_id = #{externalSampleId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>