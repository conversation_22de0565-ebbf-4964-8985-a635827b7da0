<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.IntegrationCustomerConfigInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoPO" >
    <id column="Id" property="id" jdbcType="INTEGER" />
    <result column="ProductLineId" property="productlineid" jdbcType="INTEGER" />
    <result column="CustomerGroupCode" property="customergroupcode" jdbcType="VARCHAR" />
    <result column="RefSystemId" property="refsystemid" jdbcType="INTEGER" />
    <result column="RefSystemName" property="refsystemname" jdbcType="VARCHAR" />
    <result column="RefObjectType" property="refobjecttype" jdbcType="INTEGER" />
    <result column="SceneType" property="scenetype" jdbcType="INTEGER" />
    <result column="BindRule" property="bindrule" jdbcType="INTEGER" />
    <result column="BindOrderRule" property="bindorderrule" jdbcType="INTEGER" />
    <result column="SendType" property="sendtype" jdbcType="INTEGER" />
    <result column="Desc" property="desc" jdbcType="VARCHAR" />
    <result column="DffFormGroupId" property="dffformgroupid" jdbcType="VARCHAR" />
    <result column="GridFormGroupId" property="gridformgroupid" jdbcType="VARCHAR" />
    <result column="RefSystemLabelName" property="refsystemlabelname" jdbcType="VARCHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="RefCheck" property="refcheck" jdbcType="INTEGER" />
    <result column="CreateDate" property="createdate" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoPO" extends="BaseResultMap" >
    <result column="CustomerRules" property="customerrules" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, ProductLineId, CustomerGroupCode, RefSystemId, RefSystemName, RefObjectType, 
    SceneType, BindRule, BindOrderRule, SendType, `Desc`, DffFormGroupId, GridFormGroupId, 
    RefSystemLabelName, `Status`, RefCheck, CreateDate
  </sql>
  <sql id="Blob_Column_List" >
    CustomerRules
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_integration_customer_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_integration_customer_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_integration_customer_config
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_integration_customer_config
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoExample" >
    delete from tb_integration_customer_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoPO" >
    insert into tb_integration_customer_config (Id, ProductLineId, CustomerGroupCode, 
      RefSystemId, RefSystemName, RefObjectType, 
      SceneType, BindRule, BindOrderRule, 
      SendType, `Desc`, DffFormGroupId, 
      GridFormGroupId, RefSystemLabelName, `Status`, 
      RefCheck, CreateDate, CustomerRules
      )
    values (#{id,jdbcType=INTEGER}, #{productlineid,jdbcType=INTEGER}, #{customergroupcode,jdbcType=VARCHAR}, 
      #{refsystemid,jdbcType=INTEGER}, #{refsystemname,jdbcType=VARCHAR}, #{refobjecttype,jdbcType=INTEGER}, 
      #{scenetype,jdbcType=INTEGER}, #{bindrule,jdbcType=INTEGER}, #{bindorderrule,jdbcType=INTEGER}, 
      #{sendtype,jdbcType=INTEGER}, #{desc,jdbcType=VARCHAR}, #{dffformgroupid,jdbcType=VARCHAR}, 
      #{gridformgroupid,jdbcType=VARCHAR}, #{refsystemlabelname,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{refcheck,jdbcType=INTEGER}, #{createdate,jdbcType=TIMESTAMP}, #{customerrules,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoPO" >
    insert into tb_integration_customer_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="productlineid != null" >
        ProductLineId,
      </if>
      <if test="customergroupcode != null" >
        CustomerGroupCode,
      </if>
      <if test="refsystemid != null" >
        RefSystemId,
      </if>
      <if test="refsystemname != null" >
        RefSystemName,
      </if>
      <if test="refobjecttype != null" >
        RefObjectType,
      </if>
      <if test="scenetype != null" >
        SceneType,
      </if>
      <if test="bindrule != null" >
        BindRule,
      </if>
      <if test="bindorderrule != null" >
        BindOrderRule,
      </if>
      <if test="sendtype != null" >
        SendType,
      </if>
      <if test="desc != null" >
        `Desc`,
      </if>
      <if test="dffformgroupid != null" >
        DffFormGroupId,
      </if>
      <if test="gridformgroupid != null" >
        GridFormGroupId,
      </if>
      <if test="refsystemlabelname != null" >
        RefSystemLabelName,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="refcheck != null" >
        RefCheck,
      </if>
      <if test="createdate != null" >
        CreateDate,
      </if>
      <if test="customerrules != null" >
        CustomerRules,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productlineid != null" >
        #{productlineid,jdbcType=INTEGER},
      </if>
      <if test="customergroupcode != null" >
        #{customergroupcode,jdbcType=VARCHAR},
      </if>
      <if test="refsystemid != null" >
        #{refsystemid,jdbcType=INTEGER},
      </if>
      <if test="refsystemname != null" >
        #{refsystemname,jdbcType=VARCHAR},
      </if>
      <if test="refobjecttype != null" >
        #{refobjecttype,jdbcType=INTEGER},
      </if>
      <if test="scenetype != null" >
        #{scenetype,jdbcType=INTEGER},
      </if>
      <if test="bindrule != null" >
        #{bindrule,jdbcType=INTEGER},
      </if>
      <if test="bindorderrule != null" >
        #{bindorderrule,jdbcType=INTEGER},
      </if>
      <if test="sendtype != null" >
        #{sendtype,jdbcType=INTEGER},
      </if>
      <if test="desc != null" >
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="dffformgroupid != null" >
        #{dffformgroupid,jdbcType=VARCHAR},
      </if>
      <if test="gridformgroupid != null" >
        #{gridformgroupid,jdbcType=VARCHAR},
      </if>
      <if test="refsystemlabelname != null" >
        #{refsystemlabelname,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="refcheck != null" >
        #{refcheck,jdbcType=INTEGER},
      </if>
      <if test="createdate != null" >
        #{createdate,jdbcType=TIMESTAMP},
      </if>
      <if test="customerrules != null" >
        #{customerrules,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_integration_customer_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_integration_customer_config
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.productlineid != null" >
        ProductLineId = #{record.productlineid,jdbcType=INTEGER},
      </if>
      <if test="record.customergroupcode != null" >
        CustomerGroupCode = #{record.customergroupcode,jdbcType=VARCHAR},
      </if>
      <if test="record.refsystemid != null" >
        RefSystemId = #{record.refsystemid,jdbcType=INTEGER},
      </if>
      <if test="record.refsystemname != null" >
        RefSystemName = #{record.refsystemname,jdbcType=VARCHAR},
      </if>
      <if test="record.refobjecttype != null" >
        RefObjectType = #{record.refobjecttype,jdbcType=INTEGER},
      </if>
      <if test="record.scenetype != null" >
        SceneType = #{record.scenetype,jdbcType=INTEGER},
      </if>
      <if test="record.bindrule != null" >
        BindRule = #{record.bindrule,jdbcType=INTEGER},
      </if>
      <if test="record.bindorderrule != null" >
        BindOrderRule = #{record.bindorderrule,jdbcType=INTEGER},
      </if>
      <if test="record.sendtype != null" >
        SendType = #{record.sendtype,jdbcType=INTEGER},
      </if>
      <if test="record.desc != null" >
        `Desc` = #{record.desc,jdbcType=VARCHAR},
      </if>
      <if test="record.dffformgroupid != null" >
        DffFormGroupId = #{record.dffformgroupid,jdbcType=VARCHAR},
      </if>
      <if test="record.gridformgroupid != null" >
        GridFormGroupId = #{record.gridformgroupid,jdbcType=VARCHAR},
      </if>
      <if test="record.refsystemlabelname != null" >
        RefSystemLabelName = #{record.refsystemlabelname,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.refcheck != null" >
        RefCheck = #{record.refcheck,jdbcType=INTEGER},
      </if>
      <if test="record.createdate != null" >
        CreateDate = #{record.createdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.customerrules != null" >
        CustomerRules = #{record.customerrules,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_integration_customer_config
    set Id = #{record.id,jdbcType=INTEGER},
      ProductLineId = #{record.productlineid,jdbcType=INTEGER},
      CustomerGroupCode = #{record.customergroupcode,jdbcType=VARCHAR},
      RefSystemId = #{record.refsystemid,jdbcType=INTEGER},
      RefSystemName = #{record.refsystemname,jdbcType=VARCHAR},
      RefObjectType = #{record.refobjecttype,jdbcType=INTEGER},
      SceneType = #{record.scenetype,jdbcType=INTEGER},
      BindRule = #{record.bindrule,jdbcType=INTEGER},
      BindOrderRule = #{record.bindorderrule,jdbcType=INTEGER},
      SendType = #{record.sendtype,jdbcType=INTEGER},
      `Desc` = #{record.desc,jdbcType=VARCHAR},
      DffFormGroupId = #{record.dffformgroupid,jdbcType=VARCHAR},
      GridFormGroupId = #{record.gridformgroupid,jdbcType=VARCHAR},
      RefSystemLabelName = #{record.refsystemlabelname,jdbcType=VARCHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      RefCheck = #{record.refcheck,jdbcType=INTEGER},
      CreateDate = #{record.createdate,jdbcType=TIMESTAMP},
      CustomerRules = #{record.customerrules,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_integration_customer_config
    set Id = #{record.id,jdbcType=INTEGER},
      ProductLineId = #{record.productlineid,jdbcType=INTEGER},
      CustomerGroupCode = #{record.customergroupcode,jdbcType=VARCHAR},
      RefSystemId = #{record.refsystemid,jdbcType=INTEGER},
      RefSystemName = #{record.refsystemname,jdbcType=VARCHAR},
      RefObjectType = #{record.refobjecttype,jdbcType=INTEGER},
      SceneType = #{record.scenetype,jdbcType=INTEGER},
      BindRule = #{record.bindrule,jdbcType=INTEGER},
      BindOrderRule = #{record.bindorderrule,jdbcType=INTEGER},
      SendType = #{record.sendtype,jdbcType=INTEGER},
      `Desc` = #{record.desc,jdbcType=VARCHAR},
      DffFormGroupId = #{record.dffformgroupid,jdbcType=VARCHAR},
      GridFormGroupId = #{record.gridformgroupid,jdbcType=VARCHAR},
      RefSystemLabelName = #{record.refsystemlabelname,jdbcType=VARCHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      RefCheck = #{record.refcheck,jdbcType=INTEGER},
      CreateDate = #{record.createdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoPO" >
    update tb_integration_customer_config
    <set >
      <if test="productlineid != null" >
        ProductLineId = #{productlineid,jdbcType=INTEGER},
      </if>
      <if test="customergroupcode != null" >
        CustomerGroupCode = #{customergroupcode,jdbcType=VARCHAR},
      </if>
      <if test="refsystemid != null" >
        RefSystemId = #{refsystemid,jdbcType=INTEGER},
      </if>
      <if test="refsystemname != null" >
        RefSystemName = #{refsystemname,jdbcType=VARCHAR},
      </if>
      <if test="refobjecttype != null" >
        RefObjectType = #{refobjecttype,jdbcType=INTEGER},
      </if>
      <if test="scenetype != null" >
        SceneType = #{scenetype,jdbcType=INTEGER},
      </if>
      <if test="bindrule != null" >
        BindRule = #{bindrule,jdbcType=INTEGER},
      </if>
      <if test="bindorderrule != null" >
        BindOrderRule = #{bindorderrule,jdbcType=INTEGER},
      </if>
      <if test="sendtype != null" >
        SendType = #{sendtype,jdbcType=INTEGER},
      </if>
      <if test="desc != null" >
        `Desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="dffformgroupid != null" >
        DffFormGroupId = #{dffformgroupid,jdbcType=VARCHAR},
      </if>
      <if test="gridformgroupid != null" >
        GridFormGroupId = #{gridformgroupid,jdbcType=VARCHAR},
      </if>
      <if test="refsystemlabelname != null" >
        RefSystemLabelName = #{refsystemlabelname,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="refcheck != null" >
        RefCheck = #{refcheck,jdbcType=INTEGER},
      </if>
      <if test="createdate != null" >
        CreateDate = #{createdate,jdbcType=TIMESTAMP},
      </if>
      <if test="customerrules != null" >
        CustomerRules = #{customerrules,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoPO" >
    update tb_integration_customer_config
    set ProductLineId = #{productlineid,jdbcType=INTEGER},
      CustomerGroupCode = #{customergroupcode,jdbcType=VARCHAR},
      RefSystemId = #{refsystemid,jdbcType=INTEGER},
      RefSystemName = #{refsystemname,jdbcType=VARCHAR},
      RefObjectType = #{refobjecttype,jdbcType=INTEGER},
      SceneType = #{scenetype,jdbcType=INTEGER},
      BindRule = #{bindrule,jdbcType=INTEGER},
      BindOrderRule = #{bindorderrule,jdbcType=INTEGER},
      SendType = #{sendtype,jdbcType=INTEGER},
      `Desc` = #{desc,jdbcType=VARCHAR},
      DffFormGroupId = #{dffformgroupid,jdbcType=VARCHAR},
      GridFormGroupId = #{gridformgroupid,jdbcType=VARCHAR},
      RefSystemLabelName = #{refsystemlabelname,jdbcType=VARCHAR},
      `Status` = #{status,jdbcType=INTEGER},
      RefCheck = #{refcheck,jdbcType=INTEGER},
      CreateDate = #{createdate,jdbcType=TIMESTAMP},
      CustomerRules = #{customerrules,jdbcType=LONGVARCHAR}
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationCustomerConfigInfoPO" >
    update tb_integration_customer_config
    set ProductLineId = #{productlineid,jdbcType=INTEGER},
      CustomerGroupCode = #{customergroupcode,jdbcType=VARCHAR},
      RefSystemId = #{refsystemid,jdbcType=INTEGER},
      RefSystemName = #{refsystemname,jdbcType=VARCHAR},
      RefObjectType = #{refobjecttype,jdbcType=INTEGER},
      SceneType = #{scenetype,jdbcType=INTEGER},
      BindRule = #{bindrule,jdbcType=INTEGER},
      BindOrderRule = #{bindorderrule,jdbcType=INTEGER},
      SendType = #{sendtype,jdbcType=INTEGER},
      `Desc` = #{desc,jdbcType=VARCHAR},
      DffFormGroupId = #{dffformgroupid,jdbcType=VARCHAR},
      GridFormGroupId = #{gridformgroupid,jdbcType=VARCHAR},
      RefSystemLabelName = #{refsystemlabelname,jdbcType=VARCHAR},
      `Status` = #{status,jdbcType=INTEGER},
      RefCheck = #{refcheck,jdbcType=INTEGER},
      CreateDate = #{createdate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=INTEGER}
  </update>
</mapper>