<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EnquiryTrfRelationshipInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="EnquiryId" property="enquiryId" jdbcType="VARCHAR" />
    <result column="RefSystemId" property="refSystemId" jdbcType="INTEGER" />
    <result column="RefNo" property="refNo" jdbcType="VARCHAR" />
    <result column="RefObjectType" property="refObjectType" jdbcType="INTEGER" />
    <result column="RefObjectId" property="refObjectId" jdbcType="VARCHAR" />
    <result column="ExtObjectId" property="extObjectId" jdbcType="VARCHAR" />
    <result column="ExternalOrderNo" property="externalOrderNo" jdbcType="VARCHAR" />
    <result column="CreateType" property="createType" jdbcType="INTEGER" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ExtData" property="extData" jdbcType="VARCHAR" />
    <result column="IntegrationChannel" property="integrationChannel" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, EnquiryId, RefSystemId, RefNo, RefObjectType, RefObjectId, ExtObjectId, ExternalOrderNo, 
    CreateType, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate,ExtData,IntegrationChannel
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_enquiry_trf_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_enquiry_trf_relationship
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_enquiry_trf_relationship
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoExample" >
    delete from tb_enquiry_trf_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoPO" >
    insert into tb_enquiry_trf_relationship (Id, EnquiryId, RefSystemId, 
      RefNo, RefObjectType, RefObjectId, 
      ExtObjectId, ExternalOrderNo, CreateType, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate,extData,IntegrationChannel)
    values (#{id,jdbcType=BIGINT}, #{enquiryId,jdbcType=VARCHAR}, #{refSystemId,jdbcType=INTEGER}, 
      #{refNo,jdbcType=VARCHAR}, #{refObjectType,jdbcType=INTEGER}, #{refObjectId,jdbcType=VARCHAR}, 
      #{extObjectId,jdbcType=VARCHAR}, #{externalOrderNo,jdbcType=VARCHAR}, #{createType,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP},#{extData,jdbcType=LONGVARCHAR}, #{integrationChannel,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoPO" >
    insert into tb_enquiry_trf_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="enquiryId != null" >
        EnquiryId,
      </if>
      <if test="refSystemId != null" >
        RefSystemId,
      </if>
      <if test="refNo != null" >
        RefNo,
      </if>
      <if test="refObjectType != null" >
        RefObjectType,
      </if>
      <if test="refObjectId != null" >
        RefObjectId,
      </if>
      <if test="extObjectId != null" >
        ExtObjectId,
      </if>
      <if test="externalOrderNo != null" >
        ExternalOrderNo,
      </if>
      <if test="createType != null" >
        CreateType,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="enquiryId != null" >
        #{enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="refSystemId != null" >
        #{refSystemId,jdbcType=INTEGER},
      </if>
      <if test="refNo != null" >
        #{refNo,jdbcType=VARCHAR},
      </if>
      <if test="refObjectType != null" >
        #{refObjectType,jdbcType=INTEGER},
      </if>
      <if test="refObjectId != null" >
        #{refObjectId,jdbcType=VARCHAR},
      </if>
      <if test="extObjectId != null" >
        #{extObjectId,jdbcType=VARCHAR},
      </if>
      <if test="externalOrderNo != null" >
        #{externalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="createType != null" >
        #{createType,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_enquiry_trf_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_enquiry_trf_relationship
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.enquiryId != null" >
        EnquiryId = #{record.enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="record.refSystemId != null" >
        RefSystemId = #{record.refSystemId,jdbcType=INTEGER},
      </if>
      <if test="record.refNo != null" >
        RefNo = #{record.refNo,jdbcType=VARCHAR},
      </if>
      <if test="record.refObjectType != null" >
        RefObjectType = #{record.refObjectType,jdbcType=INTEGER},
      </if>
      <if test="record.refObjectId != null" >
        RefObjectId = #{record.refObjectId,jdbcType=VARCHAR},
      </if>
      <if test="record.extObjectId != null" >
        ExtObjectId = #{record.extObjectId,jdbcType=VARCHAR},
      </if>
      <if test="record.externalOrderNo != null" >
        ExternalOrderNo = #{record.externalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createType != null" >
        CreateType = #{record.createType,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.integrationChannel != null" >
        IntegrationChannel = #{record.integrationChannel,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_enquiry_trf_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      EnquiryId = #{record.enquiryId,jdbcType=VARCHAR},
      RefSystemId = #{record.refSystemId,jdbcType=INTEGER},
      RefNo = #{record.refNo,jdbcType=VARCHAR},
      RefObjectType = #{record.refObjectType,jdbcType=INTEGER},
      RefObjectId = #{record.refObjectId,jdbcType=VARCHAR},
      ExtObjectId = #{record.extObjectId,jdbcType=VARCHAR},
      ExternalOrderNo = #{record.externalOrderNo,jdbcType=VARCHAR},
      CreateType = #{record.createType,jdbcType=INTEGER},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      IntegrationChannel = #{record.integrationChannel,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoPO" >
    update tb_enquiry_trf_relationship
    <set >
      <if test="enquiryId != null" >
        EnquiryId = #{enquiryId,jdbcType=VARCHAR},
      </if>
      <if test="refSystemId != null" >
        RefSystemId = #{refSystemId,jdbcType=INTEGER},
      </if>
      <if test="refNo != null" >
        RefNo = #{refNo,jdbcType=VARCHAR},
      </if>
      <if test="refObjectType != null" >
        RefObjectType = #{refObjectType,jdbcType=INTEGER},
      </if>
      <if test="refObjectId != null" >
        RefObjectId = #{refObjectId,jdbcType=VARCHAR},
      </if>
      <if test="extObjectId != null" >
        ExtObjectId = #{extObjectId,jdbcType=VARCHAR},
      </if>
      <if test="externalOrderNo != null" >
        ExternalOrderNo = #{externalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="createType != null" >
        CreateType = #{createType,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="integrationChannel != null" >
        IntegrationChannel = #{integrationChannel,jdbcType=VARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTrfRelationshipInfoPO" >
    update tb_enquiry_trf_relationship
    set EnquiryId = #{enquiryId,jdbcType=VARCHAR},
      RefSystemId = #{refSystemId,jdbcType=INTEGER},
      RefNo = #{refNo,jdbcType=VARCHAR},
      RefObjectType = #{refObjectType,jdbcType=INTEGER},
      RefObjectId = #{refObjectId,jdbcType=VARCHAR},
      ExtObjectId = #{extObjectId,jdbcType=VARCHAR},
      ExternalOrderNo = #{externalOrderNo,jdbcType=VARCHAR},
      CreateType = #{createType,jdbcType=INTEGER},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      IntegrationChannel = #{integrationChannel,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>