<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.KeyAccountSettingMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="BUCode" property="BUCode" jdbcType="VARCHAR" />
    <result column="LocationCode" property="locationCode" jdbcType="VARCHAR" />
    <result column="CustomerID" property="customerID" jdbcType="VARCHAR" />
    <result column="AccountID" property="accountID" jdbcType="BIGINT" />
    <result column="BossCustomerNumber" property="bossCustomerNumber" jdbcType="BIGINT" />
    <result column="CustomerNameEN" property="customerNameEN" jdbcType="VARCHAR" />
    <result column="KeyAccountFlag" property="keyAccountFlag" jdbcType="TINYINT" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="manufacture" property="manufacture" jdbcType="TINYINT" />
    <result column="supplier" property="supplier" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, BUCode, LocationCode, CustomerID, AccountID, BossCustomerNumber, CustomerNameEN, 
    KeyAccountFlag, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, 
    manufacture, supplier
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_key_account_setting
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_key_account_setting
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_key_account_setting
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingExample" >
    delete from tb_key_account_setting
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingPO" >
    insert into tb_key_account_setting (ID, BUCode, LocationCode, 
      CustomerID, AccountID, BossCustomerNumber, 
      CustomerNameEN, KeyAccountFlag, ActiveIndicator, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate, manufacture, supplier
      )
    values (#{ID,jdbcType=VARCHAR}, #{BUCode,jdbcType=VARCHAR}, #{locationCode,jdbcType=VARCHAR}, 
      #{customerID,jdbcType=VARCHAR}, #{accountID,jdbcType=BIGINT}, #{bossCustomerNumber,jdbcType=BIGINT}, 
      #{customerNameEN,jdbcType=VARCHAR}, #{keyAccountFlag,jdbcType=TINYINT}, #{activeIndicator,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{manufacture,jdbcType=TINYINT}, #{supplier,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingPO" >
    insert into tb_key_account_setting
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="BUCode != null" >
        BUCode,
      </if>
      <if test="locationCode != null" >
        LocationCode,
      </if>
      <if test="customerID != null" >
        CustomerID,
      </if>
      <if test="accountID != null" >
        AccountID,
      </if>
      <if test="bossCustomerNumber != null" >
        BossCustomerNumber,
      </if>
      <if test="customerNameEN != null" >
        CustomerNameEN,
      </if>
      <if test="keyAccountFlag != null" >
        KeyAccountFlag,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="manufacture != null" >
        manufacture,
      </if>
      <if test="supplier != null" >
        supplier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="BUCode != null" >
        #{BUCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="customerID != null" >
        #{customerID,jdbcType=VARCHAR},
      </if>
      <if test="accountID != null" >
        #{accountID,jdbcType=BIGINT},
      </if>
      <if test="bossCustomerNumber != null" >
        #{bossCustomerNumber,jdbcType=BIGINT},
      </if>
      <if test="customerNameEN != null" >
        #{customerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="keyAccountFlag != null" >
        #{keyAccountFlag,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="manufacture != null" >
        #{manufacture,jdbcType=TINYINT},
      </if>
      <if test="supplier != null" >
        #{supplier,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingExample" resultType="java.lang.Integer" >
    select count(*) from tb_key_account_setting
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_key_account_setting
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.BUCode != null" >
        BUCode = #{record.BUCode,jdbcType=VARCHAR},
      </if>
      <if test="record.locationCode != null" >
        LocationCode = #{record.locationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerID != null" >
        CustomerID = #{record.customerID,jdbcType=VARCHAR},
      </if>
      <if test="record.accountID != null" >
        AccountID = #{record.accountID,jdbcType=BIGINT},
      </if>
      <if test="record.bossCustomerNumber != null" >
        BossCustomerNumber = #{record.bossCustomerNumber,jdbcType=BIGINT},
      </if>
      <if test="record.customerNameEN != null" >
        CustomerNameEN = #{record.customerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="record.keyAccountFlag != null" >
        KeyAccountFlag = #{record.keyAccountFlag,jdbcType=TINYINT},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.manufacture != null" >
        manufacture = #{record.manufacture,jdbcType=TINYINT},
      </if>
      <if test="record.supplier != null" >
        supplier = #{record.supplier,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_key_account_setting
    set ID = #{record.ID,jdbcType=VARCHAR},
      BUCode = #{record.BUCode,jdbcType=VARCHAR},
      LocationCode = #{record.locationCode,jdbcType=VARCHAR},
      CustomerID = #{record.customerID,jdbcType=VARCHAR},
      AccountID = #{record.accountID,jdbcType=BIGINT},
      BossCustomerNumber = #{record.bossCustomerNumber,jdbcType=BIGINT},
      CustomerNameEN = #{record.customerNameEN,jdbcType=VARCHAR},
      KeyAccountFlag = #{record.keyAccountFlag,jdbcType=TINYINT},
      ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      manufacture = #{record.manufacture,jdbcType=TINYINT},
      supplier = #{record.supplier,jdbcType=TINYINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingPO" >
    update tb_key_account_setting
    <set >
      <if test="BUCode != null" >
        BUCode = #{BUCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        LocationCode = #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="customerID != null" >
        CustomerID = #{customerID,jdbcType=VARCHAR},
      </if>
      <if test="accountID != null" >
        AccountID = #{accountID,jdbcType=BIGINT},
      </if>
      <if test="bossCustomerNumber != null" >
        BossCustomerNumber = #{bossCustomerNumber,jdbcType=BIGINT},
      </if>
      <if test="customerNameEN != null" >
        CustomerNameEN = #{customerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="keyAccountFlag != null" >
        KeyAccountFlag = #{keyAccountFlag,jdbcType=TINYINT},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="manufacture != null" >
        manufacture = #{manufacture,jdbcType=TINYINT},
      </if>
      <if test="supplier != null" >
        supplier = #{supplier,jdbcType=TINYINT},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.KeyAccountSettingPO" >
    update tb_key_account_setting
    set BUCode = #{BUCode,jdbcType=VARCHAR},
      LocationCode = #{locationCode,jdbcType=VARCHAR},
      CustomerID = #{customerID,jdbcType=VARCHAR},
      AccountID = #{accountID,jdbcType=BIGINT},
      BossCustomerNumber = #{bossCustomerNumber,jdbcType=BIGINT},
      CustomerNameEN = #{customerNameEN,jdbcType=VARCHAR},
      KeyAccountFlag = #{keyAccountFlag,jdbcType=TINYINT},
      ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      manufacture = #{manufacture,jdbcType=TINYINT},
      supplier = #{supplier,jdbcType=TINYINT}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>