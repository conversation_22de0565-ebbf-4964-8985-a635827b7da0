<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.GeneralOrderInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="BUID" property="BUID" jdbcType="INTEGER" />
    <result column="LocationID" property="locationID" jdbcType="INTEGER" />
    <result column="BUCode" property="BUCode" jdbcType="VARCHAR" />
    <result column="LocationCode" property="locationCode" jdbcType="VARCHAR" />
    <result column="GroupID" property="groupID" jdbcType="VARCHAR" />
    <result column="EnquiryNo" property="enquiryNo" jdbcType="VARCHAR" />
    <result column="EnquiryID" property="enquiryID" jdbcType="VARCHAR" />
    <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="OrderStatus" property="orderStatus" jdbcType="INTEGER" />
    <result column="OrderType" property="orderType" jdbcType="INTEGER" />
    <result column="OrderCategory" property="orderCategory" jdbcType="TINYINT" />
    <result column="ToBossFlag" property="toBossFlag" jdbcType="INTEGER" />
    <result column="PayStatus" property="payStatus" jdbcType="INTEGER" />
    <result column="ModifyFlag" property="modifyFlag" jdbcType="INTEGER" />
    <result column="ToTestFlag" property="toTestFlag" jdbcType="INTEGER" />
    <result column="SelfTestFlag" property="selfTestFlag" jdbcType="INTEGER" />
    <result column="ApproveFlag" property="approveFlag" jdbcType="INTEGER" />
    <result column="ServiceLevel" property="serviceLevel" jdbcType="INTEGER" />
    <result column="PendingFlag" property="pendingFlag" jdbcType="BIT" />
    <result column="IsCopyOrderFlag" property="isCopyOrderFlag" jdbcType="BIT" />
    <result column="Discount" property="discount" jdbcType="DECIMAL" />
    <result column="BeforeTaxPrice" property="beforeTaxPrice" jdbcType="DECIMAL" />
    <result column="AfterTaxPrice" property="afterTaxPrice" jdbcType="DECIMAL" />
    <result column="TotalPrice" property="totalPrice" jdbcType="DECIMAL" />
    <result column="CurrencyID" property="currencyID" jdbcType="VARCHAR" />
    <result column="FaPiaoType" property="faPiaoType" jdbcType="INTEGER" />
    <result column="FaPiaoTitleCN" property="faPiaoTitleCN" jdbcType="VARCHAR" />
    <result column="FaPiaoTitleEN" property="faPiaoTitleEN" jdbcType="VARCHAR" />
    <result column="BranchName" property="branchName" jdbcType="VARCHAR" />
    <result column="BankAccount" property="bankAccount" jdbcType="VARCHAR" />
    <result column="BankName" property="bankName" jdbcType="VARCHAR" />
    <result column="TAT" property="TAT" jdbcType="INTEGER" />
    <result column="ServiceStartDate" property="serviceStartDate" jdbcType="TIMESTAMP" />
    <result column="ExpectedOrderDueDate" property="expectedOrderDueDate" jdbcType="TIMESTAMP" />
    <result column="ReportExpectDueDate" property="reportExpectDueDate" jdbcType="TIMESTAMP" />
    <result column="ApproverUserID" property="approverUserID" jdbcType="VARCHAR" />
    <result column="ExpressNo" property="expressNo" jdbcType="VARCHAR" />
    <result column="AssignUserID" property="assignUserID" jdbcType="VARCHAR" />
    <result column="AssignUserName" property="assignUserName" jdbcType="VARCHAR" />
    <result column="Remark" property="remark" jdbcType="VARCHAR" />
    <result column="OrderSource" property="orderSource" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifitedDate" property="modifitedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="KACustomerDeptCode" property="KACustomerDeptCode" jdbcType="VARCHAR" />
    <result column="ToDMFlag" property="toDMFlag" jdbcType="INTEGER" />
    <result column="OldOrderNo" property="oldOrderNo" jdbcType="VARCHAR" />
    <result column="OrganizationName" property="organizationName" jdbcType="VARCHAR" />
    <result column="LegalEntityCode" property="legalEntityCode" jdbcType="VARCHAR" />
    <result column="OrderConfirmDate" property="orderConfirmDate" jdbcType="TIMESTAMP" />
    <result column="operationType" property="operationType" jdbcType="INTEGER" />
    <result column="postfix" property="postfix" jdbcType="VARCHAR" />
    <result column="versionId" property="versionId" jdbcType="BIGINT" />
    <result column="suffixNum" property="suffixNum" jdbcType="VARCHAR" />
    <result column="MatrixNo" property="matrixNo" jdbcType="VARCHAR" />
    <result column="ToStarLims" property="toStarLims" jdbcType="INTEGER" />
    <result column="CertificateProgram" property="certificateProgram" jdbcType="VARCHAR" />
    <result column="SampleReceiveDate" property="sampleReceiveDate" jdbcType="TIMESTAMP" />
    <result column="DeliveryApproveStatus" property="deliveryApproveStatus" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, BUID, LocationID, BUCode, LocationCode, GroupID, EnquiryNo, EnquiryID, OrderNo, 
    OrderStatus, OrderType, OrderCategory, ToBossFlag, PayStatus, ModifyFlag, ToTestFlag, 
    SelfTestFlag, ApproveFlag, ServiceLevel, PendingFlag, IsCopyOrderFlag, Discount, 
    BeforeTaxPrice, AfterTaxPrice, TotalPrice, CurrencyID, FaPiaoType, FaPiaoTitleCN, 
    FaPiaoTitleEN, BranchName, BankAccount, BankName, TAT, ServiceStartDate, ExpectedOrderDueDate, 
    ReportExpectDueDate, ApproverUserID, ExpressNo, AssignUserID, AssignUserName, Remark, 
    OrderSource, ActiveIndicator, CreatedDate, CreatedBy, ModifitedDate, ModifiedBy, 
    KACustomerDeptCode, ToDMFlag, OldOrderNo, OrganizationName, LegalEntityCode, OrderConfirmDate, 
    operationType, `postfix`, versionId, suffixNum, MatrixNo,
    ToStarLims, CertificateProgram, SampleReceiveDate, DeliveryApproveStatus
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_general_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_general_order
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_general_order
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoExample" >
    delete from tb_general_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoPO" >
    insert into tb_general_order (ID, BUID, LocationID, labId,
      BUCode, LocationCode, labCode,GroupID,
      EnquiryNo, EnquiryID, OrderNo, 
      OrderStatus, OrderType, OrderCategory, 
      ToBossFlag, PayStatus, ModifyFlag, 
      ToTestFlag, SelfTestFlag, ApproveFlag, 
      ServiceLevel, PendingFlag, IsCopyOrderFlag, 
      Discount, BeforeTaxPrice, AfterTaxPrice, 
      TotalPrice, CurrencyID, FaPiaoType, 
      FaPiaoTitleCN, FaPiaoTitleEN, BranchName, 
      BankAccount, BankName, TAT, 
      ServiceStartDate, ExpectedOrderDueDate, 
      ReportExpectDueDate, ApproverUserID, 
      ExpressNo, AssignUserID, AssignUserName, 
      Remark, OrderSource, ActiveIndicator, 
      CreatedDate, CreatedBy, ModifitedDate, 
      ModifiedBy, KACustomerDeptCode, ToDMFlag, 
      OldOrderNo, OrganizationName, LegalEntityCode, 
      OrderConfirmDate, operationType, `postfix`, 
      versionId, suffixNum,
      MatrixNo, ToStarLims, CertificateProgram, 
      SampleReceiveDate, DeliveryApproveStatus)
    values (#{ID,jdbcType=VARCHAR}, #{BUID,jdbcType=INTEGER}, #{locationID,jdbcType=INTEGER}, #{labId,jdbcType=INTEGER},
      #{BUCode,jdbcType=VARCHAR}, #{locationCode,jdbcType=VARCHAR}, #{labCode,jdbcType=VARCHAR}, #{groupID,jdbcType=VARCHAR},
      #{enquiryNo,jdbcType=VARCHAR}, #{enquiryID,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER}, #{orderCategory,jdbcType=TINYINT}, 
      #{toBossFlag,jdbcType=INTEGER}, #{payStatus,jdbcType=INTEGER}, #{modifyFlag,jdbcType=INTEGER}, 
      #{toTestFlag,jdbcType=INTEGER}, #{selfTestFlag,jdbcType=INTEGER}, #{approveFlag,jdbcType=INTEGER}, 
      #{serviceLevel,jdbcType=INTEGER}, #{pendingFlag,jdbcType=BIT}, #{isCopyOrderFlag,jdbcType=BIT}, 
      #{discount,jdbcType=DECIMAL}, #{beforeTaxPrice,jdbcType=DECIMAL}, #{afterTaxPrice,jdbcType=DECIMAL}, 
      #{totalPrice,jdbcType=DECIMAL}, #{currencyID,jdbcType=VARCHAR}, #{faPiaoType,jdbcType=INTEGER}, 
      #{faPiaoTitleCN,jdbcType=VARCHAR}, #{faPiaoTitleEN,jdbcType=VARCHAR}, #{branchName,jdbcType=VARCHAR}, 
      #{bankAccount,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, #{TAT,jdbcType=INTEGER}, 
      #{serviceStartDate,jdbcType=TIMESTAMP}, #{expectedOrderDueDate,jdbcType=TIMESTAMP}, 
      #{reportExpectDueDate,jdbcType=TIMESTAMP}, #{approverUserID,jdbcType=VARCHAR}, 
      #{expressNo,jdbcType=VARCHAR}, #{assignUserID,jdbcType=VARCHAR}, #{assignUserName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{orderSource,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=BIT}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifitedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{KACustomerDeptCode,jdbcType=VARCHAR}, #{toDMFlag,jdbcType=INTEGER}, 
      #{oldOrderNo,jdbcType=VARCHAR}, #{organizationName,jdbcType=VARCHAR}, #{legalEntityCode,jdbcType=VARCHAR}, 
      #{orderConfirmDate,jdbcType=TIMESTAMP}, #{operationType,jdbcType=INTEGER}, #{postfix,jdbcType=VARCHAR}, 
      #{versionId,jdbcType=BIGINT}, #{suffixNum,jdbcType=VARCHAR},
      #{matrixNo,jdbcType=VARCHAR}, #{toStarLims,jdbcType=INTEGER}, #{certificateProgram,jdbcType=VARCHAR}, 
      #{sampleReceiveDate,jdbcType=TIMESTAMP}, #{deliveryApproveStatus,jdbcType=TINYINT}
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoPO" >
    insert into tb_general_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="BUID != null" >
        BUID,
      </if>
      <if test="locationID != null" >
        LocationID,
      </if>
      <if test="labId != null" >
        labId,
      </if>
      <if test="BUCode != null" >
        BUCode,
      </if>
      <if test="locationCode != null" >
        LocationCode,
      </if>
      <if test="labCode != null" >
        labCode,
      </if>
      <if test="groupID != null" >
        GroupID,
      </if>
      <if test="enquiryNo != null" >
        EnquiryNo,
      </if>
      <if test="enquiryID != null" >
        EnquiryID,
      </if>
      <if test="orderNo != null" >
        OrderNo,
      </if>
      <if test="orderStatus != null" >
        OrderStatus,
      </if>
      <if test="orderType != null" >
        OrderType,
      </if>
      <if test="orderCategory != null" >
        OrderCategory,
      </if>
      <if test="toBossFlag != null" >
        ToBossFlag,
      </if>
      <if test="payStatus != null" >
        PayStatus,
      </if>
      <if test="modifyFlag != null" >
        ModifyFlag,
      </if>
      <if test="toTestFlag != null" >
        ToTestFlag,
      </if>
      <if test="selfTestFlag != null" >
        SelfTestFlag,
      </if>
      <if test="approveFlag != null" >
        ApproveFlag,
      </if>
      <if test="serviceLevel != null" >
        ServiceLevel,
      </if>
      <if test="pendingFlag != null" >
        PendingFlag,
      </if>
      <if test="isCopyOrderFlag != null" >
        IsCopyOrderFlag,
      </if>
      <if test="discount != null" >
        Discount,
      </if>
      <if test="beforeTaxPrice != null" >
        BeforeTaxPrice,
      </if>
      <if test="afterTaxPrice != null" >
        AfterTaxPrice,
      </if>
      <if test="totalPrice != null" >
        TotalPrice,
      </if>
      <if test="currencyID != null" >
        CurrencyID,
      </if>
      <if test="faPiaoType != null" >
        FaPiaoType,
      </if>
      <if test="faPiaoTitleCN != null" >
        FaPiaoTitleCN,
      </if>
      <if test="faPiaoTitleEN != null" >
        FaPiaoTitleEN,
      </if>
      <if test="branchName != null" >
        BranchName,
      </if>
      <if test="bankAccount != null" >
        BankAccount,
      </if>
      <if test="bankName != null" >
        BankName,
      </if>
      <if test="TAT != null" >
        TAT,
      </if>
      <if test="serviceStartDate != null" >
        ServiceStartDate,
      </if>
      <if test="expectedOrderDueDate != null" >
        ExpectedOrderDueDate,
      </if>
      <if test="reportExpectDueDate != null" >
        ReportExpectDueDate,
      </if>
      <if test="approverUserID != null" >
        ApproverUserID,
      </if>
      <if test="expressNo != null" >
        ExpressNo,
      </if>
      <if test="assignUserID != null" >
        AssignUserID,
      </if>
      <if test="assignUserName != null" >
        AssignUserName,
      </if>
      <if test="remark != null" >
        Remark,
      </if>
      <if test="orderSource != null" >
        OrderSource,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifitedDate != null" >
        ModifitedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="KACustomerDeptCode != null" >
        KACustomerDeptCode,
      </if>
      <if test="toDMFlag != null" >
        ToDMFlag,
      </if>
      <if test="oldOrderNo != null" >
        OldOrderNo,
      </if>
      <if test="organizationName != null" >
        OrganizationName,
      </if>
      <if test="legalEntityCode != null" >
        LegalEntityCode,
      </if>
      <if test="orderConfirmDate != null" >
        OrderConfirmDate,
      </if>
      <if test="operationType != null" >
        operationType,
      </if>
      <if test="postfix != null" >
        `postfix`,
      </if>
      <if test="versionId != null" >
        versionId,
      </if>
      <if test="suffixNum != null" >
        suffixNum,
      </if>
      <if test="matrixNo != null" >
        MatrixNo,
      </if>
      <if test="toStarLims != null" >
        ToStarLims,
      </if>
      <if test="certificateProgram != null" >
        CertificateProgram,
      </if>
      <if test="sampleReceiveDate != null" >
        SampleReceiveDate,
      </if>
      <if test="deliveryApproveStatus != null" >
        DeliveryApproveStatus,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="BUID != null" >
        #{BUID,jdbcType=INTEGER},
      </if>
      <if test="locationID != null" >
        #{locationID,jdbcType=INTEGER},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=INTEGER},
      </if>
      <if test="BUCode != null" >
        #{BUCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="groupID != null" >
        #{groupID,jdbcType=VARCHAR},
      </if>
      <if test="enquiryNo != null" >
        #{enquiryNo,jdbcType=VARCHAR},
      </if>
      <if test="enquiryID != null" >
        #{enquiryID,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null" >
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderCategory != null" >
        #{orderCategory,jdbcType=TINYINT},
      </if>
      <if test="toBossFlag != null" >
        #{toBossFlag,jdbcType=INTEGER},
      </if>
      <if test="payStatus != null" >
        #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null" >
        #{modifyFlag,jdbcType=INTEGER},
      </if>
      <if test="toTestFlag != null" >
        #{toTestFlag,jdbcType=INTEGER},
      </if>
      <if test="selfTestFlag != null" >
        #{selfTestFlag,jdbcType=INTEGER},
      </if>
      <if test="approveFlag != null" >
        #{approveFlag,jdbcType=INTEGER},
      </if>
      <if test="serviceLevel != null" >
        #{serviceLevel,jdbcType=INTEGER},
      </if>
      <if test="pendingFlag != null" >
        #{pendingFlag,jdbcType=BIT},
      </if>
      <if test="isCopyOrderFlag != null" >
        #{isCopyOrderFlag,jdbcType=BIT},
      </if>
      <if test="discount != null" >
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="beforeTaxPrice != null" >
        #{beforeTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterTaxPrice != null" >
        #{afterTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null" >
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyID != null" >
        #{currencyID,jdbcType=VARCHAR},
      </if>
      <if test="faPiaoType != null" >
        #{faPiaoType,jdbcType=INTEGER},
      </if>
      <if test="faPiaoTitleCN != null" >
        #{faPiaoTitleCN,jdbcType=VARCHAR},
      </if>
      <if test="faPiaoTitleEN != null" >
        #{faPiaoTitleEN,jdbcType=VARCHAR},
      </if>
      <if test="branchName != null" >
        #{branchName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null" >
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="TAT != null" >
        #{TAT,jdbcType=INTEGER},
      </if>
      <if test="serviceStartDate != null" >
        #{serviceStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedOrderDueDate != null" >
        #{expectedOrderDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportExpectDueDate != null" >
        #{reportExpectDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approverUserID != null" >
        #{approverUserID,jdbcType=VARCHAR},
      </if>
      <if test="expressNo != null" >
        #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="assignUserID != null" >
        #{assignUserID,jdbcType=VARCHAR},
      </if>
      <if test="assignUserName != null" >
        #{assignUserName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null" >
        #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifitedDate != null" >
        #{modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="KACustomerDeptCode != null" >
        #{KACustomerDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="toDMFlag != null" >
        #{toDMFlag,jdbcType=INTEGER},
      </if>
      <if test="oldOrderNo != null" >
        #{oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="organizationName != null" >
        #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="legalEntityCode != null" >
        #{legalEntityCode,jdbcType=VARCHAR},
      </if>
      <if test="orderConfirmDate != null" >
        #{orderConfirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null" >
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="postfix != null" >
        #{postfix,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null" >
        #{versionId,jdbcType=BIGINT},
      </if>
      <if test="suffixNum != null" >
        #{suffixNum,jdbcType=VARCHAR},
      </if>
      <if test="matrixNo != null" >
        #{matrixNo,jdbcType=VARCHAR},
      </if>
      <if test="toStarLims != null" >
        #{toStarLims,jdbcType=INTEGER},
      </if>
      <if test="certificateProgram != null" >
        #{certificateProgram,jdbcType=VARCHAR},
      </if>
      <if test="sampleReceiveDate != null" >
        #{sampleReceiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryApproveStatus != null" >
        #{deliveryApproveStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_general_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_general_order
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.BUID != null" >
        BUID = #{record.BUID,jdbcType=INTEGER},
      </if>
      <if test="record.locationID != null" >
        LocationID = #{record.locationID,jdbcType=INTEGER},
      </if>
      <if test="record.BUCode != null" >
        BUCode = #{record.BUCode,jdbcType=VARCHAR},
      </if>
      <if test="record.locationCode != null" >
        LocationCode = #{record.locationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.groupID != null" >
        GroupID = #{record.groupID,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryNo != null" >
        EnquiryNo = #{record.enquiryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryID != null" >
        EnquiryID = #{record.enquiryID,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null" >
        OrderStatus = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.orderType != null" >
        OrderType = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderCategory != null" >
        OrderCategory = #{record.orderCategory,jdbcType=TINYINT},
      </if>
      <if test="record.toBossFlag != null" >
        ToBossFlag = #{record.toBossFlag,jdbcType=INTEGER},
      </if>
      <if test="record.payStatus != null" >
        PayStatus = #{record.payStatus,jdbcType=INTEGER},
      </if>
      <if test="record.modifyFlag != null" >
        ModifyFlag = #{record.modifyFlag,jdbcType=INTEGER},
      </if>
      <if test="record.toTestFlag != null" >
        ToTestFlag = #{record.toTestFlag,jdbcType=INTEGER},
      </if>
      <if test="record.selfTestFlag != null" >
        SelfTestFlag = #{record.selfTestFlag,jdbcType=INTEGER},
      </if>
      <if test="record.approveFlag != null" >
        ApproveFlag = #{record.approveFlag,jdbcType=INTEGER},
      </if>
      <if test="record.serviceLevel != null" >
        ServiceLevel = #{record.serviceLevel,jdbcType=INTEGER},
      </if>
      <if test="record.pendingFlag != null" >
        PendingFlag = #{record.pendingFlag,jdbcType=BIT},
      </if>
      <if test="record.isCopyOrderFlag != null" >
        IsCopyOrderFlag = #{record.isCopyOrderFlag,jdbcType=BIT},
      </if>
      <if test="record.discount != null" >
        Discount = #{record.discount,jdbcType=DECIMAL},
      </if>
      <if test="record.beforeTaxPrice != null" >
        BeforeTaxPrice = #{record.beforeTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.afterTaxPrice != null" >
        AfterTaxPrice = #{record.afterTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.totalPrice != null" >
        TotalPrice = #{record.totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.currencyID != null" >
        CurrencyID = #{record.currencyID,jdbcType=VARCHAR},
      </if>
      <if test="record.faPiaoType != null" >
        FaPiaoType = #{record.faPiaoType,jdbcType=INTEGER},
      </if>
      <if test="record.faPiaoTitleCN != null" >
        FaPiaoTitleCN = #{record.faPiaoTitleCN,jdbcType=VARCHAR},
      </if>
      <if test="record.faPiaoTitleEN != null" >
        FaPiaoTitleEN = #{record.faPiaoTitleEN,jdbcType=VARCHAR},
      </if>
      <if test="record.branchName != null" >
        BranchName = #{record.branchName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccount != null" >
        BankAccount = #{record.bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null" >
        BankName = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.TAT != null" >
        TAT = #{record.TAT,jdbcType=INTEGER},
      </if>
      <if test="record.serviceStartDate != null" >
        ServiceStartDate = #{record.serviceStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expectedOrderDueDate != null" >
        ExpectedOrderDueDate = #{record.expectedOrderDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportExpectDueDate != null" >
        ReportExpectDueDate = #{record.reportExpectDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approverUserID != null" >
        ApproverUserID = #{record.approverUserID,jdbcType=VARCHAR},
      </if>
      <if test="record.expressNo != null" >
        ExpressNo = #{record.expressNo,jdbcType=VARCHAR},
      </if>
      <if test="record.assignUserID != null" >
        AssignUserID = #{record.assignUserID,jdbcType=VARCHAR},
      </if>
      <if test="record.assignUserName != null" >
        AssignUserName = #{record.assignUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        Remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSource != null" >
        OrderSource = #{record.orderSource,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifitedDate != null" >
        ModifitedDate = #{record.modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.KACustomerDeptCode != null" >
        KACustomerDeptCode = #{record.KACustomerDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.toDMFlag != null" >
        ToDMFlag = #{record.toDMFlag,jdbcType=INTEGER},
      </if>
      <if test="record.oldOrderNo != null" >
        OldOrderNo = #{record.oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.organizationName != null" >
        OrganizationName = #{record.organizationName,jdbcType=VARCHAR},
      </if>
      <if test="record.legalEntityCode != null" >
        LegalEntityCode = #{record.legalEntityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderConfirmDate != null" >
        OrderConfirmDate = #{record.orderConfirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationType != null" >
        operationType = #{record.operationType,jdbcType=INTEGER},
      </if>
      <if test="record.postfix != null" >
        `postfix` = #{record.postfix,jdbcType=VARCHAR},
      </if>
      <if test="record.versionId != null" >
        versionId = #{record.versionId,jdbcType=BIGINT},
      </if>
      <if test="record.suffixNum != null" >
        suffixNum = #{record.suffixNum,jdbcType=VARCHAR},
      </if>
      <if test="record.matrixNo != null" >
        MatrixNo = #{record.matrixNo,jdbcType=VARCHAR},
      </if>
      <if test="record.toStarLims != null" >
        ToStarLims = #{record.toStarLims,jdbcType=INTEGER},
      </if>
      <if test="record.certificateProgram != null" >
        CertificateProgram = #{record.certificateProgram,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleReceiveDate != null" >
        SampleReceiveDate = #{record.sampleReceiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryApproveStatus != null" >
        DeliveryApproveStatus = #{record.deliveryApproveStatus,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_general_order
    set ID = #{record.ID,jdbcType=VARCHAR},
      BUID = #{record.BUID,jdbcType=INTEGER},
      LocationID = #{record.locationID,jdbcType=INTEGER},
      BUCode = #{record.BUCode,jdbcType=VARCHAR},
      LocationCode = #{record.locationCode,jdbcType=VARCHAR},
      GroupID = #{record.groupID,jdbcType=VARCHAR},
      EnquiryNo = #{record.enquiryNo,jdbcType=VARCHAR},
      EnquiryID = #{record.enquiryID,jdbcType=VARCHAR},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      OrderStatus = #{record.orderStatus,jdbcType=INTEGER},
      OrderType = #{record.orderType,jdbcType=INTEGER},
      OrderCategory = #{record.orderCategory,jdbcType=TINYINT},
      ToBossFlag = #{record.toBossFlag,jdbcType=INTEGER},
      PayStatus = #{record.payStatus,jdbcType=INTEGER},
      ModifyFlag = #{record.modifyFlag,jdbcType=INTEGER},
      ToTestFlag = #{record.toTestFlag,jdbcType=INTEGER},
      SelfTestFlag = #{record.selfTestFlag,jdbcType=INTEGER},
      ApproveFlag = #{record.approveFlag,jdbcType=INTEGER},
      ServiceLevel = #{record.serviceLevel,jdbcType=INTEGER},
      PendingFlag = #{record.pendingFlag,jdbcType=BIT},
      IsCopyOrderFlag = #{record.isCopyOrderFlag,jdbcType=BIT},
      Discount = #{record.discount,jdbcType=DECIMAL},
      BeforeTaxPrice = #{record.beforeTaxPrice,jdbcType=DECIMAL},
      AfterTaxPrice = #{record.afterTaxPrice,jdbcType=DECIMAL},
      TotalPrice = #{record.totalPrice,jdbcType=DECIMAL},
      CurrencyID = #{record.currencyID,jdbcType=VARCHAR},
      FaPiaoType = #{record.faPiaoType,jdbcType=INTEGER},
      FaPiaoTitleCN = #{record.faPiaoTitleCN,jdbcType=VARCHAR},
      FaPiaoTitleEN = #{record.faPiaoTitleEN,jdbcType=VARCHAR},
      BranchName = #{record.branchName,jdbcType=VARCHAR},
      BankAccount = #{record.bankAccount,jdbcType=VARCHAR},
      BankName = #{record.bankName,jdbcType=VARCHAR},
      TAT = #{record.TAT,jdbcType=INTEGER},
      ServiceStartDate = #{record.serviceStartDate,jdbcType=TIMESTAMP},
      ExpectedOrderDueDate = #{record.expectedOrderDueDate,jdbcType=TIMESTAMP},
      ReportExpectDueDate = #{record.reportExpectDueDate,jdbcType=TIMESTAMP},
      ApproverUserID = #{record.approverUserID,jdbcType=VARCHAR},
      ExpressNo = #{record.expressNo,jdbcType=VARCHAR},
      AssignUserID = #{record.assignUserID,jdbcType=VARCHAR},
      AssignUserName = #{record.assignUserName,jdbcType=VARCHAR},
      Remark = #{record.remark,jdbcType=VARCHAR},
      OrderSource = #{record.orderSource,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{record.modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      KACustomerDeptCode = #{record.KACustomerDeptCode,jdbcType=VARCHAR},
      ToDMFlag = #{record.toDMFlag,jdbcType=INTEGER},
      OldOrderNo = #{record.oldOrderNo,jdbcType=VARCHAR},
      OrganizationName = #{record.organizationName,jdbcType=VARCHAR},
      LegalEntityCode = #{record.legalEntityCode,jdbcType=VARCHAR},
      OrderConfirmDate = #{record.orderConfirmDate,jdbcType=TIMESTAMP},
      operationType = #{record.operationType,jdbcType=INTEGER},
      `postfix` = #{record.postfix,jdbcType=VARCHAR},
      versionId = #{record.versionId,jdbcType=BIGINT},
      suffixNum = #{record.suffixNum,jdbcType=VARCHAR},
      MatrixNo = #{record.matrixNo,jdbcType=VARCHAR},
      ToStarLims = #{record.toStarLims,jdbcType=INTEGER},
      CertificateProgram = #{record.certificateProgram,jdbcType=VARCHAR},
      SampleReceiveDate = #{record.sampleReceiveDate,jdbcType=TIMESTAMP},
      DeliveryApproveStatus = #{record.deliveryApproveStatus,jdbcType=TINYINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoPO" >
    update tb_general_order
    <set >
      <if test="BUID != null" >
        BUID = #{BUID,jdbcType=INTEGER},
      </if>
      <if test="locationID != null" >
        LocationID = #{locationID,jdbcType=INTEGER},
      </if>
      <if test="BUCode != null" >
        BUCode = #{BUCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        LocationCode = #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="groupID != null" >
        GroupID = #{groupID,jdbcType=VARCHAR},
      </if>
      <if test="enquiryNo != null" >
        EnquiryNo = #{enquiryNo,jdbcType=VARCHAR},
      </if>
      <if test="enquiryID != null" >
        EnquiryID = #{enquiryID,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null" >
        OrderStatus = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderType != null" >
        OrderType = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderCategory != null" >
        OrderCategory = #{orderCategory,jdbcType=TINYINT},
      </if>
      <if test="toBossFlag != null" >
        ToBossFlag = #{toBossFlag,jdbcType=INTEGER},
      </if>
      <if test="payStatus != null" >
        PayStatus = #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null" >
        ModifyFlag = #{modifyFlag,jdbcType=INTEGER},
      </if>
      <if test="toTestFlag != null" >
        ToTestFlag = #{toTestFlag,jdbcType=INTEGER},
      </if>
      <if test="selfTestFlag != null" >
        SelfTestFlag = #{selfTestFlag,jdbcType=INTEGER},
      </if>
      <if test="approveFlag != null" >
        ApproveFlag = #{approveFlag,jdbcType=INTEGER},
      </if>
      <if test="serviceLevel != null" >
        ServiceLevel = #{serviceLevel,jdbcType=INTEGER},
      </if>
      <if test="pendingFlag != null" >
        PendingFlag = #{pendingFlag,jdbcType=BIT},
      </if>
      <if test="isCopyOrderFlag != null" >
        IsCopyOrderFlag = #{isCopyOrderFlag,jdbcType=BIT},
      </if>
      <if test="discount != null" >
        Discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="beforeTaxPrice != null" >
        BeforeTaxPrice = #{beforeTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterTaxPrice != null" >
        AfterTaxPrice = #{afterTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null" >
        TotalPrice = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyID != null" >
        CurrencyID = #{currencyID,jdbcType=VARCHAR},
      </if>
      <if test="faPiaoType != null" >
        FaPiaoType = #{faPiaoType,jdbcType=INTEGER},
      </if>
      <if test="faPiaoTitleCN != null" >
        FaPiaoTitleCN = #{faPiaoTitleCN,jdbcType=VARCHAR},
      </if>
      <if test="faPiaoTitleEN != null" >
        FaPiaoTitleEN = #{faPiaoTitleEN,jdbcType=VARCHAR},
      </if>
      <if test="branchName != null" >
        BranchName = #{branchName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        BankAccount = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null" >
        BankName = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="TAT != null" >
        TAT = #{TAT,jdbcType=INTEGER},
      </if>
      <if test="serviceStartDate != null" >
        ServiceStartDate = #{serviceStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedOrderDueDate != null" >
        ExpectedOrderDueDate = #{expectedOrderDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportExpectDueDate != null" >
        ReportExpectDueDate = #{reportExpectDueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approverUserID != null" >
        ApproverUserID = #{approverUserID,jdbcType=VARCHAR},
      </if>
      <if test="expressNo != null" >
        ExpressNo = #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="assignUserID != null" >
        AssignUserID = #{assignUserID,jdbcType=VARCHAR},
      </if>
      <if test="assignUserName != null" >
        AssignUserName = #{assignUserName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null" >
        OrderSource = #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifitedDate != null" >
        ModifitedDate = #{modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="KACustomerDeptCode != null" >
        KACustomerDeptCode = #{KACustomerDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="toDMFlag != null" >
        ToDMFlag = #{toDMFlag,jdbcType=INTEGER},
      </if>
      <if test="oldOrderNo != null" >
        OldOrderNo = #{oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="organizationName != null" >
        OrganizationName = #{organizationName,jdbcType=VARCHAR},
      </if>
      <if test="legalEntityCode != null" >
        LegalEntityCode = #{legalEntityCode,jdbcType=VARCHAR},
      </if>
      <if test="orderConfirmDate != null" >
        OrderConfirmDate = #{orderConfirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null" >
        operationType = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="postfix != null" >
        `postfix` = #{postfix,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null" >
        versionId = #{versionId,jdbcType=BIGINT},
      </if>
      <if test="suffixNum != null" >
        suffixNum = #{suffixNum,jdbcType=VARCHAR},
      </if>
      <if test="matrixNo != null" >
        MatrixNo = #{matrixNo,jdbcType=VARCHAR},
      </if>
      <if test="toStarLims != null" >
        ToStarLims = #{toStarLims,jdbcType=INTEGER},
      </if>
      <if test="certificateProgram != null" >
        CertificateProgram = #{certificateProgram,jdbcType=VARCHAR},
      </if>
      <if test="sampleReceiveDate != null" >
        SampleReceiveDate = #{sampleReceiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryApproveStatus != null" >
        DeliveryApproveStatus = #{deliveryApproveStatus,jdbcType=TINYINT},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.GeneralOrderInfoPO" >
    update tb_general_order
    set BUID = #{BUID,jdbcType=INTEGER},
      LocationID = #{locationID,jdbcType=INTEGER},
      BUCode = #{BUCode,jdbcType=VARCHAR},
      LocationCode = #{locationCode,jdbcType=VARCHAR},
      GroupID = #{groupID,jdbcType=VARCHAR},
      EnquiryNo = #{enquiryNo,jdbcType=VARCHAR},
      EnquiryID = #{enquiryID,jdbcType=VARCHAR},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      OrderStatus = #{orderStatus,jdbcType=INTEGER},
      OrderType = #{orderType,jdbcType=INTEGER},
      OrderCategory = #{orderCategory,jdbcType=TINYINT},
      ToBossFlag = #{toBossFlag,jdbcType=INTEGER},
      PayStatus = #{payStatus,jdbcType=INTEGER},
      ModifyFlag = #{modifyFlag,jdbcType=INTEGER},
      ToTestFlag = #{toTestFlag,jdbcType=INTEGER},
      SelfTestFlag = #{selfTestFlag,jdbcType=INTEGER},
      ApproveFlag = #{approveFlag,jdbcType=INTEGER},
      ServiceLevel = #{serviceLevel,jdbcType=INTEGER},
      PendingFlag = #{pendingFlag,jdbcType=BIT},
      IsCopyOrderFlag = #{isCopyOrderFlag,jdbcType=BIT},
      Discount = #{discount,jdbcType=DECIMAL},
      BeforeTaxPrice = #{beforeTaxPrice,jdbcType=DECIMAL},
      AfterTaxPrice = #{afterTaxPrice,jdbcType=DECIMAL},
      TotalPrice = #{totalPrice,jdbcType=DECIMAL},
      CurrencyID = #{currencyID,jdbcType=VARCHAR},
      FaPiaoType = #{faPiaoType,jdbcType=INTEGER},
      FaPiaoTitleCN = #{faPiaoTitleCN,jdbcType=VARCHAR},
      FaPiaoTitleEN = #{faPiaoTitleEN,jdbcType=VARCHAR},
      BranchName = #{branchName,jdbcType=VARCHAR},
      BankAccount = #{bankAccount,jdbcType=VARCHAR},
      BankName = #{bankName,jdbcType=VARCHAR},
      TAT = #{TAT,jdbcType=INTEGER},
      ServiceStartDate = #{serviceStartDate,jdbcType=TIMESTAMP},
      ExpectedOrderDueDate = #{expectedOrderDueDate,jdbcType=TIMESTAMP},
      ReportExpectDueDate = #{reportExpectDueDate,jdbcType=TIMESTAMP},
      ApproverUserID = #{approverUserID,jdbcType=VARCHAR},
      ExpressNo = #{expressNo,jdbcType=VARCHAR},
      AssignUserID = #{assignUserID,jdbcType=VARCHAR},
      AssignUserName = #{assignUserName,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR},
      OrderSource = #{orderSource,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      KACustomerDeptCode = #{KACustomerDeptCode,jdbcType=VARCHAR},
      ToDMFlag = #{toDMFlag,jdbcType=INTEGER},
      OldOrderNo = #{oldOrderNo,jdbcType=VARCHAR},
      OrganizationName = #{organizationName,jdbcType=VARCHAR},
      LegalEntityCode = #{legalEntityCode,jdbcType=VARCHAR},
      OrderConfirmDate = #{orderConfirmDate,jdbcType=TIMESTAMP},
      operationType = #{operationType,jdbcType=INTEGER},
      `postfix` = #{postfix,jdbcType=VARCHAR},
      versionId = #{versionId,jdbcType=BIGINT},
      suffixNum = #{suffixNum,jdbcType=VARCHAR},
      MatrixNo = #{matrixNo,jdbcType=VARCHAR},
      ToStarLims = #{toStarLims,jdbcType=INTEGER},
      CertificateProgram = #{certificateProgram,jdbcType=VARCHAR},
      SampleReceiveDate = #{sampleReceiveDate,jdbcType=TIMESTAMP},
      DeliveryApproveStatus = #{deliveryApproveStatus,jdbcType=TINYINT}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>