<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.BossOrderInvoiceInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="boss_order_no" property="bossOrderNo" jdbcType="VARCHAR" />
    <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="invoice_date" property="invoiceDate" jdbcType="TIMESTAMP" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="invoice_amount" property="invoiceAmount" jdbcType="DECIMAL" />
    <result column="paid_amount" property="paidAmount" jdbcType="DECIMAL" />
    <result column="original_invoice_no" property="originalInvoiceNo" jdbcType="VARCHAR" />
    <result column="original_boss_order_no" property="originalBossOrderNo" jdbcType="VARCHAR" />
    <result column="functional_currency_code" property="functionalCurrencyCode" jdbcType="VARCHAR" />
    <result column="pre_tax_amount_functiona_currency" property="preTaxAmountFunctionaCurrency" jdbcType="DECIMAL" />
    <result column="tax_amount_functiona_currency" property="taxAmountFunctionaCurrency" jdbcType="DECIMAL" />
    <result column="balance_amount" property="balanceAmount" jdbcType="DECIMAL" />
    <result column="invoice_due_date" property="invoiceDueDate" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="invoice_line" property="invoiceLine" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, boss_order_no, invoice_no, invoice_date, currency_code, invoice_amount, paid_amount, 
    original_invoice_no, original_boss_order_no, functional_currency_code, pre_tax_amount_functiona_currency, 
    tax_amount_functiona_currency, balance_amount, invoice_due_date, created_date, created_by, 
    modified_by, modified_date, active_indicator,invoice_line
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_boss_order_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_boss_order_invoice
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_boss_order_invoice
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoExample" >
    delete from tb_boss_order_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoPO" >
    insert into tb_boss_order_invoice (id, boss_order_no, invoice_no, 
      invoice_date, currency_code, invoice_amount, 
      paid_amount, original_invoice_no, original_boss_order_no, 
      functional_currency_code, pre_tax_amount_functiona_currency, 
      tax_amount_functiona_currency, balance_amount, 
      invoice_due_date, created_date, created_by, 
      modified_by, modified_date, active_indicator,invoice_line
      )
    values (#{id,jdbcType=VARCHAR}, #{bossOrderNo,jdbcType=VARCHAR}, #{invoiceNo,jdbcType=VARCHAR}, 
      #{invoiceDate,jdbcType=TIMESTAMP}, #{currencyCode,jdbcType=VARCHAR}, #{invoiceAmount,jdbcType=DECIMAL}, 
      #{paidAmount,jdbcType=DECIMAL}, #{originalInvoiceNo,jdbcType=VARCHAR}, #{originalBossOrderNo,jdbcType=VARCHAR}, 
      #{functionalCurrencyCode,jdbcType=VARCHAR}, #{preTaxAmountFunctionaCurrency,jdbcType=DECIMAL}, 
      #{taxAmountFunctionaCurrency,jdbcType=DECIMAL}, #{balanceAmount,jdbcType=DECIMAL}, 
      #{invoiceDueDate,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{activeIndicator,jdbcType=INTEGER},
      #{invoiceLine,jdbcType=VARCHAR}
           )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoPO" >
    insert into tb_boss_order_invoice
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="bossOrderNo != null" >
        boss_order_no,
      </if>
      <if test="invoiceNo != null" >
        invoice_no,
      </if>
      <if test="invoiceDate != null" >
        invoice_date,
      </if>
      <if test="currencyCode != null" >
        currency_code,
      </if>
      <if test="invoiceAmount != null" >
        invoice_amount,
      </if>
      <if test="paidAmount != null" >
        paid_amount,
      </if>
      <if test="originalInvoiceNo != null" >
        original_invoice_no,
      </if>
      <if test="originalBossOrderNo != null" >
        original_boss_order_no,
      </if>
      <if test="functionalCurrencyCode != null" >
        functional_currency_code,
      </if>
      <if test="preTaxAmountFunctionaCurrency != null" >
        pre_tax_amount_functiona_currency,
      </if>
      <if test="taxAmountFunctionaCurrency != null" >
        tax_amount_functiona_currency,
      </if>
      <if test="balanceAmount != null" >
        balance_amount,
      </if>
      <if test="invoiceDueDate != null" >
        invoice_due_date,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="activeIndicator != null" >
        invoice_line,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="bossOrderNo != null" >
        #{bossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null" >
        #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currencyCode != null" >
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceAmount != null" >
        #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null" >
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="originalInvoiceNo != null" >
        #{originalInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="originalBossOrderNo != null" >
        #{originalBossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="functionalCurrencyCode != null" >
        #{functionalCurrencyCode,jdbcType=VARCHAR},
      </if>
      <if test="preTaxAmountFunctionaCurrency != null" >
        #{preTaxAmountFunctionaCurrency,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFunctionaCurrency != null" >
        #{taxAmountFunctionaCurrency,jdbcType=DECIMAL},
      </if>
      <if test="balanceAmount != null" >
        #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceDueDate != null" >
        #{invoiceDueDate,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="invoiceLine != null" >
        #{invoiceLine,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_boss_order_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_boss_order_invoice
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.bossOrderNo != null" >
        boss_order_no = #{record.bossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null" >
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceDate != null" >
        invoice_date = #{record.invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceAmount != null" >
        invoice_amount = #{record.invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.paidAmount != null" >
        paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.originalInvoiceNo != null" >
        original_invoice_no = #{record.originalInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.originalBossOrderNo != null" >
        original_boss_order_no = #{record.originalBossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.functionalCurrencyCode != null" >
        functional_currency_code = #{record.functionalCurrencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.preTaxAmountFunctionaCurrency != null" >
        pre_tax_amount_functiona_currency = #{record.preTaxAmountFunctionaCurrency,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmountFunctionaCurrency != null" >
        tax_amount_functiona_currency = #{record.taxAmountFunctionaCurrency,jdbcType=DECIMAL},
      </if>
      <if test="record.balanceAmount != null" >
        balance_amount = #{record.balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.invoiceDueDate != null" >
        invoice_due_date = #{record.invoiceDueDate,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceLine != null" >
        invoice_line = #{record.invoiceLine,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_boss_order_invoice
    set id = #{record.id,jdbcType=VARCHAR},
      boss_order_no = #{record.bossOrderNo,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      invoice_date = #{record.invoiceDate,jdbcType=TIMESTAMP},
      currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      invoice_amount = #{record.invoiceAmount,jdbcType=DECIMAL},
      paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      original_invoice_no = #{record.originalInvoiceNo,jdbcType=VARCHAR},
      original_boss_order_no = #{record.originalBossOrderNo,jdbcType=VARCHAR},
      functional_currency_code = #{record.functionalCurrencyCode,jdbcType=VARCHAR},
      pre_tax_amount_functiona_currency = #{record.preTaxAmountFunctionaCurrency,jdbcType=DECIMAL},
      tax_amount_functiona_currency = #{record.taxAmountFunctionaCurrency,jdbcType=DECIMAL},
      balance_amount = #{record.balanceAmount,jdbcType=DECIMAL},
      invoice_due_date = #{record.invoiceDueDate,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      invoice_line = #{record.invoiceLine,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoPO" >
    update tb_boss_order_invoice
    <set >
      <if test="bossOrderNo != null" >
        boss_order_no = #{bossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null" >
        invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceAmount != null" >
        invoice_amount = #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null" >
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="originalInvoiceNo != null" >
        original_invoice_no = #{originalInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="originalBossOrderNo != null" >
        original_boss_order_no = #{originalBossOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="functionalCurrencyCode != null" >
        functional_currency_code = #{functionalCurrencyCode,jdbcType=VARCHAR},
      </if>
      <if test="preTaxAmountFunctionaCurrency != null" >
        pre_tax_amount_functiona_currency = #{preTaxAmountFunctionaCurrency,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFunctionaCurrency != null" >
        tax_amount_functiona_currency = #{taxAmountFunctionaCurrency,jdbcType=DECIMAL},
      </if>
      <if test="balanceAmount != null" >
        balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceDueDate != null" >
        invoice_due_date = #{invoiceDueDate,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="invoiceLine != null" >
        invoice_line = #{invoiceLine,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInvoiceInfoPO" >
    update tb_boss_order_invoice
    set boss_order_no = #{bossOrderNo,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      invoice_amount = #{invoiceAmount,jdbcType=DECIMAL},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      original_invoice_no = #{originalInvoiceNo,jdbcType=VARCHAR},
      original_boss_order_no = #{originalBossOrderNo,jdbcType=VARCHAR},
      functional_currency_code = #{functionalCurrencyCode,jdbcType=VARCHAR},
      pre_tax_amount_functiona_currency = #{preTaxAmountFunctionaCurrency,jdbcType=DECIMAL},
      tax_amount_functiona_currency = #{taxAmountFunctionaCurrency,jdbcType=DECIMAL},
      balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      invoice_due_date = #{invoiceDueDate,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      active_indicator = #{activeIndicator,jdbcType=INTEGER},
      invoice_line = #{invoiceLine,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>