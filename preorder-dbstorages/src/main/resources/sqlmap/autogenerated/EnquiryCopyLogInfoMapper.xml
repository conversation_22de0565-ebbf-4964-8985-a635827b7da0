<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EnquiryCopyLogInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="original_enquiry_id" property="originalEnquiryId" jdbcType="VARCHAR" />
    <result column="new_enquiry_id" property="newEnquiryId" jdbcType="VARCHAR" />
    <result column="new_enquiry_no" property="newEnquiryNo" jdbcType="VARCHAR" />
    <result column="new_order_no" property="newOrderNo" jdbcType="VARCHAR" />
    <result column="copy_message" property="copyMessage" jdbcType="VARCHAR" />
    <result column="batch" property="batch" jdbcType="BIGINT" />
    <result column="copy_start_date" property="copyStartDate" jdbcType="TIMESTAMP" />
    <result column="copy_end_date" property="copyEndDate" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, original_enquiry_id, new_enquiry_id, new_enquiry_no, new_order_no, copy_message, 
    batch, copy_start_date, copy_end_date, created_date, modified_by, active_indicator, 
    created_by, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_enquiry_copy_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_enquiry_copy_log
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_enquiry_copy_log
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoExample" >
    delete from tb_enquiry_copy_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoPO" >
    insert into tb_enquiry_copy_log (id, original_enquiry_id, new_enquiry_id, 
      new_enquiry_no, new_order_no, copy_message, 
      batch, copy_start_date, copy_end_date, 
      created_date, modified_by, active_indicator, 
      created_by, modified_date)
    values (#{id,jdbcType=VARCHAR}, #{originalEnquiryId,jdbcType=VARCHAR}, #{newEnquiryId,jdbcType=VARCHAR}, 
      #{newEnquiryNo,jdbcType=VARCHAR}, #{newOrderNo,jdbcType=VARCHAR}, #{copyMessage,jdbcType=VARCHAR}, 
      #{batch,jdbcType=BIGINT}, #{copyStartDate,jdbcType=TIMESTAMP}, #{copyEndDate,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoPO" >
    insert into tb_enquiry_copy_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="originalEnquiryId != null" >
        original_enquiry_id,
      </if>
      <if test="newEnquiryId != null" >
        new_enquiry_id,
      </if>
      <if test="newEnquiryNo != null" >
        new_enquiry_no,
      </if>
      <if test="newOrderNo != null" >
        new_order_no,
      </if>
      <if test="copyMessage != null" >
        copy_message,
      </if>
      <if test="batch != null" >
        batch,
      </if>
      <if test="copyStartDate != null" >
        copy_start_date,
      </if>
      <if test="copyEndDate != null" >
        copy_end_date,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="originalEnquiryId != null" >
        #{originalEnquiryId,jdbcType=VARCHAR},
      </if>
      <if test="newEnquiryId != null" >
        #{newEnquiryId,jdbcType=VARCHAR},
      </if>
      <if test="newEnquiryNo != null" >
        #{newEnquiryNo,jdbcType=VARCHAR},
      </if>
      <if test="newOrderNo != null" >
        #{newOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="copyMessage != null" >
        #{copyMessage,jdbcType=VARCHAR},
      </if>
      <if test="batch != null" >
        #{batch,jdbcType=BIGINT},
      </if>
      <if test="copyStartDate != null" >
        #{copyStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="copyEndDate != null" >
        #{copyEndDate,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_enquiry_copy_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_enquiry_copy_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.originalEnquiryId != null" >
        original_enquiry_id = #{record.originalEnquiryId,jdbcType=VARCHAR},
      </if>
      <if test="record.newEnquiryId != null" >
        new_enquiry_id = #{record.newEnquiryId,jdbcType=VARCHAR},
      </if>
      <if test="record.newEnquiryNo != null" >
        new_enquiry_no = #{record.newEnquiryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.newOrderNo != null" >
        new_order_no = #{record.newOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.copyMessage != null" >
        copy_message = #{record.copyMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.batch != null" >
        batch = #{record.batch,jdbcType=BIGINT},
      </if>
      <if test="record.copyStartDate != null" >
        copy_start_date = #{record.copyStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.copyEndDate != null" >
        copy_end_date = #{record.copyEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_enquiry_copy_log
    set id = #{record.id,jdbcType=VARCHAR},
      original_enquiry_id = #{record.originalEnquiryId,jdbcType=VARCHAR},
      new_enquiry_id = #{record.newEnquiryId,jdbcType=VARCHAR},
      new_enquiry_no = #{record.newEnquiryNo,jdbcType=VARCHAR},
      new_order_no = #{record.newOrderNo,jdbcType=VARCHAR},
      copy_message = #{record.copyMessage,jdbcType=VARCHAR},
      batch = #{record.batch,jdbcType=BIGINT},
      copy_start_date = #{record.copyStartDate,jdbcType=TIMESTAMP},
      copy_end_date = #{record.copyEndDate,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoPO" >
    update tb_enquiry_copy_log
    <set >
      <if test="originalEnquiryId != null" >
        original_enquiry_id = #{originalEnquiryId,jdbcType=VARCHAR},
      </if>
      <if test="newEnquiryId != null" >
        new_enquiry_id = #{newEnquiryId,jdbcType=VARCHAR},
      </if>
      <if test="newEnquiryNo != null" >
        new_enquiry_no = #{newEnquiryNo,jdbcType=VARCHAR},
      </if>
      <if test="newOrderNo != null" >
        new_order_no = #{newOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="copyMessage != null" >
        copy_message = #{copyMessage,jdbcType=VARCHAR},
      </if>
      <if test="batch != null" >
        batch = #{batch,jdbcType=BIGINT},
      </if>
      <if test="copyStartDate != null" >
        copy_start_date = #{copyStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="copyEndDate != null" >
        copy_end_date = #{copyEndDate,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryCopyLogInfoPO" >
    update tb_enquiry_copy_log
    set original_enquiry_id = #{originalEnquiryId,jdbcType=VARCHAR},
      new_enquiry_id = #{newEnquiryId,jdbcType=VARCHAR},
      new_enquiry_no = #{newEnquiryNo,jdbcType=VARCHAR},
      new_order_no = #{newOrderNo,jdbcType=VARCHAR},
      copy_message = #{copyMessage,jdbcType=VARCHAR},
      batch = #{batch,jdbcType=BIGINT},
      copy_start_date = #{copyStartDate,jdbcType=TIMESTAMP},
      copy_end_date = #{copyEndDate,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>