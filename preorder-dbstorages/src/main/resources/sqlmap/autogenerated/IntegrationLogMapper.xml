<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.IntegrationLogMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogPO" >
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="system_id" property="systemId" jdbcType="VARCHAR"/>
    <result column="api_url" property="apiUrl" jdbcType="VARCHAR"/>
    <result column="duration" property="duration" jdbcType="INTEGER"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="retry_times" property="retryTimes" jdbcType="INTEGER"/>
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
    <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
    <result column="key_param" property="keyParam" jdbcType="VARCHAR"/>
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogWithBLOBs" extends="BaseResultMap" >
    <result column="req_param" property="reqParam" jdbcType="LONGVARCHAR"/>
    <result column="resp_param" property="respParam" jdbcType="LONGVARCHAR"/>
    <result column="error_msg" property="errorMsg" jdbcType="LONGVARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, system_id, api_url, duration, `status`, retry_times, created_date, created_by, 
    key_param
  </sql>
  <sql id="Blob_Column_List" >
    req_param, resp_param, error_msg
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_intergration_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_intergration_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_intergration_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_intergration_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogExample" >
    delete from tb_intergration_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogWithBLOBs" >
    insert into tb_intergration_log (id, system_id, api_url, 
      duration, `status`, retry_times, 
      created_date, created_by, key_param, 
      req_param, resp_param, error_msg
      )
    values (#{id,jdbcType=BIGINT}, #{systemId,jdbcType=VARCHAR}, #{apiUrl,jdbcType=VARCHAR},
      #{duration,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{retryTimes,jdbcType=INTEGER},
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{keyParam,jdbcType=VARCHAR},
      #{reqParam,jdbcType=LONGVARCHAR}, #{respParam,jdbcType=LONGVARCHAR}, #{errorMsg,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogWithBLOBs" >
    insert into tb_intergration_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="system_id != null" >
        system_id,
      </if>
      <if test="api_url != null" >
        api_url,
      </if>
      <if test="duration != null" >
        duration,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="retry_times != null" >
        retry_times,
      </if>
      <if test="created_date != null" >
        created_date,
      </if>
      <if test="created_by != null" >
        created_by,
      </if>
      <if test="key_param != null" >
        key_param,
      </if>
      <if test="req_param != null" >
        req_param,
      </if>
      <if test="resp_param != null" >
        resp_param,
      </if>
      <if test="error_msg != null" >
        error_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="system_id != null" >
        #{system_id,jdbcType=VARCHAR},
      </if>
      <if test="api_url != null" >
        #{api_url,jdbcType=VARCHAR},
      </if>
      <if test="duration != null" >
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="retry_times != null" >
        #{retry_times,jdbcType=INTEGER},
      </if>
      <if test="created_date != null" >
        #{created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="created_by != null" >
        #{created_by,jdbcType=VARCHAR},
      </if>
      <if test="key_param != null" >
        #{key_param,jdbcType=VARCHAR},
      </if>
      <if test="req_param != null" >
        #{req_param,jdbcType=LONGVARCHAR},
      </if>
      <if test="resp_param != null" >
        #{resp_param,jdbcType=LONGVARCHAR},
      </if>
      <if test="error_msg != null" >
        #{error_msg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogExample" resultType="java.lang.Integer" >
    select count(*) from tb_intergration_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_intergration_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.system_id != null" >
        system_id = #{record.system_id,jdbcType=VARCHAR},
      </if>
      <if test="record.api_url != null" >
        api_url = #{record.api_url,jdbcType=VARCHAR},
      </if>
      <if test="record.duration != null" >
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.retry_times != null" >
        retry_times = #{record.retry_times,jdbcType=INTEGER},
      </if>
      <if test="record.created_date != null" >
        created_date = #{record.created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="record.created_by != null" >
        created_by = #{record.created_by,jdbcType=VARCHAR},
      </if>
      <if test="record.key_param != null" >
        key_param = #{record.key_param,jdbcType=VARCHAR},
      </if>
      <if test="record.req_param != null" >
        req_param = #{record.req_param,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.resp_param != null" >
        resp_param = #{record.resp_param,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.error_msg != null" >
        error_msg = #{record.error_msg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_intergration_log
    set id = #{record.id,jdbcType=BIGINT},
      system_id = #{record.system_id,jdbcType=VARCHAR},
      api_url = #{record.api_url,jdbcType=VARCHAR},
      duration = #{record.duration,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=INTEGER},
      retry_times = #{record.retry_times,jdbcType=INTEGER},
      created_date = #{record.created_date,jdbcType=TIMESTAMP},
      created_by = #{record.created_by,jdbcType=VARCHAR},
      key_param = #{record.key_param,jdbcType=VARCHAR},
      req_param = #{record.req_param,jdbcType=LONGVARCHAR},
      resp_param = #{record.resp_param,jdbcType=LONGVARCHAR},
      error_msg = #{record.error_msg,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_intergration_log
    set id = #{record.id,jdbcType=BIGINT},
      system_id = #{record.system_id,jdbcType=VARCHAR},
      api_url = #{record.api_url,jdbcType=VARCHAR},
      duration = #{record.duration,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=INTEGER},
      retry_times = #{record.retry_times,jdbcType=INTEGER},
      created_date = #{record.created_date,jdbcType=TIMESTAMP},
      created_by = #{record.created_by,jdbcType=VARCHAR},
      key_param = #{record.key_param,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogWithBLOBs" >
    update tb_intergration_log
    <set >
      <if test="system_id != null" >
        system_id = #{system_id,jdbcType=VARCHAR},
      </if>
      <if test="api_url != null" >
        api_url = #{api_url,jdbcType=VARCHAR},
      </if>
      <if test="duration != null" >
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="retry_times != null" >
        retry_times = #{retry_times,jdbcType=INTEGER},
      </if>
      <if test="created_date != null" >
        created_date = #{created_date,jdbcType=TIMESTAMP},
      </if>
      <if test="created_by != null" >
        created_by = #{created_by,jdbcType=VARCHAR},
      </if>
      <if test="key_param != null" >
        key_param = #{key_param,jdbcType=VARCHAR},
      </if>
      <if test="req_param != null" >
        req_param = #{req_param,jdbcType=LONGVARCHAR},
      </if>
      <if test="resp_param != null" >
        resp_param = #{resp_param,jdbcType=LONGVARCHAR},
      </if>
      <if test="error_msg != null" >
        error_msg = #{error_msg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogWithBLOBs" >
    update tb_intergration_log
    set system_id = #{system_id,jdbcType=VARCHAR},
      api_url = #{api_url,jdbcType=VARCHAR},
      duration = #{duration,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      retry_times = #{retry_times,jdbcType=INTEGER},
      created_date = #{created_date,jdbcType=TIMESTAMP},
      created_by = #{created_by,jdbcType=VARCHAR},
      key_param = #{key_param,jdbcType=VARCHAR},
      req_param = #{req_param,jdbcType=LONGVARCHAR},
      resp_param = #{resp_param,jdbcType=LONGVARCHAR},
      error_msg = #{error_msg,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.IntegrationLogPO" >
    update tb_intergration_log
    set system_id = #{system_id,jdbcType=VARCHAR},
      api_url = #{api_url,jdbcType=VARCHAR},
      duration = #{duration,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      retry_times = #{retry_times,jdbcType=INTEGER},
      created_date = #{created_date,jdbcType=TIMESTAMP},
      created_by = #{created_by,jdbcType=VARCHAR},
      key_param = #{key_param,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>