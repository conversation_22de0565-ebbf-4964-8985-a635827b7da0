<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.TreOrderRelationshipInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="BOSSOrderID" property="BOSSOrderID" jdbcType="VARCHAR" />
    <result column="GeneralOrderID" property="generalOrderID" jdbcType="VARCHAR" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, BOSSOrderID, GeneralOrderID, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_order_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tre_order_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tre_order_relationship
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoExample" >
    delete from tre_order_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoPO" >
    insert into tre_order_relationship (ID, BOSSOrderID, GeneralOrderID, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate
      )
    values (#{ID,jdbcType=VARCHAR}, #{BOSSOrderID,jdbcType=VARCHAR}, #{generalOrderID,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoPO" >
    insert into tre_order_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="BOSSOrderID != null" >
        BOSSOrderID,
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="BOSSOrderID != null" >
        #{BOSSOrderID,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderID != null" >
        #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_order_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_order_relationship
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.BOSSOrderID != null" >
        BOSSOrderID = #{record.BOSSOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.generalOrderID != null" >
        GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_order_relationship
    set ID = #{record.ID,jdbcType=VARCHAR},
      BOSSOrderID = #{record.BOSSOrderID,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoPO" >
    update tre_order_relationship
    <set >
      <if test="BOSSOrderID != null" >
        BOSSOrderID = #{BOSSOrderID,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TreOrderRelationshipInfoPO" >
    update tre_order_relationship
    set BOSSOrderID = #{BOSSOrderID,jdbcType=VARCHAR},
      GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>