<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.TestrptCoverpageDffInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoPO" >
    <id column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
    <id column="Labelname" property="labelname" jdbcType="VARCHAR" />
    <result column="GeneralOrderID" property="generalOrderID" jdbcType="VARCHAR" />
    <result column="FieldCode" property="fieldCode" jdbcType="VARCHAR" />
    <result column="DisplayedInReportFlag" property="displayedInReportFlag" jdbcType="TINYINT" />
    <result column="DisplayedInReportSection" property="displayedInReportSection" jdbcType="VARCHAR" />
    <result column="DisplayedSeqInReport" property="displayedSeqInReport" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifitedDate" property="modifitedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="DFFFormID" property="DFFFormID" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoPO" extends="BaseResultMap" >
    <result column="DisplayData" property="displayData" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ReportNo, Labelname, GeneralOrderID, FieldCode, DisplayedInReportFlag, DisplayedInReportSection, 
    DisplayedSeqInReport, CreatedDate, CreatedBy, ModifitedDate, ModifiedBy, DFFFormID
  </sql>
  <sql id="Blob_Column_List" >
    DisplayData
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from testrpt_coverpage_dff
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from testrpt_coverpage_dff
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoKey" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from testrpt_coverpage_dff
    where ReportNo = #{reportNo,jdbcType=VARCHAR}
      and Labelname = #{labelname,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoKey" >
    delete from testrpt_coverpage_dff
    where ReportNo = #{reportNo,jdbcType=VARCHAR}
      and Labelname = #{labelname,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoExample" >
    delete from testrpt_coverpage_dff
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoPO" >
    insert into testrpt_coverpage_dff (ReportNo, Labelname, GeneralOrderID, 
      FieldCode, DisplayedInReportFlag, DisplayedInReportSection, 
      DisplayedSeqInReport, CreatedDate, CreatedBy, 
      ModifitedDate, ModifiedBy, DFFFormID, 
      DisplayData)
    values (#{reportNo,jdbcType=VARCHAR}, #{labelname,jdbcType=VARCHAR}, #{generalOrderID,jdbcType=VARCHAR}, 
      #{fieldCode,jdbcType=VARCHAR}, #{displayedInReportFlag,jdbcType=TINYINT}, #{displayedInReportSection,jdbcType=VARCHAR}, 
      #{displayedSeqInReport,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifitedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{DFFFormID,jdbcType=VARCHAR}, 
      #{displayData,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoPO" >
    insert into testrpt_coverpage_dff
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="reportNo != null" >
        ReportNo,
      </if>
      <if test="labelname != null" >
        Labelname,
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID,
      </if>
      <if test="fieldCode != null" >
        FieldCode,
      </if>
      <if test="displayedInReportFlag != null" >
        DisplayedInReportFlag,
      </if>
      <if test="displayedInReportSection != null" >
        DisplayedInReportSection,
      </if>
      <if test="displayedSeqInReport != null" >
        DisplayedSeqInReport,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifitedDate != null" >
        ModifitedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="DFFFormID != null" >
        DFFFormID,
      </if>
      <if test="displayData != null" >
        DisplayData,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="labelname != null" >
        #{labelname,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderID != null" >
        #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="displayedInReportFlag != null" >
        #{displayedInReportFlag,jdbcType=TINYINT},
      </if>
      <if test="displayedInReportSection != null" >
        #{displayedInReportSection,jdbcType=VARCHAR},
      </if>
      <if test="displayedSeqInReport != null" >
        #{displayedSeqInReport,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifitedDate != null" >
        #{modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="DFFFormID != null" >
        #{DFFFormID,jdbcType=VARCHAR},
      </if>
      <if test="displayData != null" >
        #{displayData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoExample" resultType="java.lang.Integer" >
    select count(*) from testrpt_coverpage_dff
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update testrpt_coverpage_dff
    <set >
      <if test="record.reportNo != null" >
        ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.labelname != null" >
        Labelname = #{record.labelname,jdbcType=VARCHAR},
      </if>
      <if test="record.generalOrderID != null" >
        GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldCode != null" >
        FieldCode = #{record.fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="record.displayedInReportFlag != null" >
        DisplayedInReportFlag = #{record.displayedInReportFlag,jdbcType=TINYINT},
      </if>
      <if test="record.displayedInReportSection != null" >
        DisplayedInReportSection = #{record.displayedInReportSection,jdbcType=VARCHAR},
      </if>
      <if test="record.displayedSeqInReport != null" >
        DisplayedSeqInReport = #{record.displayedSeqInReport,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifitedDate != null" >
        ModifitedDate = #{record.modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.DFFFormID != null" >
        DFFFormID = #{record.DFFFormID,jdbcType=VARCHAR},
      </if>
      <if test="record.displayData != null" >
        DisplayData = #{record.displayData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update testrpt_coverpage_dff
    set ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      Labelname = #{record.labelname,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      FieldCode = #{record.fieldCode,jdbcType=VARCHAR},
      DisplayedInReportFlag = #{record.displayedInReportFlag,jdbcType=TINYINT},
      DisplayedInReportSection = #{record.displayedInReportSection,jdbcType=VARCHAR},
      DisplayedSeqInReport = #{record.displayedSeqInReport,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{record.modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      DFFFormID = #{record.DFFFormID,jdbcType=VARCHAR},
      DisplayData = #{record.displayData,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update testrpt_coverpage_dff
    set ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      Labelname = #{record.labelname,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      FieldCode = #{record.fieldCode,jdbcType=VARCHAR},
      DisplayedInReportFlag = #{record.displayedInReportFlag,jdbcType=TINYINT},
      DisplayedInReportSection = #{record.displayedInReportSection,jdbcType=VARCHAR},
      DisplayedSeqInReport = #{record.displayedSeqInReport,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{record.modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      DFFFormID = #{record.DFFFormID,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoPO" >
    update testrpt_coverpage_dff
    <set >
      <if test="generalOrderID != null" >
        GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        FieldCode = #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="displayedInReportFlag != null" >
        DisplayedInReportFlag = #{displayedInReportFlag,jdbcType=TINYINT},
      </if>
      <if test="displayedInReportSection != null" >
        DisplayedInReportSection = #{displayedInReportSection,jdbcType=VARCHAR},
      </if>
      <if test="displayedSeqInReport != null" >
        DisplayedSeqInReport = #{displayedSeqInReport,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifitedDate != null" >
        ModifitedDate = #{modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="DFFFormID != null" >
        DFFFormID = #{DFFFormID,jdbcType=VARCHAR},
      </if>
      <if test="displayData != null" >
        DisplayData = #{displayData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ReportNo = #{reportNo,jdbcType=VARCHAR}
      and Labelname = #{labelname,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoPO" >
    update testrpt_coverpage_dff
    set GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      FieldCode = #{fieldCode,jdbcType=VARCHAR},
      DisplayedInReportFlag = #{displayedInReportFlag,jdbcType=TINYINT},
      DisplayedInReportSection = #{displayedInReportSection,jdbcType=VARCHAR},
      DisplayedSeqInReport = #{displayedSeqInReport,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      DFFFormID = #{DFFFormID,jdbcType=VARCHAR},
      DisplayData = #{displayData,jdbcType=LONGVARCHAR}
    where ReportNo = #{reportNo,jdbcType=VARCHAR}
      and Labelname = #{labelname,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TestrptCoverpageDffInfoPO" >
    update testrpt_coverpage_dff
    set GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      FieldCode = #{fieldCode,jdbcType=VARCHAR},
      DisplayedInReportFlag = #{displayedInReportFlag,jdbcType=TINYINT},
      DisplayedInReportSection = #{displayedInReportSection,jdbcType=VARCHAR},
      DisplayedSeqInReport = #{displayedSeqInReport,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      DFFFormID = #{DFFFormID,jdbcType=VARCHAR}
    where ReportNo = #{reportNo,jdbcType=VARCHAR}
      and Labelname = #{labelname,jdbcType=VARCHAR}
  </update>
</mapper>