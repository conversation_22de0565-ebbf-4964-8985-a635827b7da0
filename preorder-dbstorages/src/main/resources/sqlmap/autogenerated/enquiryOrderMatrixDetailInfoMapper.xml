<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EnquiryOrderMatrixDetailInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="enquiry_order_matrix_id" property="enquiryOrderMatrixId" jdbcType="VARCHAR" />
    <result column="matrix_id" property="matrixId" jdbcType="VARCHAR" />
    <result column="service_item_id" property="serviceItemId" jdbcType="VARCHAR" />
    <result column="qty" property="qty" jdbcType="INTEGER" />
    <result column="sample_no" property="sampleNo" jdbcType="VARCHAR" />
    <result column="CreatedBy" property="createdby" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createddate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedby" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifieddate" jdbcType="TIMESTAMP" />
    <result column="ActiveIndicator" property="activeindicator" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, enquiry_order_matrix_id, matrix_id, service_item_id, qty, sample_no, CreatedBy, 
    CreatedDate, ModifiedBy, ModifiedDate, ActiveIndicator
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_enquiry_order_matrix_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_enquiry_order_matrix_detail
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_enquiry_order_matrix_detail
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoExample" >
    delete from tb_enquiry_order_matrix_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoPO" >
    insert into tb_enquiry_order_matrix_detail (id, enquiry_order_matrix_id, matrix_id, 
      service_item_id, qty, sample_no, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate, ActiveIndicator)
    values (#{id,jdbcType=VARCHAR}, #{enquiryOrderMatrixId,jdbcType=VARCHAR}, #{matrixId,jdbcType=VARCHAR}, 
      #{serviceItemId,jdbcType=VARCHAR}, #{qty,jdbcType=INTEGER}, #{sampleNo,jdbcType=VARCHAR}, 
      #{createdby,jdbcType=VARCHAR}, #{createddate,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=VARCHAR}, 
      #{modifieddate,jdbcType=TIMESTAMP}, #{activeindicator,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoPO" >
    insert into tb_enquiry_order_matrix_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="enquiryOrderMatrixId != null" >
        enquiry_order_matrix_id,
      </if>
      <if test="matrixId != null" >
        matrix_id,
      </if>
      <if test="serviceItemId != null" >
        service_item_id,
      </if>
      <if test="qty != null" >
        qty,
      </if>
      <if test="sampleNo != null" >
        sample_no,
      </if>
      <if test="createdby != null" >
        CreatedBy,
      </if>
      <if test="createddate != null" >
        CreatedDate,
      </if>
      <if test="modifiedby != null" >
        ModifiedBy,
      </if>
      <if test="modifieddate != null" >
        ModifiedDate,
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="enquiryOrderMatrixId != null" >
        #{enquiryOrderMatrixId,jdbcType=VARCHAR},
      </if>
      <if test="matrixId != null" >
        #{matrixId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemId != null" >
        #{serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="qty != null" >
        #{qty,jdbcType=INTEGER},
      </if>
      <if test="sampleNo != null" >
        #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="createdby != null" >
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeindicator != null" >
        #{activeindicator,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_enquiry_order_matrix_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_enquiry_order_matrix_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryOrderMatrixId != null" >
        enquiry_order_matrix_id = #{record.enquiryOrderMatrixId,jdbcType=VARCHAR},
      </if>
      <if test="record.matrixId != null" >
        matrix_id = #{record.matrixId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItemId != null" >
        service_item_id = #{record.serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="record.qty != null" >
        qty = #{record.qty,jdbcType=INTEGER},
      </if>
      <if test="record.sampleNo != null" >
        sample_no = #{record.sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createdby != null" >
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.createddate != null" >
        CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedby != null" >
        ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="record.modifieddate != null" >
        ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activeindicator != null" >
        ActiveIndicator = #{record.activeindicator,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_enquiry_order_matrix_detail
    set id = #{record.id,jdbcType=VARCHAR},
      enquiry_order_matrix_id = #{record.enquiryOrderMatrixId,jdbcType=VARCHAR},
      matrix_id = #{record.matrixId,jdbcType=VARCHAR},
      service_item_id = #{record.serviceItemId,jdbcType=VARCHAR},
      qty = #{record.qty,jdbcType=INTEGER},
      sample_no = #{record.sampleNo,jdbcType=VARCHAR},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedDate = #{record.createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifieddate,jdbcType=TIMESTAMP},
      ActiveIndicator = #{record.activeindicator,jdbcType=TINYINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoPO" >
    update tb_enquiry_order_matrix_detail
    <set >
      <if test="enquiryOrderMatrixId != null" >
        enquiry_order_matrix_id = #{enquiryOrderMatrixId,jdbcType=VARCHAR},
      </if>
      <if test="matrixId != null" >
        matrix_id = #{matrixId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemId != null" >
        service_item_id = #{serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="qty != null" >
        qty = #{qty,jdbcType=INTEGER},
      </if>
      <if test="sampleNo != null" >
        sample_no = #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="createdby != null" >
        CreatedBy = #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createddate != null" >
        CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      </if>
      <if test="modifieddate != null" >
        ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeindicator != null" >
        ActiveIndicator = #{activeindicator,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryOrderMatrixDetailInfoPO" >
    update tb_enquiry_order_matrix_detail
    set enquiry_order_matrix_id = #{enquiryOrderMatrixId,jdbcType=VARCHAR},
      matrix_id = #{matrixId,jdbcType=VARCHAR},
      service_item_id = #{serviceItemId,jdbcType=VARCHAR},
      qty = #{qty,jdbcType=INTEGER},
      sample_no = #{sampleNo,jdbcType=VARCHAR},
      CreatedBy = #{createdby,jdbcType=VARCHAR},
      CreatedDate = #{createddate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedby,jdbcType=VARCHAR},
      ModifiedDate = #{modifieddate,jdbcType=TIMESTAMP},
      ActiveIndicator = #{activeindicator,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>