<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.BossOrderLineInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="BOSSOrderID" property="BOSSOrderID" jdbcType="VARCHAR" />
    <result column="Lattribute1" property="lattribute1" jdbcType="VARCHAR" />
    <result column="Lattribute12" property="lattribute12" jdbcType="VARCHAR" />
    <result column="Lattribute14" property="lattribute14" jdbcType="VARCHAR" />
    <result column="Lattribute2" property="lattribute2" jdbcType="VARCHAR" />
    <result column="Lattribute4" property="lattribute4" jdbcType="VARCHAR" />
    <result column="Lattribute5" property="lattribute5" jdbcType="VARCHAR" />
    <result column="Lattribute6" property="lattribute6" jdbcType="VARCHAR" />
    <result column="Lattribute8" property="lattribute8" jdbcType="VARCHAR" />
    <result column="Lattribute9" property="lattribute9" jdbcType="VARCHAR" />
    <result column="Lattribute3" property="lattribute3" jdbcType="VARCHAR" />
    <result column="Lattribute7" property="lattribute7" jdbcType="VARCHAR" />
    <result column="Lattribute20" property="lattribute20" jdbcType="VARCHAR" />
    <result column="LbilltoorgID" property="lbilltoorgID" jdbcType="INTEGER" />
    <result column="LcalculatePriceFlag" property="lcalculatePriceFlag" jdbcType="VARCHAR" />
    <result column="LineContext" property="lineContext" jdbcType="VARCHAR" />
    <result column="LinventoryitemID" property="linventoryitemID" jdbcType="VARCHAR" />
    <result column="LitemTypeCode" property="litemTypeCode" jdbcType="VARCHAR" />
    <result column="LoperationCode" property="loperationCode" jdbcType="VARCHAR" />
    <result column="LorderedQuantity" property="lorderedQuantity" jdbcType="INTEGER" />
    <result column="LorderQuantityUom" property="lorderQuantityUom" jdbcType="VARCHAR" />
    <result column="LorigSysLineRef" property="lorigSysLineRef" jdbcType="VARCHAR" />
    <result column="LpaymentTermName" property="lpaymentTermName" jdbcType="VARCHAR" />
    <result column="LpriceListName" property="lpriceListName" jdbcType="VARCHAR" />
    <result column="LsalesrepID" property="lsalesrepID" jdbcType="VARCHAR" />
    <result column="LshipFromOrgName" property="lshipFromOrgName" jdbcType="VARCHAR" />
    <result column="LshiptoorgID" property="lshiptoorgID" jdbcType="VARCHAR" />
    <result column="LsoldFromOrgName" property="lsoldFromOrgName" jdbcType="VARCHAR" />
    <result column="LunitSellingPrice" property="lunitSellingPrice" jdbcType="DECIMAL" />
    <result column="SuccessFlag" property="successFlag" jdbcType="TINYINT" />
    <result column="LtaxCode" property="ltaxCode" jdbcType="VARCHAR" />
    <result column="Lsoldtocust" property="lsoldtocust" jdbcType="VARCHAR" />
    <result column="LcertificateReportNB" property="lcertificateReportNB" jdbcType="VARCHAR" />
    <result column="Lattribute16" property="lattribute16" jdbcType="VARCHAR" />
    <result column="ICreatedDate" property="ICreatedDate" jdbcType="TIMESTAMP" />
    <result column="IModifiedDate" property="IModifiedDate" jdbcType="TIMESTAMP" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="LSalesPerson" property="LSalesPerson" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, BOSSOrderID, Lattribute1, Lattribute12, Lattribute14, Lattribute2, Lattribute4, 
    Lattribute5, Lattribute6, Lattribute8, Lattribute9, Lattribute3, Lattribute7, Lattribute20, 
    LbilltoorgID, LcalculatePriceFlag, LineContext, LinventoryitemID, LitemTypeCode, 
    LoperationCode, LorderedQuantity, LorderQuantityUom, LorigSysLineRef, LpaymentTermName, 
    LpriceListName, LsalesrepID, LshipFromOrgName, LshiptoorgID, LsoldFromOrgName, LunitSellingPrice, 
    SuccessFlag, LtaxCode, Lsoldtocust, LcertificateReportNB, Lattribute16, ICreatedDate, 
    IModifiedDate, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, 
    LSalesPerson
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_boss_order_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_boss_order_line
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_boss_order_line
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoExample" >
    delete from tb_boss_order_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoPO" >
    insert into tb_boss_order_line (ID, BOSSOrderID, Lattribute1, 
      Lattribute12, Lattribute14, Lattribute2, 
      Lattribute4, Lattribute5, Lattribute6, 
      Lattribute8, Lattribute9, Lattribute3, 
      Lattribute7, Lattribute20, LbilltoorgID, 
      LcalculatePriceFlag, LineContext, LinventoryitemID, 
      LitemTypeCode, LoperationCode, LorderedQuantity, 
      LorderQuantityUom, LorigSysLineRef, LpaymentTermName, 
      LpriceListName, LsalesrepID, LshipFromOrgName, 
      LshiptoorgID, LsoldFromOrgName, LunitSellingPrice, 
      SuccessFlag, LtaxCode, Lsoldtocust, 
      LcertificateReportNB, Lattribute16, ICreatedDate, 
      IModifiedDate, ActiveIndicator, CreatedBy, 
      CreatedDate, ModifiedBy, ModifiedDate, 
      LSalesPerson)
    values (#{ID,jdbcType=VARCHAR}, #{BOSSOrderID,jdbcType=VARCHAR}, #{lattribute1,jdbcType=VARCHAR}, 
      #{lattribute12,jdbcType=VARCHAR}, #{lattribute14,jdbcType=VARCHAR}, #{lattribute2,jdbcType=VARCHAR}, 
      #{lattribute4,jdbcType=VARCHAR}, #{lattribute5,jdbcType=VARCHAR}, #{lattribute6,jdbcType=VARCHAR}, 
      #{lattribute8,jdbcType=VARCHAR}, #{lattribute9,jdbcType=VARCHAR}, #{lattribute3,jdbcType=VARCHAR}, 
      #{lattribute7,jdbcType=VARCHAR}, #{lattribute20,jdbcType=VARCHAR}, #{lbilltoorgID,jdbcType=INTEGER}, 
      #{lcalculatePriceFlag,jdbcType=VARCHAR}, #{lineContext,jdbcType=VARCHAR}, #{linventoryitemID,jdbcType=VARCHAR}, 
      #{litemTypeCode,jdbcType=VARCHAR}, #{loperationCode,jdbcType=VARCHAR}, #{lorderedQuantity,jdbcType=INTEGER}, 
      #{lorderQuantityUom,jdbcType=VARCHAR}, #{lorigSysLineRef,jdbcType=VARCHAR}, #{lpaymentTermName,jdbcType=VARCHAR}, 
      #{lpriceListName,jdbcType=VARCHAR}, #{lsalesrepID,jdbcType=VARCHAR}, #{lshipFromOrgName,jdbcType=VARCHAR}, 
      #{lshiptoorgID,jdbcType=VARCHAR}, #{lsoldFromOrgName,jdbcType=VARCHAR}, #{lunitSellingPrice,jdbcType=DECIMAL}, 
      #{successFlag,jdbcType=TINYINT}, #{ltaxCode,jdbcType=VARCHAR}, #{lsoldtocust,jdbcType=VARCHAR},
      #{lcertificateReportNB,jdbcType=VARCHAR}, #{lattribute16,jdbcType=VARCHAR}, #{ICreatedDate,jdbcType=TIMESTAMP}, 
      #{IModifiedDate,jdbcType=TIMESTAMP}, #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP},
      #{LSalesPerson,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoPO" >
    insert into tb_boss_order_line
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="BOSSOrderID != null" >
        BOSSOrderID,
      </if>
      <if test="lattribute1 != null" >
        Lattribute1,
      </if>
      <if test="lattribute12 != null" >
        Lattribute12,
      </if>
      <if test="lattribute14 != null" >
        Lattribute14,
      </if>
      <if test="lattribute2 != null" >
        Lattribute2,
      </if>
      <if test="lattribute4 != null" >
        Lattribute4,
      </if>
      <if test="lattribute5 != null" >
        Lattribute5,
      </if>
      <if test="lattribute6 != null" >
        Lattribute6,
      </if>
      <if test="lattribute8 != null" >
        Lattribute8,
      </if>
      <if test="lattribute9 != null" >
        Lattribute9,
      </if>
      <if test="lattribute3 != null" >
        Lattribute3,
      </if>
      <if test="lattribute7 != null" >
        Lattribute7,
      </if>
      <if test="lattribute20 != null" >
        Lattribute20,
      </if>
      <if test="lbilltoorgID != null" >
        LbilltoorgID,
      </if>
      <if test="lcalculatePriceFlag != null" >
        LcalculatePriceFlag,
      </if>
      <if test="lineContext != null" >
        LineContext,
      </if>
      <if test="linventoryitemID != null" >
        LinventoryitemID,
      </if>
      <if test="litemTypeCode != null" >
        LitemTypeCode,
      </if>
      <if test="loperationCode != null" >
        LoperationCode,
      </if>
      <if test="lorderedQuantity != null" >
        LorderedQuantity,
      </if>
      <if test="lorderQuantityUom != null" >
        LorderQuantityUom,
      </if>
      <if test="lorigSysLineRef != null" >
        LorigSysLineRef,
      </if>
      <if test="lpaymentTermName != null" >
        LpaymentTermName,
      </if>
      <if test="lpriceListName != null" >
        LpriceListName,
      </if>
      <if test="lsalesrepID != null" >
        LsalesrepID,
      </if>
      <if test="lshipFromOrgName != null" >
        LshipFromOrgName,
      </if>
      <if test="lshiptoorgID != null" >
        LshiptoorgID,
      </if>
      <if test="lsoldFromOrgName != null" >
        LsoldFromOrgName,
      </if>
      <if test="lunitSellingPrice != null" >
        LunitSellingPrice,
      </if>
      <if test="successFlag != null" >
        SuccessFlag,
      </if>
      <if test="ltaxCode != null" >
        LtaxCode,
      </if>
      <if test="lsoldtocust != null" >
        Lsoldtocust,
      </if>
      <if test="lcertificateReportNB != null" >
        LcertificateReportNB,
      </if>
      <if test="lattribute16 != null" >
        Lattribute16,
      </if>
      <if test="ICreatedDate != null" >
        ICreatedDate,
      </if>
      <if test="IModifiedDate != null" >
        IModifiedDate,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="LSalesPerson != null" >
        LSalesPerson,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="BOSSOrderID != null" >
        #{BOSSOrderID,jdbcType=VARCHAR},
      </if>
      <if test="lattribute1 != null" >
        #{lattribute1,jdbcType=VARCHAR},
      </if>
      <if test="lattribute12 != null" >
        #{lattribute12,jdbcType=VARCHAR},
      </if>
      <if test="lattribute14 != null" >
        #{lattribute14,jdbcType=VARCHAR},
      </if>
      <if test="lattribute2 != null" >
        #{lattribute2,jdbcType=VARCHAR},
      </if>
      <if test="lattribute4 != null" >
        #{lattribute4,jdbcType=VARCHAR},
      </if>
      <if test="lattribute5 != null" >
        #{lattribute5,jdbcType=VARCHAR},
      </if>
      <if test="lattribute6 != null" >
        #{lattribute6,jdbcType=VARCHAR},
      </if>
      <if test="lattribute8 != null" >
        #{lattribute8,jdbcType=VARCHAR},
      </if>
      <if test="lattribute9 != null" >
        #{lattribute9,jdbcType=VARCHAR},
      </if>
      <if test="lattribute3 != null" >
        #{lattribute3,jdbcType=VARCHAR},
      </if>
      <if test="lattribute7 != null" >
        #{lattribute7,jdbcType=VARCHAR},
      </if>
      <if test="lattribute20 != null" >
        #{lattribute20,jdbcType=VARCHAR},
      </if>
      <if test="lbilltoorgID != null" >
        #{lbilltoorgID,jdbcType=INTEGER},
      </if>
      <if test="lcalculatePriceFlag != null" >
        #{lcalculatePriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="lineContext != null" >
        #{lineContext,jdbcType=VARCHAR},
      </if>
      <if test="linventoryitemID != null" >
        #{linventoryitemID,jdbcType=VARCHAR},
      </if>
      <if test="litemTypeCode != null" >
        #{litemTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="loperationCode != null" >
        #{loperationCode,jdbcType=VARCHAR},
      </if>
      <if test="lorderedQuantity != null" >
        #{lorderedQuantity,jdbcType=INTEGER},
      </if>
      <if test="lorderQuantityUom != null" >
        #{lorderQuantityUom,jdbcType=VARCHAR},
      </if>
      <if test="lorigSysLineRef != null" >
        #{lorigSysLineRef,jdbcType=VARCHAR},
      </if>
      <if test="lpaymentTermName != null" >
        #{lpaymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="lpriceListName != null" >
        #{lpriceListName,jdbcType=VARCHAR},
      </if>
      <if test="lsalesrepID != null" >
        #{lsalesrepID,jdbcType=VARCHAR},
      </if>
      <if test="lshipFromOrgName != null" >
        #{lshipFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="lshiptoorgID != null" >
        #{lshiptoorgID,jdbcType=VARCHAR},
      </if>
      <if test="lsoldFromOrgName != null" >
        #{lsoldFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="lunitSellingPrice != null" >
        #{lunitSellingPrice,jdbcType=DECIMAL},
      </if>
      <if test="successFlag != null" >
        #{successFlag,jdbcType=TINYINT},
      </if>
      <if test="ltaxCode != null" >
        #{ltaxCode,jdbcType=VARCHAR},
      </if>
      <if test="lsoldtocust != null" >
        #{lsoldtocust,jdbcType=VARCHAR},
      </if>
      <if test="lcertificateReportNB != null" >
        #{lcertificateReportNB,jdbcType=VARCHAR},
      </if>
      <if test="lattribute16 != null" >
        #{lattribute16,jdbcType=VARCHAR},
      </if>
      <if test="ICreatedDate != null" >
        #{ICreatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="IModifiedDate != null" >
        #{IModifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="LSalesPerson != null" >
        #{LSalesPerson,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_boss_order_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_boss_order_line
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.BOSSOrderID != null" >
        BOSSOrderID = #{record.BOSSOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute1 != null" >
        Lattribute1 = #{record.lattribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute12 != null" >
        Lattribute12 = #{record.lattribute12,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute14 != null" >
        Lattribute14 = #{record.lattribute14,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute2 != null" >
        Lattribute2 = #{record.lattribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute4 != null" >
        Lattribute4 = #{record.lattribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute5 != null" >
        Lattribute5 = #{record.lattribute5,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute6 != null" >
        Lattribute6 = #{record.lattribute6,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute8 != null" >
        Lattribute8 = #{record.lattribute8,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute9 != null" >
        Lattribute9 = #{record.lattribute9,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute3 != null" >
        Lattribute3 = #{record.lattribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute7 != null" >
        Lattribute7 = #{record.lattribute7,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute20 != null" >
        Lattribute20 = #{record.lattribute20,jdbcType=VARCHAR},
      </if>
      <if test="record.lbilltoorgID != null" >
        LbilltoorgID = #{record.lbilltoorgID,jdbcType=INTEGER},
      </if>
      <if test="record.lcalculatePriceFlag != null" >
        LcalculatePriceFlag = #{record.lcalculatePriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.lineContext != null" >
        LineContext = #{record.lineContext,jdbcType=VARCHAR},
      </if>
      <if test="record.linventoryitemID != null" >
        LinventoryitemID = #{record.linventoryitemID,jdbcType=VARCHAR},
      </if>
      <if test="record.litemTypeCode != null" >
        LitemTypeCode = #{record.litemTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.loperationCode != null" >
        LoperationCode = #{record.loperationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lorderedQuantity != null" >
        LorderedQuantity = #{record.lorderedQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.lorderQuantityUom != null" >
        LorderQuantityUom = #{record.lorderQuantityUom,jdbcType=VARCHAR},
      </if>
      <if test="record.lorigSysLineRef != null" >
        LorigSysLineRef = #{record.lorigSysLineRef,jdbcType=VARCHAR},
      </if>
      <if test="record.lpaymentTermName != null" >
        LpaymentTermName = #{record.lpaymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="record.lpriceListName != null" >
        LpriceListName = #{record.lpriceListName,jdbcType=VARCHAR},
      </if>
      <if test="record.lsalesrepID != null" >
        LsalesrepID = #{record.lsalesrepID,jdbcType=VARCHAR},
      </if>
      <if test="record.lshipFromOrgName != null" >
        LshipFromOrgName = #{record.lshipFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.lshiptoorgID != null" >
        LshiptoorgID = #{record.lshiptoorgID,jdbcType=VARCHAR},
      </if>
      <if test="record.lsoldFromOrgName != null" >
        LsoldFromOrgName = #{record.lsoldFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.lunitSellingPrice != null" >
        LunitSellingPrice = #{record.lunitSellingPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.successFlag != null" >
        SuccessFlag = #{record.successFlag,jdbcType=TINYINT},
      </if>
      <if test="record.ltaxCode != null" >
        LtaxCode = #{record.ltaxCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lsoldtocust != null" >
        Lsoldtocust = #{record.lsoldtocust,jdbcType=VARCHAR},
      </if>
      <if test="record.lcertificateReportNB != null" >
        LcertificateReportNB = #{record.lcertificateReportNB,jdbcType=VARCHAR},
      </if>
      <if test="record.lattribute16 != null" >
        Lattribute16 = #{record.lattribute16,jdbcType=VARCHAR},
      </if>
      <if test="record.ICreatedDate != null" >
        ICreatedDate = #{record.ICreatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.IModifiedDate != null" >
        IModifiedDate = #{record.IModifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.LSalesPerson != null" >
        LSalesPerson = #{record.LSalesPerson,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_boss_order_line
    set ID = #{record.ID,jdbcType=VARCHAR},
      BOSSOrderID = #{record.BOSSOrderID,jdbcType=VARCHAR},
      Lattribute1 = #{record.lattribute1,jdbcType=VARCHAR},
      Lattribute12 = #{record.lattribute12,jdbcType=VARCHAR},
      Lattribute14 = #{record.lattribute14,jdbcType=VARCHAR},
      Lattribute2 = #{record.lattribute2,jdbcType=VARCHAR},
      Lattribute4 = #{record.lattribute4,jdbcType=VARCHAR},
      Lattribute5 = #{record.lattribute5,jdbcType=VARCHAR},
      Lattribute6 = #{record.lattribute6,jdbcType=VARCHAR},
      Lattribute8 = #{record.lattribute8,jdbcType=VARCHAR},
      Lattribute9 = #{record.lattribute9,jdbcType=VARCHAR},
      Lattribute3 = #{record.lattribute3,jdbcType=VARCHAR},
      Lattribute7 = #{record.lattribute7,jdbcType=VARCHAR},
      Lattribute20 = #{record.lattribute20,jdbcType=VARCHAR},
      LbilltoorgID = #{record.lbilltoorgID,jdbcType=INTEGER},
      LcalculatePriceFlag = #{record.lcalculatePriceFlag,jdbcType=VARCHAR},
      LineContext = #{record.lineContext,jdbcType=VARCHAR},
      LinventoryitemID = #{record.linventoryitemID,jdbcType=VARCHAR},
      LitemTypeCode = #{record.litemTypeCode,jdbcType=VARCHAR},
      LoperationCode = #{record.loperationCode,jdbcType=VARCHAR},
      LorderedQuantity = #{record.lorderedQuantity,jdbcType=INTEGER},
      LorderQuantityUom = #{record.lorderQuantityUom,jdbcType=VARCHAR},
      LorigSysLineRef = #{record.lorigSysLineRef,jdbcType=VARCHAR},
      LpaymentTermName = #{record.lpaymentTermName,jdbcType=VARCHAR},
      LpriceListName = #{record.lpriceListName,jdbcType=VARCHAR},
      LsalesrepID = #{record.lsalesrepID,jdbcType=VARCHAR},
      LshipFromOrgName = #{record.lshipFromOrgName,jdbcType=VARCHAR},
      LshiptoorgID = #{record.lshiptoorgID,jdbcType=VARCHAR},
      LsoldFromOrgName = #{record.lsoldFromOrgName,jdbcType=VARCHAR},
      LunitSellingPrice = #{record.lunitSellingPrice,jdbcType=DECIMAL},
      SuccessFlag = #{record.successFlag,jdbcType=TINYINT},
      LtaxCode = #{record.ltaxCode,jdbcType=VARCHAR},
      Lsoldtocust = #{record.lsoldtocust,jdbcType=VARCHAR},
      LcertificateReportNB = #{record.lcertificateReportNB,jdbcType=VARCHAR},
      Lattribute16 = #{record.lattribute16,jdbcType=VARCHAR},
      ICreatedDate = #{record.ICreatedDate,jdbcType=TIMESTAMP},
      IModifiedDate = #{record.IModifiedDate,jdbcType=TIMESTAMP},
      ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      LSalesPerson = #{record.LSalesPerson,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoPO" >
    update tb_boss_order_line
    <set >
      <if test="BOSSOrderID != null" >
        BOSSOrderID = #{BOSSOrderID,jdbcType=VARCHAR},
      </if>
      <if test="lattribute1 != null" >
        Lattribute1 = #{lattribute1,jdbcType=VARCHAR},
      </if>
      <if test="lattribute12 != null" >
        Lattribute12 = #{lattribute12,jdbcType=VARCHAR},
      </if>
      <if test="lattribute14 != null" >
        Lattribute14 = #{lattribute14,jdbcType=VARCHAR},
      </if>
      <if test="lattribute2 != null" >
        Lattribute2 = #{lattribute2,jdbcType=VARCHAR},
      </if>
      <if test="lattribute4 != null" >
        Lattribute4 = #{lattribute4,jdbcType=VARCHAR},
      </if>
      <if test="lattribute5 != null" >
        Lattribute5 = #{lattribute5,jdbcType=VARCHAR},
      </if>
      <if test="lattribute6 != null" >
        Lattribute6 = #{lattribute6,jdbcType=VARCHAR},
      </if>
      <if test="lattribute8 != null" >
        Lattribute8 = #{lattribute8,jdbcType=VARCHAR},
      </if>
      <if test="lattribute9 != null" >
        Lattribute9 = #{lattribute9,jdbcType=VARCHAR},
      </if>
      <if test="lattribute3 != null" >
        Lattribute3 = #{lattribute3,jdbcType=VARCHAR},
      </if>
      <if test="lattribute7 != null" >
        Lattribute7 = #{lattribute7,jdbcType=VARCHAR},
      </if>
      <if test="lattribute20 != null" >
        Lattribute20 = #{lattribute20,jdbcType=VARCHAR},
      </if>
      <if test="lbilltoorgID != null" >
        LbilltoorgID = #{lbilltoorgID,jdbcType=INTEGER},
      </if>
      <if test="lcalculatePriceFlag != null" >
        LcalculatePriceFlag = #{lcalculatePriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="lineContext != null" >
        LineContext = #{lineContext,jdbcType=VARCHAR},
      </if>
      <if test="linventoryitemID != null" >
        LinventoryitemID = #{linventoryitemID,jdbcType=VARCHAR},
      </if>
      <if test="litemTypeCode != null" >
        LitemTypeCode = #{litemTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="loperationCode != null" >
        LoperationCode = #{loperationCode,jdbcType=VARCHAR},
      </if>
      <if test="lorderedQuantity != null" >
        LorderedQuantity = #{lorderedQuantity,jdbcType=INTEGER},
      </if>
      <if test="lorderQuantityUom != null" >
        LorderQuantityUom = #{lorderQuantityUom,jdbcType=VARCHAR},
      </if>
      <if test="lorigSysLineRef != null" >
        LorigSysLineRef = #{lorigSysLineRef,jdbcType=VARCHAR},
      </if>
      <if test="lpaymentTermName != null" >
        LpaymentTermName = #{lpaymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="lpriceListName != null" >
        LpriceListName = #{lpriceListName,jdbcType=VARCHAR},
      </if>
      <if test="lsalesrepID != null" >
        LsalesrepID = #{lsalesrepID,jdbcType=VARCHAR},
      </if>
      <if test="lshipFromOrgName != null" >
        LshipFromOrgName = #{lshipFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="lshiptoorgID != null" >
        LshiptoorgID = #{lshiptoorgID,jdbcType=VARCHAR},
      </if>
      <if test="lsoldFromOrgName != null" >
        LsoldFromOrgName = #{lsoldFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="lunitSellingPrice != null" >
        LunitSellingPrice = #{lunitSellingPrice,jdbcType=DECIMAL},
      </if>
      <if test="successFlag != null" >
        SuccessFlag = #{successFlag,jdbcType=TINYINT},
      </if>
      <if test="ltaxCode != null" >
        LtaxCode = #{ltaxCode,jdbcType=VARCHAR},
      </if>
      <if test="lsoldtocust != null" >
        Lsoldtocust = #{lsoldtocust,jdbcType=VARCHAR},
      </if>
      <if test="lcertificateReportNB != null" >
        LcertificateReportNB = #{lcertificateReportNB,jdbcType=VARCHAR},
      </if>
      <if test="lattribute16 != null" >
        Lattribute16 = #{lattribute16,jdbcType=VARCHAR},
      </if>
      <if test="ICreatedDate != null" >
        ICreatedDate = #{ICreatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="IModifiedDate != null" >
        IModifiedDate = #{IModifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="LSalesPerson != null" >
        LSalesPerson = #{LSalesPerson,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderLineInfoPO" >
    update tb_boss_order_line
    set BOSSOrderID = #{BOSSOrderID,jdbcType=VARCHAR},
      Lattribute1 = #{lattribute1,jdbcType=VARCHAR},
      Lattribute12 = #{lattribute12,jdbcType=VARCHAR},
      Lattribute14 = #{lattribute14,jdbcType=VARCHAR},
      Lattribute2 = #{lattribute2,jdbcType=VARCHAR},
      Lattribute4 = #{lattribute4,jdbcType=VARCHAR},
      Lattribute5 = #{lattribute5,jdbcType=VARCHAR},
      Lattribute6 = #{lattribute6,jdbcType=VARCHAR},
      Lattribute8 = #{lattribute8,jdbcType=VARCHAR},
      Lattribute9 = #{lattribute9,jdbcType=VARCHAR},
      Lattribute3 = #{lattribute3,jdbcType=VARCHAR},
      Lattribute7 = #{lattribute7,jdbcType=VARCHAR},
      Lattribute20 = #{lattribute20,jdbcType=VARCHAR},
      LbilltoorgID = #{lbilltoorgID,jdbcType=INTEGER},
      LcalculatePriceFlag = #{lcalculatePriceFlag,jdbcType=VARCHAR},
      LineContext = #{lineContext,jdbcType=VARCHAR},
      LinventoryitemID = #{linventoryitemID,jdbcType=VARCHAR},
      LitemTypeCode = #{litemTypeCode,jdbcType=VARCHAR},
      LoperationCode = #{loperationCode,jdbcType=VARCHAR},
      LorderedQuantity = #{lorderedQuantity,jdbcType=INTEGER},
      LorderQuantityUom = #{lorderQuantityUom,jdbcType=VARCHAR},
      LorigSysLineRef = #{lorigSysLineRef,jdbcType=VARCHAR},
      LpaymentTermName = #{lpaymentTermName,jdbcType=VARCHAR},
      LpriceListName = #{lpriceListName,jdbcType=VARCHAR},
      LsalesrepID = #{lsalesrepID,jdbcType=VARCHAR},
      LshipFromOrgName = #{lshipFromOrgName,jdbcType=VARCHAR},
      LshiptoorgID = #{lshiptoorgID,jdbcType=VARCHAR},
      LsoldFromOrgName = #{lsoldFromOrgName,jdbcType=VARCHAR},
      LunitSellingPrice = #{lunitSellingPrice,jdbcType=DECIMAL},
      SuccessFlag = #{successFlag,jdbcType=TINYINT},
      LtaxCode = #{ltaxCode,jdbcType=VARCHAR},
      Lsoldtocust = #{lsoldtocust,jdbcType=VARCHAR},
      LcertificateReportNB = #{lcertificateReportNB,jdbcType=VARCHAR},
      Lattribute16 = #{lattribute16,jdbcType=VARCHAR},
      ICreatedDate = #{ICreatedDate,jdbcType=TIMESTAMP},
      IModifiedDate = #{IModifiedDate,jdbcType=TIMESTAMP},
      ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      LSalesPerson = #{LSalesPerson,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>