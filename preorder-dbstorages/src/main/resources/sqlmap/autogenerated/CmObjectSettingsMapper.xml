<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.CmObjectSettingsMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="template_id" property="templateId" jdbcType="VARCHAR" />
    <result column="object_id" property="objectId" jdbcType="VARCHAR" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="is_display" property="isDisplay" jdbcType="SMALLINT" />
    <result column="is_display_condition" property="isDisplayCondition" jdbcType="VARCHAR" />
    <result column="is_required" property="isRequired" jdbcType="SMALLINT" />
    <result column="is_required_condition" property="isRequiredCondition" jdbcType="VARCHAR" />
    <result column="regular" property="regular" jdbcType="VARCHAR" />
    <result column="default_value" property="defaultValue" jdbcType="VARCHAR" />
    <result column="is_expand" property="isExpand" jdbcType="SMALLINT" />
    <result column="is_fixed" property="isFixed" jdbcType="SMALLINT" />
    <result column="same_as_order" property="sameAsOrder" jdbcType="CHAR" />
    <result column="function_expression" property="function" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, template_id, object_id, sort, is_display, is_display_condition, is_required, 
    is_required_condition, regular, default_value, is_expand, is_fixed, same_as_order, 
    function_expression
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_bu_object_settings
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_bu_object_settings
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_bu_object_settings
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsExample" >
    delete from tb_bu_object_settings
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsPO" >
    insert into tb_bu_object_settings (id, template_id, object_id, 
      sort, is_display, is_display_condition, 
      is_required, is_required_condition, regular, 
      default_value, is_expand, is_fixed, 
      same_as_order, function_expression)
    values (#{id,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{isDisplay,jdbcType=SMALLINT}, #{isDisplayCondition,jdbcType=VARCHAR}, 
      #{isRequired,jdbcType=SMALLINT}, #{isRequiredCondition,jdbcType=VARCHAR}, #{regular,jdbcType=VARCHAR}, 
      #{defaultValue,jdbcType=VARCHAR}, #{isExpand,jdbcType=SMALLINT}, #{isFixed,jdbcType=SMALLINT}, 
      #{sameAsOrder,jdbcType=CHAR}, #{function,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsPO" >
    insert into tb_bu_object_settings
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="templateId != null" >
        template_id,
      </if>
      <if test="objectId != null" >
        object_id,
      </if>
      <if test="sort != null" >
        sort,
      </if>
      <if test="isDisplay != null" >
        is_display,
      </if>
      <if test="isDisplayCondition != null" >
        is_display_condition,
      </if>
      <if test="isRequired != null" >
        is_required,
      </if>
      <if test="isRequiredCondition != null" >
        is_required_condition,
      </if>
      <if test="regular != null" >
        regular,
      </if>
      <if test="defaultValue != null" >
        default_value,
      </if>
      <if test="isExpand != null" >
        is_expand,
      </if>
      <if test="isFixed != null" >
        is_fixed,
      </if>
      <if test="sameAsOrder != null" >
        same_as_order,
      </if>
      <if test="function != null" >
        function_expression,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDisplay != null" >
        #{isDisplay,jdbcType=SMALLINT},
      </if>
      <if test="isDisplayCondition != null" >
        #{isDisplayCondition,jdbcType=VARCHAR},
      </if>
      <if test="isRequired != null" >
        #{isRequired,jdbcType=SMALLINT},
      </if>
      <if test="isRequiredCondition != null" >
        #{isRequiredCondition,jdbcType=VARCHAR},
      </if>
      <if test="regular != null" >
        #{regular,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null" >
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="isExpand != null" >
        #{isExpand,jdbcType=SMALLINT},
      </if>
      <if test="isFixed != null" >
        #{isFixed,jdbcType=SMALLINT},
      </if>
      <if test="sameAsOrder != null" >
        #{sameAsOrder,jdbcType=CHAR},
      </if>
      <if test="function != null" >
        #{function,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsExample" resultType="java.lang.Integer" >
    select count(*) from tb_bu_object_settings
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_bu_object_settings
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.objectId != null" >
        object_id = #{record.objectId,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null" >
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.isDisplay != null" >
        is_display = #{record.isDisplay,jdbcType=SMALLINT},
      </if>
      <if test="record.isDisplayCondition != null" >
        is_display_condition = #{record.isDisplayCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.isRequired != null" >
        is_required = #{record.isRequired,jdbcType=SMALLINT},
      </if>
      <if test="record.isRequiredCondition != null" >
        is_required_condition = #{record.isRequiredCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.regular != null" >
        regular = #{record.regular,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultValue != null" >
        default_value = #{record.defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.isExpand != null" >
        is_expand = #{record.isExpand,jdbcType=SMALLINT},
      </if>
      <if test="record.isFixed != null" >
        is_fixed = #{record.isFixed,jdbcType=SMALLINT},
      </if>
      <if test="record.sameAsOrder != null" >
        same_as_order = #{record.sameAsOrder,jdbcType=CHAR},
      </if>
      <if test="record.function != null" >
        function_expression = #{record.function,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_bu_object_settings
    set id = #{record.id,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      object_id = #{record.objectId,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      is_display = #{record.isDisplay,jdbcType=SMALLINT},
      is_display_condition = #{record.isDisplayCondition,jdbcType=VARCHAR},
      is_required = #{record.isRequired,jdbcType=SMALLINT},
      is_required_condition = #{record.isRequiredCondition,jdbcType=VARCHAR},
      regular = #{record.regular,jdbcType=VARCHAR},
      default_value = #{record.defaultValue,jdbcType=VARCHAR},
      is_expand = #{record.isExpand,jdbcType=SMALLINT},
      is_fixed = #{record.isFixed,jdbcType=SMALLINT},
      same_as_order = #{record.sameAsOrder,jdbcType=CHAR},
      function_expression = #{record.function,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsPO" >
    update tb_bu_object_settings
    <set >
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        object_id = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDisplay != null" >
        is_display = #{isDisplay,jdbcType=SMALLINT},
      </if>
      <if test="isDisplayCondition != null" >
        is_display_condition = #{isDisplayCondition,jdbcType=VARCHAR},
      </if>
      <if test="isRequired != null" >
        is_required = #{isRequired,jdbcType=SMALLINT},
      </if>
      <if test="isRequiredCondition != null" >
        is_required_condition = #{isRequiredCondition,jdbcType=VARCHAR},
      </if>
      <if test="regular != null" >
        regular = #{regular,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null" >
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="isExpand != null" >
        is_expand = #{isExpand,jdbcType=SMALLINT},
      </if>
      <if test="isFixed != null" >
        is_fixed = #{isFixed,jdbcType=SMALLINT},
      </if>
      <if test="sameAsOrder != null" >
        same_as_order = #{sameAsOrder,jdbcType=CHAR},
      </if>
      <if test="function != null" >
        function_expression = #{function,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CmObjectSettingsPO" >
    update tb_bu_object_settings
    set template_id = #{templateId,jdbcType=VARCHAR},
      object_id = #{objectId,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      is_display = #{isDisplay,jdbcType=SMALLINT},
      is_display_condition = #{isDisplayCondition,jdbcType=VARCHAR},
      is_required = #{isRequired,jdbcType=SMALLINT},
      is_required_condition = #{isRequiredCondition,jdbcType=VARCHAR},
      regular = #{regular,jdbcType=VARCHAR},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      is_expand = #{isExpand,jdbcType=SMALLINT},
      is_fixed = #{isFixed,jdbcType=SMALLINT},
      same_as_order = #{sameAsOrder,jdbcType=CHAR},
      function_expression = #{function,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>