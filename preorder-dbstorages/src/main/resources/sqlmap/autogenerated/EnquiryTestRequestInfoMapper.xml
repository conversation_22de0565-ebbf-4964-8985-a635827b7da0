<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EnquiryTestRequestInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="EnquiryID" property="enquiryID" jdbcType="VARCHAR" />
    <result column="EvaluationBasis" property="evaluationBasis" jdbcType="VARCHAR" />
    <result column="OtherRequirements" property="otherRequirements" jdbcType="VARCHAR" />
    <result column="Qualification" property="qualification" jdbcType="VARCHAR" />
    <result column="ReoprtDate" property="reoprtDate" jdbcType="TIMESTAMP" />
    <result column="ReportLanguage" property="reportLanguage" jdbcType="VARCHAR" />
    <result column="ReportManner" property="reportManner" jdbcType="INTEGER" />
    <result column="ReportType" property="reportType" jdbcType="VARCHAR" />
    <result column="ResultJudgingFlag" property="resultJudgingFlag" jdbcType="BIT" />
    <result column="ServiceType" property="serviceType" jdbcType="INTEGER" />
    <result column="DisplaySupplierFlag" property="displaySupplierFlag" jdbcType="BIT" />
    <result column="CommentFlag" property="commentFlag" jdbcType="BIT" />
    <result column="HardCopyFlag" property="hardCopyFlag" jdbcType="BIT" />
    <result column="ChineseReportFlag" property="chineseReportFlag" jdbcType="BIT" />
    <result column="TakePhotoFlag" property="takePhotoFlag" jdbcType="BIT" />
    <result column="ConfirmCoverPageFlag" property="confirmCoverPageFlag" jdbcType="BIT" />
    <result column="PackageIndicator" property="packageIndicator" jdbcType="VARCHAR" />
    <result column="TakePhotoRemark" property="takePhotoRemark" jdbcType="VARCHAR" />
    <result column="ReturnResidueSampleFlag" property="returnResidueSampleFlag" jdbcType="BIT" />
    <result column="ReturnTestedSampleFlag" property="returnTestedSampleFlag" jdbcType="BIT" />
    <result column="ReturnResidueSampleRemark" property="returnResidueSampleRemark" jdbcType="VARCHAR" />
    <result column="ReturnTestedSampleRemark" property="returnTestedSampleRemark" jdbcType="VARCHAR" />
    <result column="ReportAccreditationNeededFlag" property="reportAccreditationNeededFlag" jdbcType="BIT" />
    <result column="HardCopyToApplicantFlag" property="hardCopyToApplicantFlag" jdbcType="BIT" />
    <result column="HardCopyToPayertFlag" property="hardCopyToPayertFlag" jdbcType="BIT" />
    <result column="HardCopyToOther" property="hardCopyToOther" jdbcType="VARCHAR" />
    <result column="SoftCopyToApplicantFlag" property="softCopyToApplicantFlag" jdbcType="BIT" />
    <result column="SoftCopyToPayerFlag" property="softCopyToPayerFlag" jdbcType="BIT" />
    <result column="SoftCopyToOther" property="softCopyToOther" jdbcType="VARCHAR" />
    <result column="HtmlString" property="htmlString" jdbcType="VARCHAR" />
    <result column="PdfReportSecurity" property="pdfReportSecurity" jdbcType="BIT" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="PaymentRemark" property="paymentRemark" jdbcType="VARCHAR" />
    <result column="QualificationType" property="qualificationType" jdbcType="VARCHAR" />
    <result column="DraftReportRequired" property="draftReportRequired" jdbcType="TINYINT" />
    <result column="ProformaInvoice" property="proformaInvoice" jdbcType="TINYINT" />
    <result column="LiquidTestSample" property="liquidTestSample" jdbcType="TINYINT" />
    <result column="VatType" property="vatType" jdbcType="TINYINT" />
    <result column="PhotoRequest" property="photoRequest" jdbcType="VARCHAR" />
    <result column="ReportRequirement" property="reportRequirement" jdbcType="VARCHAR" />
    <result column="NeedConclusion" property="needConclusion" jdbcType="BIT" />
    <result column="HardCopyReportDeliverWay" property="hardCopyReportDeliverWay" jdbcType="VARCHAR" />
    <result column="InvoiceDeliverWay" property="invoiceDeliverWay" jdbcType="VARCHAR" />
    <result column="QrcodeFlag" property="qrcodeFlag" jdbcType="VARCHAR" />
    <result column="SealFlag" property="sealFlag" jdbcType="BIT" />
    <result column="SealCode" property="sealCode" jdbcType="VARCHAR" />
    <result column="SampleSaveDuration" property="sampleSaveDuration" jdbcType="INTEGER" />
    <result column="BusinessProjectType" property="businessProjectType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, EnquiryID, EvaluationBasis, OtherRequirements, Qualification, ReoprtDate, ReportLanguage, 
    ReportManner, ReportType, ResultJudgingFlag, ServiceType, DisplaySupplierFlag, CommentFlag, 
    HardCopyFlag, ChineseReportFlag, TakePhotoFlag, ConfirmCoverPageFlag, PackageIndicator, 
    TakePhotoRemark, ReturnResidueSampleFlag, ReturnTestedSampleFlag, ReturnResidueSampleRemark, 
    ReturnTestedSampleRemark, ReportAccreditationNeededFlag, HardCopyToApplicantFlag, 
    HardCopyToPayertFlag, HardCopyToOther, SoftCopyToApplicantFlag, SoftCopyToPayerFlag, 
    SoftCopyToOther, HtmlString, PdfReportSecurity, ActiveIndicator, CreatedBy, CreatedDate, 
    ModifiedBy, ModifiedDate, PaymentRemark, QualificationType,
    DraftReportRequired, ProformaInvoice, LiquidTestSample, VatType, PhotoRequest, ReportRequirement, 
    NeedConclusion, HardCopyReportDeliverWay, InvoiceDeliverWay, QrcodeFlag, SealFlag, 
    SealCode, SampleSaveDuration, BusinessProjectType
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_enquiry_test_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_enquiry_test_request
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_enquiry_test_request
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoExample" >
    delete from tb_enquiry_test_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoPO" >
    insert into tb_enquiry_test_request (ID, EnquiryID, EvaluationBasis, 
      OtherRequirements, Qualification, ReoprtDate, 
      ReportLanguage, ReportManner, ReportType, 
      ResultJudgingFlag, ServiceType, DisplaySupplierFlag, 
      CommentFlag, HardCopyFlag, ChineseReportFlag, 
      TakePhotoFlag, ConfirmCoverPageFlag, PackageIndicator, 
      TakePhotoRemark, ReturnResidueSampleFlag, ReturnTestedSampleFlag, 
      ReturnResidueSampleRemark, ReturnTestedSampleRemark, 
      ReportAccreditationNeededFlag, HardCopyToApplicantFlag, 
      HardCopyToPayertFlag, HardCopyToOther, SoftCopyToApplicantFlag, 
      SoftCopyToPayerFlag, SoftCopyToOther, HtmlString, 
      PdfReportSecurity, ActiveIndicator, CreatedBy, 
      CreatedDate, ModifiedBy, ModifiedDate, 
      PaymentRemark,
      QualificationType, DraftReportRequired, 
      ProformaInvoice, LiquidTestSample, VatType, 
      PhotoRequest, ReportRequirement, NeedConclusion, 
      HardCopyReportDeliverWay, InvoiceDeliverWay, 
      QrcodeFlag, SealFlag, SealCode, 
      SampleSaveDuration, BusinessProjectType
      )
    values (#{ID,jdbcType=VARCHAR}, #{enquiryID,jdbcType=VARCHAR}, #{evaluationBasis,jdbcType=VARCHAR}, 
      #{otherRequirements,jdbcType=VARCHAR}, #{qualification,jdbcType=VARCHAR}, #{reoprtDate,jdbcType=TIMESTAMP}, 
      #{reportLanguage,jdbcType=VARCHAR}, #{reportManner,jdbcType=INTEGER}, #{reportType,jdbcType=VARCHAR}, 
      #{resultJudgingFlag,jdbcType=BIT}, #{serviceType,jdbcType=INTEGER}, #{displaySupplierFlag,jdbcType=BIT}, 
      #{commentFlag,jdbcType=BIT}, #{hardCopyFlag,jdbcType=BIT}, #{chineseReportFlag,jdbcType=BIT}, 
      #{takePhotoFlag,jdbcType=BIT}, #{confirmCoverPageFlag,jdbcType=BIT}, #{packageIndicator,jdbcType=VARCHAR}, 
      #{takePhotoRemark,jdbcType=VARCHAR}, #{returnResidueSampleFlag,jdbcType=BIT}, #{returnTestedSampleFlag,jdbcType=BIT}, 
      #{returnResidueSampleRemark,jdbcType=VARCHAR}, #{returnTestedSampleRemark,jdbcType=VARCHAR}, 
      #{reportAccreditationNeededFlag,jdbcType=BIT}, #{hardCopyToApplicantFlag,jdbcType=BIT}, 
      #{hardCopyToPayertFlag,jdbcType=BIT}, #{hardCopyToOther,jdbcType=VARCHAR}, #{softCopyToApplicantFlag,jdbcType=BIT}, 
      #{softCopyToPayerFlag,jdbcType=BIT}, #{softCopyToOther,jdbcType=VARCHAR}, #{htmlString,jdbcType=VARCHAR}, 
      #{pdfReportSecurity,jdbcType=BIT}, #{activeIndicator,jdbcType=BIT}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{paymentRemark,jdbcType=VARCHAR},
      #{qualificationType,jdbcType=VARCHAR}, #{draftReportRequired,jdbcType=TINYINT},
      #{proformaInvoice,jdbcType=TINYINT}, #{liquidTestSample,jdbcType=TINYINT}, #{vatType,jdbcType=TINYINT}, 
      #{photoRequest,jdbcType=VARCHAR}, #{reportRequirement,jdbcType=VARCHAR}, #{needConclusion,jdbcType=BIT}, 
      #{hardCopyReportDeliverWay,jdbcType=VARCHAR}, #{invoiceDeliverWay,jdbcType=VARCHAR}, 
      #{qrcodeFlag,jdbcType=VARCHAR}, #{sealFlag,jdbcType=BIT}, #{sealCode,jdbcType=VARCHAR}, 
      #{sampleSaveDuration,jdbcType=INTEGER}, #{businessProjectType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoPO" >
    insert into tb_enquiry_test_request
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="enquiryID != null" >
        EnquiryID,
      </if>
      <if test="evaluationBasis != null" >
        EvaluationBasis,
      </if>
      <if test="otherRequirements != null" >
        OtherRequirements,
      </if>
      <if test="qualification != null" >
        Qualification,
      </if>
      <if test="reoprtDate != null" >
        ReoprtDate,
      </if>
      <if test="reportLanguage != null" >
        ReportLanguage,
      </if>
      <if test="reportManner != null" >
        ReportManner,
      </if>
      <if test="reportType != null" >
        ReportType,
      </if>
      <if test="resultJudgingFlag != null" >
        ResultJudgingFlag,
      </if>
      <if test="serviceType != null" >
        ServiceType,
      </if>
      <if test="displaySupplierFlag != null" >
        DisplaySupplierFlag,
      </if>
      <if test="commentFlag != null" >
        CommentFlag,
      </if>
      <if test="hardCopyFlag != null" >
        HardCopyFlag,
      </if>
      <if test="chineseReportFlag != null" >
        ChineseReportFlag,
      </if>
      <if test="takePhotoFlag != null" >
        TakePhotoFlag,
      </if>
      <if test="confirmCoverPageFlag != null" >
        ConfirmCoverPageFlag,
      </if>
      <if test="packageIndicator != null" >
        PackageIndicator,
      </if>
      <if test="takePhotoRemark != null" >
        TakePhotoRemark,
      </if>
      <if test="returnResidueSampleFlag != null" >
        ReturnResidueSampleFlag,
      </if>
      <if test="returnTestedSampleFlag != null" >
        ReturnTestedSampleFlag,
      </if>
      <if test="returnResidueSampleRemark != null" >
        ReturnResidueSampleRemark,
      </if>
      <if test="returnTestedSampleRemark != null" >
        ReturnTestedSampleRemark,
      </if>
      <if test="reportAccreditationNeededFlag != null" >
        ReportAccreditationNeededFlag,
      </if>
      <if test="hardCopyToApplicantFlag != null" >
        HardCopyToApplicantFlag,
      </if>
      <if test="hardCopyToPayertFlag != null" >
        HardCopyToPayertFlag,
      </if>
      <if test="hardCopyToOther != null" >
        HardCopyToOther,
      </if>
      <if test="softCopyToApplicantFlag != null" >
        SoftCopyToApplicantFlag,
      </if>
      <if test="softCopyToPayerFlag != null" >
        SoftCopyToPayerFlag,
      </if>
      <if test="softCopyToOther != null" >
        SoftCopyToOther,
      </if>
      <if test="htmlString != null" >
        HtmlString,
      </if>
      <if test="pdfReportSecurity != null" >
        PdfReportSecurity,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="paymentRemark != null" >
        PaymentRemark,
      </if>
      <if test="qualificationType != null" >
        QualificationType,
      </if>
      <if test="draftReportRequired != null" >
        DraftReportRequired,
      </if>
      <if test="proformaInvoice != null" >
        ProformaInvoice,
      </if>
      <if test="liquidTestSample != null" >
        LiquidTestSample,
      </if>
      <if test="vatType != null" >
        VatType,
      </if>
      <if test="photoRequest != null" >
        PhotoRequest,
      </if>
      <if test="reportRequirement != null" >
        ReportRequirement,
      </if>
      <if test="needConclusion != null" >
        NeedConclusion,
      </if>
      <if test="hardCopyReportDeliverWay != null" >
        HardCopyReportDeliverWay,
      </if>
      <if test="invoiceDeliverWay != null" >
        InvoiceDeliverWay,
      </if>
      <if test="qrcodeFlag != null" >
        QrcodeFlag,
      </if>
      <if test="sealFlag != null" >
        SealFlag,
      </if>
      <if test="sealCode != null" >
        SealCode,
      </if>
      <if test="sampleSaveDuration != null" >
        SampleSaveDuration,
      </if>
      <if test="businessProjectType != null" >
        BusinessProjectType,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="enquiryID != null" >
        #{enquiryID,jdbcType=VARCHAR},
      </if>
      <if test="evaluationBasis != null" >
        #{evaluationBasis,jdbcType=VARCHAR},
      </if>
      <if test="otherRequirements != null" >
        #{otherRequirements,jdbcType=VARCHAR},
      </if>
      <if test="qualification != null" >
        #{qualification,jdbcType=VARCHAR},
      </if>
      <if test="reoprtDate != null" >
        #{reoprtDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportLanguage != null" >
        #{reportLanguage,jdbcType=VARCHAR},
      </if>
      <if test="reportManner != null" >
        #{reportManner,jdbcType=INTEGER},
      </if>
      <if test="reportType != null" >
        #{reportType,jdbcType=VARCHAR},
      </if>
      <if test="resultJudgingFlag != null" >
        #{resultJudgingFlag,jdbcType=BIT},
      </if>
      <if test="serviceType != null" >
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="displaySupplierFlag != null" >
        #{displaySupplierFlag,jdbcType=BIT},
      </if>
      <if test="commentFlag != null" >
        #{commentFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyFlag != null" >
        #{hardCopyFlag,jdbcType=BIT},
      </if>
      <if test="chineseReportFlag != null" >
        #{chineseReportFlag,jdbcType=BIT},
      </if>
      <if test="takePhotoFlag != null" >
        #{takePhotoFlag,jdbcType=BIT},
      </if>
      <if test="confirmCoverPageFlag != null" >
        #{confirmCoverPageFlag,jdbcType=BIT},
      </if>
      <if test="packageIndicator != null" >
        #{packageIndicator,jdbcType=VARCHAR},
      </if>
      <if test="takePhotoRemark != null" >
        #{takePhotoRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnResidueSampleFlag != null" >
        #{returnResidueSampleFlag,jdbcType=BIT},
      </if>
      <if test="returnTestedSampleFlag != null" >
        #{returnTestedSampleFlag,jdbcType=BIT},
      </if>
      <if test="returnResidueSampleRemark != null" >
        #{returnResidueSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnTestedSampleRemark != null" >
        #{returnTestedSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="reportAccreditationNeededFlag != null" >
        #{reportAccreditationNeededFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyToApplicantFlag != null" >
        #{hardCopyToApplicantFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyToPayertFlag != null" >
        #{hardCopyToPayertFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyToOther != null" >
        #{hardCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="softCopyToApplicantFlag != null" >
        #{softCopyToApplicantFlag,jdbcType=BIT},
      </if>
      <if test="softCopyToPayerFlag != null" >
        #{softCopyToPayerFlag,jdbcType=BIT},
      </if>
      <if test="softCopyToOther != null" >
        #{softCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="htmlString != null" >
        #{htmlString,jdbcType=VARCHAR},
      </if>
      <if test="pdfReportSecurity != null" >
        #{pdfReportSecurity,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentRemark != null" >
        #{paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="qualificationType != null" >
        #{qualificationType,jdbcType=VARCHAR},
      </if>
      <if test="draftReportRequired != null" >
        #{draftReportRequired,jdbcType=TINYINT},
      </if>
      <if test="proformaInvoice != null" >
        #{proformaInvoice,jdbcType=TINYINT},
      </if>
      <if test="liquidTestSample != null" >
        #{liquidTestSample,jdbcType=TINYINT},
      </if>
      <if test="vatType != null" >
        #{vatType,jdbcType=TINYINT},
      </if>
      <if test="photoRequest != null" >
        #{photoRequest,jdbcType=VARCHAR},
      </if>
      <if test="reportRequirement != null" >
        #{reportRequirement,jdbcType=VARCHAR},
      </if>
      <if test="needConclusion != null" >
        #{needConclusion,jdbcType=BIT},
      </if>
      <if test="hardCopyReportDeliverWay != null" >
        #{hardCopyReportDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDeliverWay != null" >
        #{invoiceDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="qrcodeFlag != null" >
        #{qrcodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="sealFlag != null" >
        #{sealFlag,jdbcType=BIT},
      </if>
      <if test="sealCode != null" >
        #{sealCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleSaveDuration != null" >
        #{sampleSaveDuration,jdbcType=INTEGER},
      </if>
      <if test="businessProjectType != null" >
        #{businessProjectType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_enquiry_test_request
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_enquiry_test_request
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryID != null" >
        EnquiryID = #{record.enquiryID,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluationBasis != null" >
        EvaluationBasis = #{record.evaluationBasis,jdbcType=VARCHAR},
      </if>
      <if test="record.otherRequirements != null" >
        OtherRequirements = #{record.otherRequirements,jdbcType=VARCHAR},
      </if>
      <if test="record.qualification != null" >
        Qualification = #{record.qualification,jdbcType=VARCHAR},
      </if>
      <if test="record.reoprtDate != null" >
        ReoprtDate = #{record.reoprtDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportLanguage != null" >
        ReportLanguage = #{record.reportLanguage,jdbcType=VARCHAR},
      </if>
      <if test="record.reportManner != null" >
        ReportManner = #{record.reportManner,jdbcType=INTEGER},
      </if>
      <if test="record.reportType != null" >
        ReportType = #{record.reportType,jdbcType=VARCHAR},
      </if>
      <if test="record.resultJudgingFlag != null" >
        ResultJudgingFlag = #{record.resultJudgingFlag,jdbcType=BIT},
      </if>
      <if test="record.serviceType != null" >
        ServiceType = #{record.serviceType,jdbcType=INTEGER},
      </if>
      <if test="record.displaySupplierFlag != null" >
        DisplaySupplierFlag = #{record.displaySupplierFlag,jdbcType=BIT},
      </if>
      <if test="record.commentFlag != null" >
        CommentFlag = #{record.commentFlag,jdbcType=BIT},
      </if>
      <if test="record.hardCopyFlag != null" >
        HardCopyFlag = #{record.hardCopyFlag,jdbcType=BIT},
      </if>
      <if test="record.chineseReportFlag != null" >
        ChineseReportFlag = #{record.chineseReportFlag,jdbcType=BIT},
      </if>
      <if test="record.takePhotoFlag != null" >
        TakePhotoFlag = #{record.takePhotoFlag,jdbcType=BIT},
      </if>
      <if test="record.confirmCoverPageFlag != null" >
        ConfirmCoverPageFlag = #{record.confirmCoverPageFlag,jdbcType=BIT},
      </if>
      <if test="record.packageIndicator != null" >
        PackageIndicator = #{record.packageIndicator,jdbcType=VARCHAR},
      </if>
      <if test="record.takePhotoRemark != null" >
        TakePhotoRemark = #{record.takePhotoRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.returnResidueSampleFlag != null" >
        ReturnResidueSampleFlag = #{record.returnResidueSampleFlag,jdbcType=BIT},
      </if>
      <if test="record.returnTestedSampleFlag != null" >
        ReturnTestedSampleFlag = #{record.returnTestedSampleFlag,jdbcType=BIT},
      </if>
      <if test="record.returnResidueSampleRemark != null" >
        ReturnResidueSampleRemark = #{record.returnResidueSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.returnTestedSampleRemark != null" >
        ReturnTestedSampleRemark = #{record.returnTestedSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAccreditationNeededFlag != null" >
        ReportAccreditationNeededFlag = #{record.reportAccreditationNeededFlag,jdbcType=BIT},
      </if>
      <if test="record.hardCopyToApplicantFlag != null" >
        HardCopyToApplicantFlag = #{record.hardCopyToApplicantFlag,jdbcType=BIT},
      </if>
      <if test="record.hardCopyToPayertFlag != null" >
        HardCopyToPayertFlag = #{record.hardCopyToPayertFlag,jdbcType=BIT},
      </if>
      <if test="record.hardCopyToOther != null" >
        HardCopyToOther = #{record.hardCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="record.softCopyToApplicantFlag != null" >
        SoftCopyToApplicantFlag = #{record.softCopyToApplicantFlag,jdbcType=BIT},
      </if>
      <if test="record.softCopyToPayerFlag != null" >
        SoftCopyToPayerFlag = #{record.softCopyToPayerFlag,jdbcType=BIT},
      </if>
      <if test="record.softCopyToOther != null" >
        SoftCopyToOther = #{record.softCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="record.htmlString != null" >
        HtmlString = #{record.htmlString,jdbcType=VARCHAR},
      </if>
      <if test="record.pdfReportSecurity != null" >
        PdfReportSecurity = #{record.pdfReportSecurity,jdbcType=BIT},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paymentRemark != null" >
        PaymentRemark = #{record.paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.qualificationType != null" >
        QualificationType = #{record.qualificationType,jdbcType=VARCHAR},
      </if>
      <if test="record.draftReportRequired != null" >
        DraftReportRequired = #{record.draftReportRequired,jdbcType=TINYINT},
      </if>
      <if test="record.proformaInvoice != null" >
        ProformaInvoice = #{record.proformaInvoice,jdbcType=TINYINT},
      </if>
      <if test="record.liquidTestSample != null" >
        LiquidTestSample = #{record.liquidTestSample,jdbcType=TINYINT},
      </if>
      <if test="record.vatType != null" >
        VatType = #{record.vatType,jdbcType=TINYINT},
      </if>
      <if test="record.photoRequest != null" >
        PhotoRequest = #{record.photoRequest,jdbcType=VARCHAR},
      </if>
      <if test="record.reportRequirement != null" >
        ReportRequirement = #{record.reportRequirement,jdbcType=VARCHAR},
      </if>
      <if test="record.needConclusion != null" >
        NeedConclusion = #{record.needConclusion,jdbcType=BIT},
      </if>
      <if test="record.hardCopyReportDeliverWay != null" >
        HardCopyReportDeliverWay = #{record.hardCopyReportDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceDeliverWay != null" >
        InvoiceDeliverWay = #{record.invoiceDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="record.qrcodeFlag != null" >
        QrcodeFlag = #{record.qrcodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.sealFlag != null" >
        SealFlag = #{record.sealFlag,jdbcType=BIT},
      </if>
      <if test="record.sealCode != null" >
        SealCode = #{record.sealCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleSaveDuration != null" >
        SampleSaveDuration = #{record.sampleSaveDuration,jdbcType=INTEGER},
      </if>
      <if test="record.businessProjectType != null" >
        BusinessProjectType = #{record.businessProjectType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_enquiry_test_request
    set ID = #{record.ID,jdbcType=VARCHAR},
      EnquiryID = #{record.enquiryID,jdbcType=VARCHAR},
      EvaluationBasis = #{record.evaluationBasis,jdbcType=VARCHAR},
      OtherRequirements = #{record.otherRequirements,jdbcType=VARCHAR},
      Qualification = #{record.qualification,jdbcType=VARCHAR},
      ReoprtDate = #{record.reoprtDate,jdbcType=TIMESTAMP},
      ReportLanguage = #{record.reportLanguage,jdbcType=VARCHAR},
      ReportManner = #{record.reportManner,jdbcType=INTEGER},
      ReportType = #{record.reportType,jdbcType=VARCHAR},
      ResultJudgingFlag = #{record.resultJudgingFlag,jdbcType=BIT},
      ServiceType = #{record.serviceType,jdbcType=INTEGER},
      DisplaySupplierFlag = #{record.displaySupplierFlag,jdbcType=BIT},
      CommentFlag = #{record.commentFlag,jdbcType=BIT},
      HardCopyFlag = #{record.hardCopyFlag,jdbcType=BIT},
      ChineseReportFlag = #{record.chineseReportFlag,jdbcType=BIT},
      TakePhotoFlag = #{record.takePhotoFlag,jdbcType=BIT},
      ConfirmCoverPageFlag = #{record.confirmCoverPageFlag,jdbcType=BIT},
      PackageIndicator = #{record.packageIndicator,jdbcType=VARCHAR},
      TakePhotoRemark = #{record.takePhotoRemark,jdbcType=VARCHAR},
      ReturnResidueSampleFlag = #{record.returnResidueSampleFlag,jdbcType=BIT},
      ReturnTestedSampleFlag = #{record.returnTestedSampleFlag,jdbcType=BIT},
      ReturnResidueSampleRemark = #{record.returnResidueSampleRemark,jdbcType=VARCHAR},
      ReturnTestedSampleRemark = #{record.returnTestedSampleRemark,jdbcType=VARCHAR},
      ReportAccreditationNeededFlag = #{record.reportAccreditationNeededFlag,jdbcType=BIT},
      HardCopyToApplicantFlag = #{record.hardCopyToApplicantFlag,jdbcType=BIT},
      HardCopyToPayertFlag = #{record.hardCopyToPayertFlag,jdbcType=BIT},
      HardCopyToOther = #{record.hardCopyToOther,jdbcType=VARCHAR},
      SoftCopyToApplicantFlag = #{record.softCopyToApplicantFlag,jdbcType=BIT},
      SoftCopyToPayerFlag = #{record.softCopyToPayerFlag,jdbcType=BIT},
      SoftCopyToOther = #{record.softCopyToOther,jdbcType=VARCHAR},
      HtmlString = #{record.htmlString,jdbcType=VARCHAR},
      PdfReportSecurity = #{record.pdfReportSecurity,jdbcType=BIT},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      PaymentRemark = #{record.paymentRemark,jdbcType=VARCHAR},
      QualificationType = #{record.qualificationType,jdbcType=VARCHAR},
      DraftReportRequired = #{record.draftReportRequired,jdbcType=TINYINT},
      ProformaInvoice = #{record.proformaInvoice,jdbcType=TINYINT},
      LiquidTestSample = #{record.liquidTestSample,jdbcType=TINYINT},
      VatType = #{record.vatType,jdbcType=TINYINT},
      PhotoRequest = #{record.photoRequest,jdbcType=VARCHAR},
      ReportRequirement = #{record.reportRequirement,jdbcType=VARCHAR},
      NeedConclusion = #{record.needConclusion,jdbcType=BIT},
      HardCopyReportDeliverWay = #{record.hardCopyReportDeliverWay,jdbcType=VARCHAR},
      InvoiceDeliverWay = #{record.invoiceDeliverWay,jdbcType=VARCHAR},
      QrcodeFlag = #{record.qrcodeFlag,jdbcType=VARCHAR},
      SealFlag = #{record.sealFlag,jdbcType=BIT},
      SealCode = #{record.sealCode,jdbcType=VARCHAR},
      SampleSaveDuration = #{record.sampleSaveDuration,jdbcType=INTEGER},
      BusinessProjectType = #{record.businessProjectType,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoPO" >
    update tb_enquiry_test_request
    <set >
      <if test="enquiryID != null" >
        EnquiryID = #{enquiryID,jdbcType=VARCHAR},
      </if>
      <if test="evaluationBasis != null" >
        EvaluationBasis = #{evaluationBasis,jdbcType=VARCHAR},
      </if>
      <if test="otherRequirements != null" >
        OtherRequirements = #{otherRequirements,jdbcType=VARCHAR},
      </if>
      <if test="qualification != null" >
        Qualification = #{qualification,jdbcType=VARCHAR},
      </if>
      <if test="reoprtDate != null" >
        ReoprtDate = #{reoprtDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportLanguage != null" >
        ReportLanguage = #{reportLanguage,jdbcType=VARCHAR},
      </if>
      <if test="reportManner != null" >
        ReportManner = #{reportManner,jdbcType=INTEGER},
      </if>
      <if test="reportType != null" >
        ReportType = #{reportType,jdbcType=VARCHAR},
      </if>
      <if test="resultJudgingFlag != null" >
        ResultJudgingFlag = #{resultJudgingFlag,jdbcType=BIT},
      </if>
      <if test="serviceType != null" >
        ServiceType = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="displaySupplierFlag != null" >
        DisplaySupplierFlag = #{displaySupplierFlag,jdbcType=BIT},
      </if>
      <if test="commentFlag != null" >
        CommentFlag = #{commentFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyFlag != null" >
        HardCopyFlag = #{hardCopyFlag,jdbcType=BIT},
      </if>
      <if test="chineseReportFlag != null" >
        ChineseReportFlag = #{chineseReportFlag,jdbcType=BIT},
      </if>
      <if test="takePhotoFlag != null" >
        TakePhotoFlag = #{takePhotoFlag,jdbcType=BIT},
      </if>
      <if test="confirmCoverPageFlag != null" >
        ConfirmCoverPageFlag = #{confirmCoverPageFlag,jdbcType=BIT},
      </if>
      <if test="packageIndicator != null" >
        PackageIndicator = #{packageIndicator,jdbcType=VARCHAR},
      </if>
      <if test="takePhotoRemark != null" >
        TakePhotoRemark = #{takePhotoRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnResidueSampleFlag != null" >
        ReturnResidueSampleFlag = #{returnResidueSampleFlag,jdbcType=BIT},
      </if>
      <if test="returnTestedSampleFlag != null" >
        ReturnTestedSampleFlag = #{returnTestedSampleFlag,jdbcType=BIT},
      </if>
      <if test="returnResidueSampleRemark != null" >
        ReturnResidueSampleRemark = #{returnResidueSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="returnTestedSampleRemark != null" >
        ReturnTestedSampleRemark = #{returnTestedSampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="reportAccreditationNeededFlag != null" >
        ReportAccreditationNeededFlag = #{reportAccreditationNeededFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyToApplicantFlag != null" >
        HardCopyToApplicantFlag = #{hardCopyToApplicantFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyToPayertFlag != null" >
        HardCopyToPayertFlag = #{hardCopyToPayertFlag,jdbcType=BIT},
      </if>
      <if test="hardCopyToOther != null" >
        HardCopyToOther = #{hardCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="softCopyToApplicantFlag != null" >
        SoftCopyToApplicantFlag = #{softCopyToApplicantFlag,jdbcType=BIT},
      </if>
      <if test="softCopyToPayerFlag != null" >
        SoftCopyToPayerFlag = #{softCopyToPayerFlag,jdbcType=BIT},
      </if>
      <if test="softCopyToOther != null" >
        SoftCopyToOther = #{softCopyToOther,jdbcType=VARCHAR},
      </if>
      <if test="htmlString != null" >
        HtmlString = #{htmlString,jdbcType=VARCHAR},
      </if>
      <if test="pdfReportSecurity != null" >
        PdfReportSecurity = #{pdfReportSecurity,jdbcType=BIT},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentRemark != null" >
        PaymentRemark = #{paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="qualificationType != null" >
        QualificationType = #{qualificationType,jdbcType=VARCHAR},
      </if>
      <if test="draftReportRequired != null" >
        DraftReportRequired = #{draftReportRequired,jdbcType=TINYINT},
      </if>
      <if test="proformaInvoice != null" >
        ProformaInvoice = #{proformaInvoice,jdbcType=TINYINT},
      </if>
      <if test="liquidTestSample != null" >
        LiquidTestSample = #{liquidTestSample,jdbcType=TINYINT},
      </if>
      <if test="vatType != null" >
        VatType = #{vatType,jdbcType=TINYINT},
      </if>
      <if test="photoRequest != null" >
        PhotoRequest = #{photoRequest,jdbcType=VARCHAR},
      </if>
      <if test="reportRequirement != null" >
        ReportRequirement = #{reportRequirement,jdbcType=VARCHAR},
      </if>
      <if test="needConclusion != null" >
        NeedConclusion = #{needConclusion,jdbcType=BIT},
      </if>
      <if test="hardCopyReportDeliverWay != null" >
        HardCopyReportDeliverWay = #{hardCopyReportDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDeliverWay != null" >
        InvoiceDeliverWay = #{invoiceDeliverWay,jdbcType=VARCHAR},
      </if>
      <if test="qrcodeFlag != null" >
        QrcodeFlag = #{qrcodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="sealFlag != null" >
        SealFlag = #{sealFlag,jdbcType=BIT},
      </if>
      <if test="sealCode != null" >
        SealCode = #{sealCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleSaveDuration != null" >
        SampleSaveDuration = #{sampleSaveDuration,jdbcType=INTEGER},
      </if>
      <if test="businessProjectType != null" >
        BusinessProjectType = #{businessProjectType,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EnquiryTestRequestInfoPO" >
    update tb_enquiry_test_request
    set EnquiryID = #{enquiryID,jdbcType=VARCHAR},
      EvaluationBasis = #{evaluationBasis,jdbcType=VARCHAR},
      OtherRequirements = #{otherRequirements,jdbcType=VARCHAR},
      Qualification = #{qualification,jdbcType=VARCHAR},
      ReoprtDate = #{reoprtDate,jdbcType=TIMESTAMP},
      ReportLanguage = #{reportLanguage,jdbcType=VARCHAR},
      ReportManner = #{reportManner,jdbcType=INTEGER},
      ReportType = #{reportType,jdbcType=VARCHAR},
      ResultJudgingFlag = #{resultJudgingFlag,jdbcType=BIT},
      ServiceType = #{serviceType,jdbcType=INTEGER},
      DisplaySupplierFlag = #{displaySupplierFlag,jdbcType=BIT},
      CommentFlag = #{commentFlag,jdbcType=BIT},
      HardCopyFlag = #{hardCopyFlag,jdbcType=BIT},
      ChineseReportFlag = #{chineseReportFlag,jdbcType=BIT},
      TakePhotoFlag = #{takePhotoFlag,jdbcType=BIT},
      ConfirmCoverPageFlag = #{confirmCoverPageFlag,jdbcType=BIT},
      PackageIndicator = #{packageIndicator,jdbcType=VARCHAR},
      TakePhotoRemark = #{takePhotoRemark,jdbcType=VARCHAR},
      ReturnResidueSampleFlag = #{returnResidueSampleFlag,jdbcType=BIT},
      ReturnTestedSampleFlag = #{returnTestedSampleFlag,jdbcType=BIT},
      ReturnResidueSampleRemark = #{returnResidueSampleRemark,jdbcType=VARCHAR},
      ReturnTestedSampleRemark = #{returnTestedSampleRemark,jdbcType=VARCHAR},
      ReportAccreditationNeededFlag = #{reportAccreditationNeededFlag,jdbcType=BIT},
      HardCopyToApplicantFlag = #{hardCopyToApplicantFlag,jdbcType=BIT},
      HardCopyToPayertFlag = #{hardCopyToPayertFlag,jdbcType=BIT},
      HardCopyToOther = #{hardCopyToOther,jdbcType=VARCHAR},
      SoftCopyToApplicantFlag = #{softCopyToApplicantFlag,jdbcType=BIT},
      SoftCopyToPayerFlag = #{softCopyToPayerFlag,jdbcType=BIT},
      SoftCopyToOther = #{softCopyToOther,jdbcType=VARCHAR},
      HtmlString = #{htmlString,jdbcType=VARCHAR},
      PdfReportSecurity = #{pdfReportSecurity,jdbcType=BIT},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      PaymentRemark = #{paymentRemark,jdbcType=VARCHAR},
      QualificationType = #{qualificationType,jdbcType=VARCHAR},
      DraftReportRequired = #{draftReportRequired,jdbcType=TINYINT},
      ProformaInvoice = #{proformaInvoice,jdbcType=TINYINT},
      LiquidTestSample = #{liquidTestSample,jdbcType=TINYINT},
      VatType = #{vatType,jdbcType=TINYINT},
      PhotoRequest = #{photoRequest,jdbcType=VARCHAR},
      ReportRequirement = #{reportRequirement,jdbcType=VARCHAR},
      NeedConclusion = #{needConclusion,jdbcType=BIT},
      HardCopyReportDeliverWay = #{hardCopyReportDeliverWay,jdbcType=VARCHAR},
      InvoiceDeliverWay = #{invoiceDeliverWay,jdbcType=VARCHAR},
      QrcodeFlag = #{qrcodeFlag,jdbcType=VARCHAR},
      SealFlag = #{sealFlag,jdbcType=BIT},
      SealCode = #{sealCode,jdbcType=VARCHAR},
      SampleSaveDuration = #{sampleSaveDuration,jdbcType=INTEGER},
      BusinessProjectType = #{businessProjectType,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>