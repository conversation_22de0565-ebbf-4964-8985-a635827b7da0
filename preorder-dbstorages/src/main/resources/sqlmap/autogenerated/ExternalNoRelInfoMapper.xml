<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.ExternalNoRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoPO" >
    <id column="Id" property="id" jdbcType="INTEGER" />
    <result column="GeneralOrderId" property="generalOrderId" jdbcType="VARCHAR" />
    <result column="ExternalOrderNo" property="externalOrderNo" jdbcType="VARCHAR" />
    <result column="ExternalOrderNoSuffixNum" property="externalOrderNoSuffixNum" jdbcType="VARCHAR" />
    <result column="ReportId" property="reportId" jdbcType="VARCHAR" />
    <result column="ExternalReportNo" property="externalReportNo" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifitedDate" property="modifitedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, GeneralOrderId, ExternalOrderNo, ExternalOrderNoSuffixNum, ReportId, ExternalReportNo, 
    ActiveIndicator, CreatedDate, CreatedBy, ModifitedDate, ModifiedBy
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_external_no_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tb_external_no_rel
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_external_no_rel
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoExample" >
    delete from tb_external_no_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoPO" >
    insert into tb_external_no_rel (Id, GeneralOrderId, ExternalOrderNo, 
      ExternalOrderNoSuffixNum, ReportId, ExternalReportNo, 
      ActiveIndicator, CreatedDate, CreatedBy, 
      ModifitedDate, ModifiedBy
      )
    values (#{id,jdbcType=INTEGER}, #{generalOrderId,jdbcType=VARCHAR}, #{externalOrderNo,jdbcType=VARCHAR}, 
      #{externalOrderNoSuffixNum,jdbcType=VARCHAR}, #{reportId,jdbcType=VARCHAR}, #{externalReportNo,jdbcType=VARCHAR}, 
      #{activeIndicator,jdbcType=BIT}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifitedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoPO" >
    insert into tb_external_no_rel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="generalOrderId != null" >
        GeneralOrderId,
      </if>
      <if test="externalOrderNo != null" >
        ExternalOrderNo,
      </if>
      <if test="externalOrderNoSuffixNum != null" >
        ExternalOrderNoSuffixNum,
      </if>
      <if test="reportId != null" >
        ReportId,
      </if>
      <if test="externalReportNo != null" >
        ExternalReportNo,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifitedDate != null" >
        ModifitedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="generalOrderId != null" >
        #{generalOrderId,jdbcType=VARCHAR},
      </if>
      <if test="externalOrderNo != null" >
        #{externalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="externalOrderNoSuffixNum != null" >
        #{externalOrderNoSuffixNum,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null" >
        #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="externalReportNo != null" >
        #{externalReportNo,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifitedDate != null" >
        #{modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_external_no_rel
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_external_no_rel
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.generalOrderId != null" >
        GeneralOrderId = #{record.generalOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.externalOrderNo != null" >
        ExternalOrderNo = #{record.externalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.externalOrderNoSuffixNum != null" >
        ExternalOrderNoSuffixNum = #{record.externalOrderNoSuffixNum,jdbcType=VARCHAR},
      </if>
      <if test="record.reportId != null" >
        ReportId = #{record.reportId,jdbcType=VARCHAR},
      </if>
      <if test="record.externalReportNo != null" >
        ExternalReportNo = #{record.externalReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifitedDate != null" >
        ModifitedDate = #{record.modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_external_no_rel
    set Id = #{record.id,jdbcType=INTEGER},
      GeneralOrderId = #{record.generalOrderId,jdbcType=VARCHAR},
      ExternalOrderNo = #{record.externalOrderNo,jdbcType=VARCHAR},
      ExternalOrderNoSuffixNum = #{record.externalOrderNoSuffixNum,jdbcType=VARCHAR},
      ReportId = #{record.reportId,jdbcType=VARCHAR},
      ExternalReportNo = #{record.externalReportNo,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{record.modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoPO" >
    update tb_external_no_rel
    <set >
      <if test="generalOrderId != null" >
        GeneralOrderId = #{generalOrderId,jdbcType=VARCHAR},
      </if>
      <if test="externalOrderNo != null" >
        ExternalOrderNo = #{externalOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="externalOrderNoSuffixNum != null" >
        ExternalOrderNoSuffixNum = #{externalOrderNoSuffixNum,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null" >
        ReportId = #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="externalReportNo != null" >
        ExternalReportNo = #{externalReportNo,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifitedDate != null" >
        ModifitedDate = #{modifitedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ExternalNoRelInfoPO" >
    update tb_external_no_rel
    set GeneralOrderId = #{generalOrderId,jdbcType=VARCHAR},
      ExternalOrderNo = #{externalOrderNo,jdbcType=VARCHAR},
      ExternalOrderNoSuffixNum = #{externalOrderNoSuffixNum,jdbcType=VARCHAR},
      ReportId = #{reportId,jdbcType=VARCHAR},
      ExternalReportNo = #{externalReportNo,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifitedDate = #{modifitedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=INTEGER}
  </update>
</mapper>