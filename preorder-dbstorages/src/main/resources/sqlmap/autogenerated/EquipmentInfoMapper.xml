<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.EquipmentInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="equipment_type_id" property="equipmentTypeId" jdbcType="VARCHAR" />
    <result column="equipment_type_name" property="equipmentTypeName" jdbcType="VARCHAR" />
    <result column="equipment_code" property="equipmentCode" jdbcType="VARCHAR" />
    <result column="equipment_name" property="equipmentName" jdbcType="VARCHAR" />
    <result column="calibration_date" property="calibrationDate" jdbcType="TIMESTAMP" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, equipment_type_id, equipment_type_name, equipment_code, equipment_name, calibration_date, 
    active_indicator, created_date, created_by, modified_date, modified_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_equipment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_equipment
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_equipment
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoExample" >
    delete from tb_equipment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoPO" >
    insert into tb_equipment (id, equipment_type_id, equipment_type_name, 
      equipment_code, equipment_name, calibration_date, 
      active_indicator, created_date, created_by, 
      modified_date, modified_by)
    values (#{id,jdbcType=VARCHAR}, #{equipmentTypeId,jdbcType=VARCHAR}, #{equipmentTypeName,jdbcType=VARCHAR}, 
      #{equipmentCode,jdbcType=VARCHAR}, #{equipmentName,jdbcType=VARCHAR}, #{calibrationDate,jdbcType=TIMESTAMP}, 
      #{activeIndicator,jdbcType=TINYINT}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoPO" >
    insert into tb_equipment
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="equipmentTypeId != null" >
        equipment_type_id,
      </if>
      <if test="equipmentTypeName != null" >
        equipment_type_name,
      </if>
      <if test="equipmentCode != null" >
        equipment_code,
      </if>
      <if test="equipmentName != null" >
        equipment_name,
      </if>
      <if test="calibrationDate != null" >
        calibration_date,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="equipmentTypeId != null" >
        #{equipmentTypeId,jdbcType=VARCHAR},
      </if>
      <if test="equipmentTypeName != null" >
        #{equipmentTypeName,jdbcType=VARCHAR},
      </if>
      <if test="equipmentCode != null" >
        #{equipmentCode,jdbcType=VARCHAR},
      </if>
      <if test="equipmentName != null" >
        #{equipmentName,jdbcType=VARCHAR},
      </if>
      <if test="calibrationDate != null" >
        #{calibrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_equipment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_equipment
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.equipmentTypeId != null" >
        equipment_type_id = #{record.equipmentTypeId,jdbcType=VARCHAR},
      </if>
      <if test="record.equipmentTypeName != null" >
        equipment_type_name = #{record.equipmentTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.equipmentCode != null" >
        equipment_code = #{record.equipmentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.equipmentName != null" >
        equipment_name = #{record.equipmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.calibrationDate != null" >
        calibration_date = #{record.calibrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_equipment
    set id = #{record.id,jdbcType=VARCHAR},
      equipment_type_id = #{record.equipmentTypeId,jdbcType=VARCHAR},
      equipment_type_name = #{record.equipmentTypeName,jdbcType=VARCHAR},
      equipment_code = #{record.equipmentCode,jdbcType=VARCHAR},
      equipment_name = #{record.equipmentName,jdbcType=VARCHAR},
      calibration_date = #{record.calibrationDate,jdbcType=TIMESTAMP},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoPO" >
    update tb_equipment
    <set >
      <if test="equipmentTypeId != null" >
        equipment_type_id = #{equipmentTypeId,jdbcType=VARCHAR},
      </if>
      <if test="equipmentTypeName != null" >
        equipment_type_name = #{equipmentTypeName,jdbcType=VARCHAR},
      </if>
      <if test="equipmentCode != null" >
        equipment_code = #{equipmentCode,jdbcType=VARCHAR},
      </if>
      <if test="equipmentName != null" >
        equipment_name = #{equipmentName,jdbcType=VARCHAR},
      </if>
      <if test="calibrationDate != null" >
        calibration_date = #{calibrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.EquipmentInfoPO" >
    update tb_equipment
    set equipment_type_id = #{equipmentTypeId,jdbcType=VARCHAR},
      equipment_type_name = #{equipmentTypeName,jdbcType=VARCHAR},
      equipment_code = #{equipmentCode,jdbcType=VARCHAR},
      equipment_name = #{equipmentName,jdbcType=VARCHAR},
      calibration_date = #{calibrationDate,jdbcType=TIMESTAMP},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>