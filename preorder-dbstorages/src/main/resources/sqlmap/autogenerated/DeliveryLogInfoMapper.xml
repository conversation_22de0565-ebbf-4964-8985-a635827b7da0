<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.DeliveryLogInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="bu_code" property="buCode" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="VARCHAR" />
    <result column="object_no" property="objectNo" jdbcType="VARCHAR" />
    <result column="action" property="action" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="error_msg" property="errorMsg" jdbcType="VARCHAR" />
    <result column="data" property="data" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, bu_code, object_type, object_no, `action`, `status`, error_msg, `data`, created_date, 
    created_by, modified_by, modified_date, active_indicator
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_delivery_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_delivery_log
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_delivery_log
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoExample" >
    delete from tb_delivery_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoPO" >
    insert into tb_delivery_log (id, bu_code, object_type, 
      object_no, `action`, `status`, 
      error_msg, `data`, created_date, 
      created_by, modified_by, modified_date, 
      active_indicator)
    values (#{id,jdbcType=VARCHAR}, #{buCode,jdbcType=VARCHAR}, #{objectType,jdbcType=VARCHAR}, 
      #{objectNo,jdbcType=VARCHAR}, #{action,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{errorMsg,jdbcType=VARCHAR}, #{data,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{activeIndicator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoPO" >
    insert into tb_delivery_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="buCode != null" >
        bu_code,
      </if>
      <if test="objectType != null" >
        object_type,
      </if>
      <if test="objectNo != null" >
        object_no,
      </if>
      <if test="action != null" >
        `action`,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="errorMsg != null" >
        error_msg,
      </if>
      <if test="data != null" >
        `data`,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="buCode != null" >
        #{buCode,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="action != null" >
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null" >
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="data != null" >
        #{data,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_delivery_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_delivery_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.buCode != null" >
        bu_code = #{record.buCode,jdbcType=VARCHAR},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=VARCHAR},
      </if>
      <if test="record.objectNo != null" >
        object_no = #{record.objectNo,jdbcType=VARCHAR},
      </if>
      <if test="record.action != null" >
        `action` = #{record.action,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMsg != null" >
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.data != null" >
        `data` = #{record.data,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_delivery_log
    set id = #{record.id,jdbcType=VARCHAR},
      bu_code = #{record.buCode,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      object_no = #{record.objectNo,jdbcType=VARCHAR},
      `action` = #{record.action,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      `data` = #{record.data,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoPO" >
    update tb_delivery_log
    <set >
      <if test="buCode != null" >
        bu_code = #{buCode,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        object_no = #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="action != null" >
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null" >
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="data != null" >
        `data` = #{data,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.DeliveryLogInfoPO" >
    update tb_delivery_log
    set bu_code = #{buCode,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      object_no = #{objectNo,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      `data` = #{data,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      active_indicator = #{activeIndicator,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>