<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.TrfTodoListInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoPO" >
    <id column="ID" property="ID" jdbcType="INTEGER" />
    <result column="TrfNo" property="trfNo" jdbcType="VARCHAR" />
    <result column="TrfSubmissionDate" property="trfSubmissionDate" jdbcType="TIMESTAMP" />
    <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
    <result column="BuyerNameEN" property="buyerNameEN" jdbcType="VARCHAR" />
    <result column="ApplicantNameEN" property="applicantNameEN" jdbcType="VARCHAR" />
    <result column="PayerNameEN" property="payerNameEN" jdbcType="VARCHAR" />
    <result column="LabContact" property="labContact" jdbcType="VARCHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="Source" property="source" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, TrfNo, TrfSubmissionDate, LabCode, BuyerNameEN, ApplicantNameEN, PayerNameEN, 
    LabContact, `Status`, `Source`
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trf_todo_list
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from tb_trf_todo_list
    where ID = #{ID,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from tb_trf_todo_list
    where ID = #{ID,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoExample" >
    delete from tb_trf_todo_list
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoPO" >
    insert into tb_trf_todo_list (ID, TrfNo, TrfSubmissionDate, 
      LabCode, BuyerNameEN, ApplicantNameEN, 
      PayerNameEN, LabContact, `Status`, 
      `Source`)
    values (#{ID,jdbcType=INTEGER}, #{trfNo,jdbcType=VARCHAR}, #{trfSubmissionDate,jdbcType=TIMESTAMP}, 
      #{labCode,jdbcType=VARCHAR}, #{buyerNameEN,jdbcType=VARCHAR}, #{applicantNameEN,jdbcType=VARCHAR}, 
      #{payerNameEN,jdbcType=VARCHAR}, #{labContact,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoPO" >
    insert into tb_trf_todo_list
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="trfNo != null" >
        TrfNo,
      </if>
      <if test="trfSubmissionDate != null" >
        TrfSubmissionDate,
      </if>
      <if test="labCode != null" >
        LabCode,
      </if>
      <if test="buyerNameEN != null" >
        BuyerNameEN,
      </if>
      <if test="applicantNameEN != null" >
        ApplicantNameEN,
      </if>
      <if test="payerNameEN != null" >
        PayerNameEN,
      </if>
      <if test="labContact != null" >
        LabContact,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="source != null" >
        `Source`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=INTEGER},
      </if>
      <if test="trfNo != null" >
        #{trfNo,jdbcType=VARCHAR},
      </if>
      <if test="trfSubmissionDate != null" >
        #{trfSubmissionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerNameEN != null" >
        #{buyerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="applicantNameEN != null" >
        #{applicantNameEN,jdbcType=VARCHAR},
      </if>
      <if test="payerNameEN != null" >
        #{payerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="labContact != null" >
        #{labContact,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="source != null" >
        #{source,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_trf_todo_list
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trf_todo_list
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=INTEGER},
      </if>
      <if test="record.trfNo != null" >
        TrfNo = #{record.trfNo,jdbcType=VARCHAR},
      </if>
      <if test="record.trfSubmissionDate != null" >
        TrfSubmissionDate = #{record.trfSubmissionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.labCode != null" >
        LabCode = #{record.labCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerNameEN != null" >
        BuyerNameEN = #{record.buyerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="record.applicantNameEN != null" >
        ApplicantNameEN = #{record.applicantNameEN,jdbcType=VARCHAR},
      </if>
      <if test="record.payerNameEN != null" >
        PayerNameEN = #{record.payerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="record.labContact != null" >
        LabContact = #{record.labContact,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.source != null" >
        `Source` = #{record.source,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trf_todo_list
    set ID = #{record.ID,jdbcType=INTEGER},
      TrfNo = #{record.trfNo,jdbcType=VARCHAR},
      TrfSubmissionDate = #{record.trfSubmissionDate,jdbcType=TIMESTAMP},
      LabCode = #{record.labCode,jdbcType=VARCHAR},
      BuyerNameEN = #{record.buyerNameEN,jdbcType=VARCHAR},
      ApplicantNameEN = #{record.applicantNameEN,jdbcType=VARCHAR},
      PayerNameEN = #{record.payerNameEN,jdbcType=VARCHAR},
      LabContact = #{record.labContact,jdbcType=VARCHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      `Source` = #{record.source,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoPO" >
    update tb_trf_todo_list
    <set >
      <if test="trfNo != null" >
        TrfNo = #{trfNo,jdbcType=VARCHAR},
      </if>
      <if test="trfSubmissionDate != null" >
        TrfSubmissionDate = #{trfSubmissionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="labCode != null" >
        LabCode = #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerNameEN != null" >
        BuyerNameEN = #{buyerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="applicantNameEN != null" >
        ApplicantNameEN = #{applicantNameEN,jdbcType=VARCHAR},
      </if>
      <if test="payerNameEN != null" >
        PayerNameEN = #{payerNameEN,jdbcType=VARCHAR},
      </if>
      <if test="labContact != null" >
        LabContact = #{labContact,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="source != null" >
        `Source` = #{source,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.TrfTodoListInfoPO" >
    update tb_trf_todo_list
    set TrfNo = #{trfNo,jdbcType=VARCHAR},
      TrfSubmissionDate = #{trfSubmissionDate,jdbcType=TIMESTAMP},
      LabCode = #{labCode,jdbcType=VARCHAR},
      BuyerNameEN = #{buyerNameEN,jdbcType=VARCHAR},
      ApplicantNameEN = #{applicantNameEN,jdbcType=VARCHAR},
      PayerNameEN = #{payerNameEN,jdbcType=VARCHAR},
      LabContact = #{labContact,jdbcType=VARCHAR},
      `Status` = #{status,jdbcType=INTEGER},
      `Source` = #{source,jdbcType=INTEGER}
    where ID = #{ID,jdbcType=INTEGER}
  </update>
</mapper>