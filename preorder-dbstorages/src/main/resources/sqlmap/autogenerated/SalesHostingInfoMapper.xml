<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.SalesHostingInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.SalesHostingInfoPO" >
    <result column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="RegionAccount" property="regionAccount" jdbcType="VARCHAR" />
    <result column="SalesPerson" property="salesPerson" jdbcType="VARCHAR" />
    <result column="HostingID" property="hostingID" jdbcType="VARCHAR" />
    <result column="HostingCustomerNo" property="hostingCustomerNo" jdbcType="VARCHAR" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="BuCode" property="buCode" jdbcType="VARCHAR" />
    <result column="LocationCode" property="locationCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, RegionAccount, SalesPerson, HostingID, HostingCustomerNo, CreatedBy, CreatedDate, 
    ModifiedBy, ModifiedDate, BuCode, LocationCode
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SalesHostingInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_sales_hosting
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SalesHostingInfoExample" >
    delete from tb_sales_hosting
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SalesHostingInfoPO" >
    insert into tb_sales_hosting (ID, RegionAccount, SalesPerson, 
      HostingID, HostingCustomerNo, CreatedBy, 
      CreatedDate, ModifiedBy, ModifiedDate, 
      BuCode, LocationCode)
    values (#{ID,jdbcType=VARCHAR}, #{regionAccount,jdbcType=VARCHAR}, #{salesPerson,jdbcType=VARCHAR}, 
      #{hostingID,jdbcType=VARCHAR}, #{hostingCustomerNo,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{buCode,jdbcType=VARCHAR}, #{locationCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SalesHostingInfoPO" >
    insert into tb_sales_hosting
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="regionAccount != null" >
        RegionAccount,
      </if>
      <if test="salesPerson != null" >
        SalesPerson,
      </if>
      <if test="hostingID != null" >
        HostingID,
      </if>
      <if test="hostingCustomerNo != null" >
        HostingCustomerNo,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="buCode != null" >
        BuCode,
      </if>
      <if test="locationCode != null" >
        LocationCode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="regionAccount != null" >
        #{regionAccount,jdbcType=VARCHAR},
      </if>
      <if test="salesPerson != null" >
        #{salesPerson,jdbcType=VARCHAR},
      </if>
      <if test="hostingID != null" >
        #{hostingID,jdbcType=VARCHAR},
      </if>
      <if test="hostingCustomerNo != null" >
        #{hostingCustomerNo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="buCode != null" >
        #{buCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        #{locationCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SalesHostingInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_sales_hosting
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_sales_hosting
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.regionAccount != null" >
        RegionAccount = #{record.regionAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.salesPerson != null" >
        SalesPerson = #{record.salesPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.hostingID != null" >
        HostingID = #{record.hostingID,jdbcType=VARCHAR},
      </if>
      <if test="record.hostingCustomerNo != null" >
        HostingCustomerNo = #{record.hostingCustomerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.buCode != null" >
        BuCode = #{record.buCode,jdbcType=VARCHAR},
      </if>
      <if test="record.locationCode != null" >
        LocationCode = #{record.locationCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_sales_hosting
    set ID = #{record.ID,jdbcType=VARCHAR},
      RegionAccount = #{record.regionAccount,jdbcType=VARCHAR},
      SalesPerson = #{record.salesPerson,jdbcType=VARCHAR},
      HostingID = #{record.hostingID,jdbcType=VARCHAR},
      HostingCustomerNo = #{record.hostingCustomerNo,jdbcType=VARCHAR},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      BuCode = #{record.buCode,jdbcType=VARCHAR},
      LocationCode = #{record.locationCode,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>