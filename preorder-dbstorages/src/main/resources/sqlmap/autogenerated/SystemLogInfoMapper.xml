<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.SystemLogInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="product_line_code" property="productLineCode" jdbcType="VARCHAR" />
    <result column="location_code" property="locationCode" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="request_id" property="requestId" jdbcType="VARCHAR" />
    <result column="target" property="target" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="VARCHAR" />
    <result column="object_no" property="objectNo" jdbcType="VARCHAR" />
    <result column="operation_type" property="operationType" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="request" property="request" jdbcType="CHAR" />
    <result column="response" property="response" jdbcType="CHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="BIT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoPO" extends="BaseResultMap" >
    <result column="remark" property="remark" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, product_line_code, location_code, `type`, request_id, target, object_type, object_no, 
    operation_type, url, request, response, active_indicator, created_by, created_date, 
    modified_by, modified_date
  </sql>
  <sql id="Blob_Column_List" >
    remark
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_system_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_system_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_system_log
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_system_log
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoExample" >
    delete from tb_system_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoPO" >
    insert into tb_system_log (id, product_line_code, location_code, 
      `type`, request_id, target, 
      object_type, object_no, operation_type, 
      url, request, response, active_indicator, 
      created_by, created_date, modified_by, 
      modified_date, remark)
    values (#{id,jdbcType=VARCHAR}, #{productLineCode,jdbcType=VARCHAR}, #{locationCode,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{requestId,jdbcType=VARCHAR}, #{target,jdbcType=VARCHAR}, 
      #{objectType,jdbcType=VARCHAR}, #{objectNo,jdbcType=VARCHAR}, #{operationType,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{request,jdbcType=CHAR}, #{response,jdbcType=CHAR}, #{activeIndicator,jdbcType=BIT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoPO" >
    insert into tb_system_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="productLineCode != null" >
        product_line_code,
      </if>
      <if test="locationCode != null" >
        location_code,
      </if>
      <if test="type != null" >
        `type`,
      </if>
      <if test="requestId != null" >
        request_id,
      </if>
      <if test="target != null" >
        target,
      </if>
      <if test="objectType != null" >
        object_type,
      </if>
      <if test="objectNo != null" >
        object_no,
      </if>
      <if test="operationType != null" >
        operation_type,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="request != null" >
        request,
      </if>
      <if test="response != null" >
        response,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="productLineCode != null" >
        #{productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null" >
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="target != null" >
        #{target,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null" >
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="request != null" >
        #{request,jdbcType=CHAR},
      </if>
      <if test="response != null" >
        #{response,jdbcType=CHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_system_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_system_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.productLineCode != null" >
        product_line_code = #{record.productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="record.locationCode != null" >
        location_code = #{record.locationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.requestId != null" >
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.target != null" >
        target = #{record.target,jdbcType=VARCHAR},
      </if>
      <if test="record.objectType != null" >
        object_type = #{record.objectType,jdbcType=VARCHAR},
      </if>
      <if test="record.objectNo != null" >
        object_no = #{record.objectNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operationType != null" >
        operation_type = #{record.operationType,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null" >
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.request != null" >
        request = #{record.request,jdbcType=CHAR},
      </if>
      <if test="record.response != null" >
        response = #{record.response,jdbcType=CHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_system_log
    set id = #{record.id,jdbcType=VARCHAR},
      product_line_code = #{record.productLineCode,jdbcType=VARCHAR},
      location_code = #{record.locationCode,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      target = #{record.target,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      object_no = #{record.objectNo,jdbcType=VARCHAR},
      operation_type = #{record.operationType,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      request = #{record.request,jdbcType=CHAR},
      response = #{record.response,jdbcType=CHAR},
      active_indicator = #{record.activeIndicator,jdbcType=BIT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_system_log
    set id = #{record.id,jdbcType=VARCHAR},
      product_line_code = #{record.productLineCode,jdbcType=VARCHAR},
      location_code = #{record.locationCode,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      target = #{record.target,jdbcType=VARCHAR},
      object_type = #{record.objectType,jdbcType=VARCHAR},
      object_no = #{record.objectNo,jdbcType=VARCHAR},
      operation_type = #{record.operationType,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      request = #{record.request,jdbcType=CHAR},
      response = #{record.response,jdbcType=CHAR},
      active_indicator = #{record.activeIndicator,jdbcType=BIT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoPO" >
    update tb_system_log
    <set >
      <if test="productLineCode != null" >
        product_line_code = #{productLineCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        location_code = #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null" >
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="target != null" >
        target = #{target,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="objectNo != null" >
        object_no = #{objectNo,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null" >
        operation_type = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="request != null" >
        request = #{request,jdbcType=CHAR},
      </if>
      <if test="response != null" >
        response = #{response,jdbcType=CHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoPO" >
    update tb_system_log
    set product_line_code = #{productLineCode,jdbcType=VARCHAR},
      location_code = #{locationCode,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      request_id = #{requestId,jdbcType=VARCHAR},
      target = #{target,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      object_no = #{objectNo,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      request = #{request,jdbcType=CHAR},
      response = #{response,jdbcType=CHAR},
      active_indicator = #{activeIndicator,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.SystemLogInfoPO" >
    update tb_system_log
    set product_line_code = #{productLineCode,jdbcType=VARCHAR},
      location_code = #{locationCode,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      request_id = #{requestId,jdbcType=VARCHAR},
      target = #{target,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      object_no = #{objectNo,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      request = #{request,jdbcType=CHAR},
      response = #{response,jdbcType=CHAR},
      active_indicator = #{activeIndicator,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>