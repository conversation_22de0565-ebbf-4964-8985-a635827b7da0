<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.BuObjectSettingsOrderModelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="bu_object_setting_id" property="buObjectSettingId" jdbcType="VARCHAR" />
    <result column="order_model" property="orderModel" jdbcType="VARCHAR" />
    <result column="is_display" property="isDisplay" jdbcType="SMALLINT" />
    <result column="is_required" property="isRequired" jdbcType="SMALLINT" />
    <result column="default_value" property="defaultValue" jdbcType="VARCHAR" />
    <result column="is_expand" property="isExpand" jdbcType="SMALLINT" />
    <result column="is_fixed" property="isFixed" jdbcType="SMALLINT" />
    <result column="same_as_order" property="sameAsOrder" jdbcType="CHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoPO" extends="BaseResultMap" >
    <result column="function_expression" property="function" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, bu_object_setting_id, order_model, is_display, is_required, default_value, is_expand, 
    is_fixed, same_as_order
  </sql>
  <sql id="Blob_Column_List" >
    function_expression
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_bu_object_settings_order_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_bu_object_settings_order_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_bu_object_settings_order_model
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_bu_object_settings_order_model
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoExample" >
    delete from tb_bu_object_settings_order_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoPO" >
    insert into tb_bu_object_settings_order_model (id, bu_object_setting_id, order_model, 
      is_display, is_required, default_value, 
      is_expand, is_fixed, same_as_order, 
      function_expression)
    values (#{id,jdbcType=VARCHAR}, #{buObjectSettingId,jdbcType=VARCHAR}, #{orderModel,jdbcType=VARCHAR}, 
      #{isDisplay,jdbcType=SMALLINT}, #{isRequired,jdbcType=SMALLINT}, #{defaultValue,jdbcType=VARCHAR}, 
      #{isExpand,jdbcType=SMALLINT}, #{isFixed,jdbcType=SMALLINT}, #{sameAsOrder,jdbcType=CHAR}, 
      #{function,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoPO" >
    insert into tb_bu_object_settings_order_model
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="buObjectSettingId != null" >
        bu_object_setting_id,
      </if>
      <if test="orderModel != null" >
        order_model,
      </if>
      <if test="isDisplay != null" >
        is_display,
      </if>
      <if test="isRequired != null" >
        is_required,
      </if>
      <if test="defaultValue != null" >
        default_value,
      </if>
      <if test="isExpand != null" >
        is_expand,
      </if>
      <if test="isFixed != null" >
        is_fixed,
      </if>
      <if test="sameAsOrder != null" >
        same_as_order,
      </if>
      <if test="function != null" >
        function_expression,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="buObjectSettingId != null" >
        #{buObjectSettingId,jdbcType=VARCHAR},
      </if>
      <if test="orderModel != null" >
        #{orderModel,jdbcType=VARCHAR},
      </if>
      <if test="isDisplay != null" >
        #{isDisplay,jdbcType=SMALLINT},
      </if>
      <if test="isRequired != null" >
        #{isRequired,jdbcType=SMALLINT},
      </if>
      <if test="defaultValue != null" >
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="isExpand != null" >
        #{isExpand,jdbcType=SMALLINT},
      </if>
      <if test="isFixed != null" >
        #{isFixed,jdbcType=SMALLINT},
      </if>
      <if test="sameAsOrder != null" >
        #{sameAsOrder,jdbcType=CHAR},
      </if>
      <if test="function != null" >
        #{function,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_bu_object_settings_order_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_bu_object_settings_order_model
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.buObjectSettingId != null" >
        bu_object_setting_id = #{record.buObjectSettingId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderModel != null" >
        order_model = #{record.orderModel,jdbcType=VARCHAR},
      </if>
      <if test="record.isDisplay != null" >
        is_display = #{record.isDisplay,jdbcType=SMALLINT},
      </if>
      <if test="record.isRequired != null" >
        is_required = #{record.isRequired,jdbcType=SMALLINT},
      </if>
      <if test="record.defaultValue != null" >
        default_value = #{record.defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.isExpand != null" >
        is_expand = #{record.isExpand,jdbcType=SMALLINT},
      </if>
      <if test="record.isFixed != null" >
        is_fixed = #{record.isFixed,jdbcType=SMALLINT},
      </if>
      <if test="record.sameAsOrder != null" >
        same_as_order = #{record.sameAsOrder,jdbcType=CHAR},
      </if>
      <if test="record.function != null" >
        function_expression = #{record.function,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_bu_object_settings_order_model
    set id = #{record.id,jdbcType=VARCHAR},
      bu_object_setting_id = #{record.buObjectSettingId,jdbcType=VARCHAR},
      order_model = #{record.orderModel,jdbcType=VARCHAR},
      is_display = #{record.isDisplay,jdbcType=SMALLINT},
      is_required = #{record.isRequired,jdbcType=SMALLINT},
      default_value = #{record.defaultValue,jdbcType=VARCHAR},
      is_expand = #{record.isExpand,jdbcType=SMALLINT},
      is_fixed = #{record.isFixed,jdbcType=SMALLINT},
      same_as_order = #{record.sameAsOrder,jdbcType=CHAR},
      function_expression = #{record.function,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_bu_object_settings_order_model
    set id = #{record.id,jdbcType=VARCHAR},
      bu_object_setting_id = #{record.buObjectSettingId,jdbcType=VARCHAR},
      order_model = #{record.orderModel,jdbcType=VARCHAR},
      is_display = #{record.isDisplay,jdbcType=SMALLINT},
      is_required = #{record.isRequired,jdbcType=SMALLINT},
      default_value = #{record.defaultValue,jdbcType=VARCHAR},
      is_expand = #{record.isExpand,jdbcType=SMALLINT},
      is_fixed = #{record.isFixed,jdbcType=SMALLINT},
      same_as_order = #{record.sameAsOrder,jdbcType=CHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoPO" >
    update tb_bu_object_settings_order_model
    <set >
      <if test="buObjectSettingId != null" >
        bu_object_setting_id = #{buObjectSettingId,jdbcType=VARCHAR},
      </if>
      <if test="orderModel != null" >
        order_model = #{orderModel,jdbcType=VARCHAR},
      </if>
      <if test="isDisplay != null" >
        is_display = #{isDisplay,jdbcType=SMALLINT},
      </if>
      <if test="isRequired != null" >
        is_required = #{isRequired,jdbcType=SMALLINT},
      </if>
      <if test="defaultValue != null" >
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="isExpand != null" >
        is_expand = #{isExpand,jdbcType=SMALLINT},
      </if>
      <if test="isFixed != null" >
        is_fixed = #{isFixed,jdbcType=SMALLINT},
      </if>
      <if test="sameAsOrder != null" >
        same_as_order = #{sameAsOrder,jdbcType=CHAR},
      </if>
      <if test="function != null" >
        function_expression = #{function,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoPO" >
    update tb_bu_object_settings_order_model
    set bu_object_setting_id = #{buObjectSettingId,jdbcType=VARCHAR},
      order_model = #{orderModel,jdbcType=VARCHAR},
      is_display = #{isDisplay,jdbcType=SMALLINT},
      is_required = #{isRequired,jdbcType=SMALLINT},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      is_expand = #{isExpand,jdbcType=SMALLINT},
      is_fixed = #{isFixed,jdbcType=SMALLINT},
      same_as_order = #{sameAsOrder,jdbcType=CHAR},
      function_expression = #{function,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BuObjectSettingsOrderModelInfoPO" >
    update tb_bu_object_settings_order_model
    set bu_object_setting_id = #{buObjectSettingId,jdbcType=VARCHAR},
      order_model = #{orderModel,jdbcType=VARCHAR},
      is_display = #{isDisplay,jdbcType=SMALLINT},
      is_required = #{isRequired,jdbcType=SMALLINT},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      is_expand = #{isExpand,jdbcType=SMALLINT},
      is_fixed = #{isFixed,jdbcType=SMALLINT},
      same_as_order = #{sameAsOrder,jdbcType=CHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>