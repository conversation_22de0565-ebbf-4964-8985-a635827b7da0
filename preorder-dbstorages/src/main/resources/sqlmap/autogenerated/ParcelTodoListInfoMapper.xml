<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.ParcelTodoListInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoPO" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
    <result column="resp_cs_name" property="respCsName" jdbcType="VARCHAR" />
    <result column="report_deliver_date" property="reportDeliverDate" jdbcType="TIMESTAMP" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="contact" property="contact" jdbcType="VARCHAR" />
    <result column="express_content" property="expressContent" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="parcel_no" property="parcelNo" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="express_no" property="expressNo" jdbcType="VARCHAR" />
    <result column="send_date" property="sendDate" jdbcType="TIMESTAMP" />
    <result column="lab_code" property="labCode" jdbcType="VARCHAR" />
    <result column="bu_code" property="buCode" jdbcType="VARCHAR" />
    <result column="location_code" property="locationCode" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="tax_invoice_no" property="taxInvoiceNo" jdbcType="VARCHAR" />
    <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="VARCHAR" />
    <result column="contract_tel" property="contractTel" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, order_no, report_no, resp_cs_name, report_deliver_date, company_name, address, contact,
    express_content, remark, parcel_no, `status`, express_no, send_date, lab_code, bu_code, 
    location_code, created_date, created_by, modified_date, modified_by, tax_invoice_no, 
    invoice_no, order_id, contract_tel
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_parcel_todo_list
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from tb_parcel_todo_list
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_parcel_todo_list
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoExample" >
    delete from tb_parcel_todo_list
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoPO" >
    insert into tb_parcel_todo_list (ID, order_no, report_no, resp_cs_name,
      report_deliver_date, company_name, address,
      contact, express_content, remark,
      parcel_no, `status`, express_no,
      send_date, lab_code, bu_code,
      location_code, created_date, created_by,
      modified_date, modified_by, tax_invoice_no,
      invoice_no, order_id, contract_tel
      )
    values (#{id,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{respCsName,jdbcType=VARCHAR},
      #{reportDeliverDate,jdbcType=TIMESTAMP}, #{companyName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
      #{contact,jdbcType=VARCHAR}, #{expressContent,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
      #{parcelNo,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{expressNo,jdbcType=VARCHAR},
      #{sendDate,jdbcType=TIMESTAMP}, #{labCode,jdbcType=VARCHAR}, #{buCode,jdbcType=VARCHAR},
      #{locationCode,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{taxInvoiceNo,jdbcType=VARCHAR},
      #{invoiceNo,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{contractTel,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoPO" >
    insert into tb_parcel_todo_list
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="reportNo != null" >
        report_no,
      </if>
      <if test="respCsName != null" >
        resp_cs_name,
      </if>
      <if test="reportDeliverDate != null" >
        report_deliver_date,
      </if>
      <if test="companyName != null" >
        company_name,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="contact != null" >
        contact,
      </if>
      <if test="expressContent != null" >
        express_content,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="parcelNo != null" >
        parcel_no,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="expressNo != null" >
        express_no,
      </if>
      <if test="sendDate != null" >
        send_date,
      </if>
      <if test="labCode != null" >
        lab_code,
      </if>
      <if test="buCode != null" >
        bu_code,
      </if>
      <if test="locationCode != null" >
        location_code,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="taxInvoiceNo != null" >
        tax_invoice_no,
      </if>
      <if test="invoiceNo != null" >
        invoice_no,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="contractTel != null" >
        contract_tel,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="respCsName != null" >
        #{respCsName,jdbcType=VARCHAR},
      </if>
      <if test="reportDeliverDate != null" >
        #{reportDeliverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="contact != null" >
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="expressContent != null" >
        #{expressContent,jdbcType=TINYINT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="parcelNo != null" >
        #{parcelNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="expressNo != null" >
        #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="sendDate != null" >
        #{sendDate,jdbcType=TIMESTAMP},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="buCode != null" >
        #{buCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceNo != null" >
        #{taxInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="contractTel != null" >
        #{contractTel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_parcel_todo_list
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

    <update id="updateByExampleSelective" parameterType="map" >
    update tb_parcel_todo_list
    <set >
      <if test="record.id != null" >
        ID = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.respCsName != null" >
        resp_cs_name = #{record.respCsName,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDeliverDate != null" >
        report_deliver_date = #{record.reportDeliverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyName != null" >
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null" >
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.contact != null" >
        contact = #{record.contact,jdbcType=VARCHAR},
      </if>
      <if test="record.expressContent != null" >
        express_content = #{record.expressContent,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.parcelNo != null" >
        parcel_no = #{record.parcelNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.expressNo != null" >
        express_no = #{record.expressNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendDate != null" >
        send_date = #{record.sendDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.labCode != null" >
        lab_code = #{record.labCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buCode != null" >
        bu_code = #{record.buCode,jdbcType=VARCHAR},
      </if>
      <if test="record.locationCode != null" >
        location_code = #{record.locationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInvoiceNo != null" >
        tax_invoice_no = #{record.taxInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null" >
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null" >
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.contractTel != null" >
        contract_tel = #{record.contractTel,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_parcel_todo_list
    set ID = #{record.id,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      resp_cs_name = #{record.respCsName,jdbcType=VARCHAR},
      report_deliver_date = #{record.reportDeliverDate,jdbcType=TIMESTAMP},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      contact = #{record.contact,jdbcType=VARCHAR},
      express_content = #{record.expressContent,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      parcel_no = #{record.parcelNo,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      express_no = #{record.expressNo,jdbcType=VARCHAR},
      send_date = #{record.sendDate,jdbcType=TIMESTAMP},
      lab_code = #{record.labCode,jdbcType=VARCHAR},
      bu_code = #{record.buCode,jdbcType=VARCHAR},
      location_code = #{record.locationCode,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      tax_invoice_no = #{record.taxInvoiceNo,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      contract_tel = #{record.contractTel,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoPO" >
    update tb_parcel_todo_list
    <set >
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="respCsName != null" >
        resp_cs_name = #{respCsName,jdbcType=VARCHAR},
      </if>
      <if test="reportDeliverDate != null" >
        report_deliver_date = #{reportDeliverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyName != null" >
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="contact != null" >
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="expressContent != null" >
        express_content = #{expressContent,jdbcType=TINYINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="parcelNo != null" >
        parcel_no = #{parcelNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="expressNo != null" >
        express_no = #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="sendDate != null" >
        send_date = #{sendDate,jdbcType=TIMESTAMP},
      </if>
      <if test="labCode != null" >
        lab_code = #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="buCode != null" >
        bu_code = #{buCode,jdbcType=VARCHAR},
      </if>
      <if test="locationCode != null" >
        location_code = #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceNo != null" >
        tax_invoice_no = #{taxInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="contractTel != null" >
        contract_tel = #{contractTel,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ParcelTodoListInfoPO" >
    update tb_parcel_todo_list
    set order_no = #{orderNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      resp_cs_name = #{respCsName,jdbcType=VARCHAR},
      report_deliver_date = #{reportDeliverDate,jdbcType=TIMESTAMP},
      company_name = #{companyName,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      contact = #{contact,jdbcType=VARCHAR},
      express_content = #{expressContent,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      parcel_no = #{parcelNo,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      express_no = #{expressNo,jdbcType=VARCHAR},
      send_date = #{sendDate,jdbcType=TIMESTAMP},
      lab_code = #{labCode,jdbcType=VARCHAR},
      bu_code = #{buCode,jdbcType=VARCHAR},
      location_code = #{locationCode,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      tax_invoice_no = #{taxInvoiceNo,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      contract_tel = #{contractTel,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>