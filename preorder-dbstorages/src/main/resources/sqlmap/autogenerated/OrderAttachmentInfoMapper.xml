<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.OrderAttachmentInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="FileID" property="fileID" jdbcType="VARCHAR" />
    <result column="GeneralOrderID" property="generalOrderID" jdbcType="VARCHAR" />
    <result column="ObjectID" property="objectID" jdbcType="VARCHAR" />
    <result column="BusinessType" property="businessType" jdbcType="VARCHAR" />
    <result column="CloudID" property="cloudID" jdbcType="VARCHAR" />
    <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
    <result column="AttachmentName" property="attachmentName" jdbcType="VARCHAR" />
    <result column="SampleNo" property="sampleNo" jdbcType="VARCHAR" />
    <result column="PhotoType" property="photoType" jdbcType="INTEGER" />
    <result column="ToCp" property="toCp" jdbcType="INTEGER" />
    <result column="SuffixNum" property="suffixNum" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="Source" property="source" jdbcType="VARCHAR" />
    <result column="TestLineInstanceId" property="testLineInstanceId" jdbcType="VARCHAR" />
    <result column="UsedReport" property="usedReport" jdbcType="INTEGER" />
    <result column="LastDownloadBy" property="lastDownloadBy" jdbcType="VARCHAR" />
    <result column="LastDownloadDate" property="lastDownloadDate" jdbcType="TIMESTAMP" />
    <result column="Tag" property="tag" jdbcType="VARCHAR" />

  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, FileID, GeneralOrderID, ObjectID, BusinessType, CloudID, ReportNo, SampleNo,PhotoType,SuffixNum,
    ActiveIndicator,AttachmentName,
    CreatedBy, CreatedDate, ModifiedBy, ModifiedDate,ToCp,`Source`,TestLineInstanceId,UsedReport,LastDownloadBy,Tag,LastDownloadDate
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_order_attachment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_order_attachment
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_order_attachment
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoExample" >
    delete from tb_order_attachment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoPO" >
    insert into tb_order_attachment (ID, FileID, GeneralOrderID, 
      ObjectID, BusinessType, CloudID, 
      ReportNo, SampleNo,PhotoType,SuffixNum,
      ActiveIndicator, CreatedBy,
      CreatedDate, ModifiedBy, ModifiedDate,ToCp,`Source`,TestLineInstanceId,UsedReport,LastDownloadBy,Tag,LastDownloadDate
      )
    values (#{ID,jdbcType=VARCHAR}, #{fileID,jdbcType=VARCHAR}, #{generalOrderID,jdbcType=VARCHAR}, 
      #{objectID,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{cloudID,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{sampleNo,jdbcType=VARCHAR},#{photoType,jdbcType=INTEGER},#{suffixNum,jdbcType=VARCHAR},
      #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP},
      #{toCp,jdbcType=INTEGER},#{source,jdbcType=VARCHAR},#{testLineInstanceId,jdbcType=VARCHAR},#{usedReport,jdbcType=INTEGER},
      #{lastDownloadBy,jdbcType=VARCHAR},#{tag,jdbcType=VARCHAR},#{lastDownloadDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoPO" >
    insert into tb_order_attachment
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="fileID != null" >
        FileID,
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID,
      </if>
      <if test="objectID != null" >
        ObjectID,
      </if>
      <if test="businessType != null" >
        BusinessType,
      </if>
      <if test="cloudID != null" >
        CloudID,
      </if>
      <if test="reportNo != null" >
        ReportNo,
      </if>
      <if test="sampleNo != null" >
        SampleNo,
      </if>
      <if test="photoType != null" >
        PhotoType,
      </if>
      <if test="suffixNum != null" >
        SuffixNum,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="thumbnailCloudId != null" >
        ThumbnailCloudId,
      </if>
      <if test="toCp != null" >
        ToCp,
      </if>
      <if test="source != null" >
        `Source`,
      </if>
      <if test="testLineInstanceId != null" >
        `TestLineInstanceId`,
      </if>
      <if test="usedReport != null" >
        UsedReport,
      </if>
      <if test="lastDownloadBy != null" >
        LastDownloadBy,
      </if>
      <if test="tag != null" >
        Tag,
      </if>
      <if test="lastDownloadDate != null" >
        LastDownloadDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="fileID != null" >
        #{fileID,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderID != null" >
        #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="objectID != null" >
        #{objectID,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="cloudID != null" >
        #{cloudID,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleNo != null" >
        #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="photoType != null" >
        #{photoType,jdbcType=INTEGER},
      </if>
      <if test="suffixNum != null" >
        #{suffixNum,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="thumbnailCloudId != null" >
        #{thumbnailCloudId,jdbcType=TIMESTAMP},
      </if>
      <if test="toCp != null" >
        #{toCp,jdbcType=INTEGER},
      </if>
      <if test="source != null" >
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="testLineInstanceId != null" >
        #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="usedReport != null" >
        #{usedReport,jdbcType=INTEGER},
      </if>
      <if test="lastDownloadBy != null" >
        #{lastDownloadBy,jdbcType=VARCHAR},
      </if>
      <if test="tag != null" >
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="lastDownloadDate != null" >
        #{lastDownloadDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_order_attachment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_order_attachment
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.fileID != null" >
        FileID = #{record.fileID,jdbcType=VARCHAR},
      </if>
      <if test="record.generalOrderID != null" >
        GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.objectID != null" >
        ObjectID = #{record.objectID,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null" >
        BusinessType = #{record.businessType,jdbcType=VARCHAR},
      </if>
      <if test="record.cloudID != null" >
        CloudID = #{record.cloudID,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleNo != null" >
        SampleNo = #{record.sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.photoType != null" >
        PhotoType = #{record.photoType,jdbcType=INTEGER},
      </if>
      <if test="record.suffixNum != null" >
        SuffixNum = #{record.suffixNum,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.toCp != null" >
        ToCp = #{record.toCp,jdbcType=INTEGER},
      </if>
      <if test="record.source != null" >
        `Source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineInstanceId != null" >
        `TestLineInstanceId` = #{record.testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.usedReport != null" >
        UsedReport = #{record.usedReport,jdbcType=INTEGER},
      </if>
      <if test="record.lastDownloadBy != null" >
        `LastDownloadBy` = #{record.lastDownloadBy,jdbcType=VARCHAR},
      </if>
      <if test="record.tag != null" >
        `Tag` = #{record.tag,jdbcType=VARCHAR},
      </if>
      <if test="record.lastDownloadDate != null" >
        LastDownloadDate = #{record.lastDownloadDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_order_attachment
    set ID = #{record.ID,jdbcType=VARCHAR},
      FileID = #{record.fileID,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      ObjectID = #{record.objectID,jdbcType=VARCHAR},
      BusinessType = #{record.businessType,jdbcType=VARCHAR},
      CloudID = #{record.cloudID,jdbcType=VARCHAR},
      ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      SampleNo = #{record.sampleNo,jdbcType=VARCHAR},
      PhotoType = #{record.photoType,jdbcType=INTEGER},
      SuffixNum = #{record.reportNo,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ToCp = #{record.toCp,jdbcType=INTEGER},
      `Source` = #{record.source,jdbcType=VARCHAR},
      `TestLineInstanceId` = #{record.testLineInstanceId,jdbcType=VARCHAR},
      UsedReport = #{record.usedReport,jdbcType=INTEGER},
      LastDownloadBy = #{record.lastDownloadBy,jdbcType=VARCHAR},
      Tag = #{record.tag,jdbcType=VARCHAR},
      LastDownloadDate = #{record.lastDownloadDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoPO" >
    update tb_order_attachment
    <set >
      <if test="fileID != null" >
        FileID = #{fileID,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="objectID != null" >
        ObjectID = #{objectID,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null" >
        BusinessType = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="cloudID != null" >
        CloudID = #{cloudID,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        ReportNo = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleNo != null" >
        SampleNo = #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="photoType != null" >
        PhotoType = #{photoType,jdbcType=INTEGER},
      </if>
      <if test="suffixNum != null" >
        SuffixNum = #{suffixNum,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="toCp != null" >
        ToCp = #{toCp,jdbcType=INTEGER},
      </if>
      <if test="source != null" >
        `Source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="testLineInstanceId != null" >
        `TestLineInstanceId` = #{testLineInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="usedReport != null" >
        UsedReport = #{usedReport,jdbcType=INTEGER},
      </if>
      <if test="lastDownloadBy != null" >
        LastDownloadBy = #{lastDownloadBy,jdbcType=VARCHAR},
      </if>
      <if test="tag != null" >
        Tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="lastDownloadDate != null" >
        LastDownloadDate = #{lastDownloadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="showInReport != null">
        ShowInReport = #{showInReport,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderAttachmentInfoPO" >
    update tb_order_attachment
    set FileID = #{fileID,jdbcType=VARCHAR},
      GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      ObjectID = #{objectID,jdbcType=VARCHAR},
      BusinessType = #{businessType,jdbcType=VARCHAR},
      CloudID = #{cloudID,jdbcType=VARCHAR},
      ReportNo = #{reportNo,jdbcType=VARCHAR},
      SampleNo = #{sampleNo,jdbcType=VARCHAR},
      PhotoType = #{photoType,jdbcType=INTEGER},
      SuffixNum = #{suffixNum,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ToCp = #{toCp,jdbcType=INTEGER},
      `Source` = #{source,jdbcType=VARCHAR},
      `TestLineInstanceId` = #{testLineInstanceId,jdbcType=VARCHAR},
      UsedReport = #{usedReport,jdbcType=INTEGER},
      LastDownloadBy = #{lastDownloadBy,jdbcType=VARCHAR},
      Tag = #{tag,jdbcType=VARCHAR},
      LastDownloadDate = #{lastDownloadDate,jdbcType=TIMESTAMP}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <insert id="insertBatch">
    insert into tb_order_attachment (ID, FileID, GeneralOrderID,
      ObjectID, BusinessType, CloudID,
      ReportNo, SampleNo,PhotoType,SuffixNum,
      ActiveIndicator, CreatedBy,
      CreatedDate, ModifiedBy, ModifiedDate,ToCp,`Source`,TestLineInstanceId,UsedReport,LastDownloadBy,Tag,LastDownloadDate,AttachmentName
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.ID,jdbcType=VARCHAR}, #{item.fileID,jdbcType=VARCHAR}, #{item.generalOrderID,jdbcType=VARCHAR},
      #{item.objectID,jdbcType=VARCHAR}, #{item.businessType,jdbcType=VARCHAR}, #{item.cloudID,jdbcType=VARCHAR},
      #{item.reportNo,jdbcType=VARCHAR}, #{item.sampleNo,jdbcType=VARCHAR},#{item.photoType,jdbcType=INTEGER},#{item.suffixNum,jdbcType=VARCHAR},
      1, #{item.createdBy,jdbcType=VARCHAR},
      #{item.createdDate,jdbcType=TIMESTAMP}, #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP},
      #{item.toCp,jdbcType=INTEGER},#{item.source,jdbcType=VARCHAR},#{item.testLineInstanceId,jdbcType=VARCHAR},#{item.usedReport,jdbcType=INTEGER},
      #{item.lastDownloadBy,jdbcType=VARCHAR},#{item.tag,jdbcType=VARCHAR},#{item.lastDownloadDate,jdbcType=TIMESTAMP},#{item.attachmentName,jdbcType=VARCHAR}
      )
    </foreach>

  </insert>

  <update id="updateReportNoByReportNo">
    update tb_order_attachment set ReportNo = #{req.newReportNo} where ReportNo = #{req.oldReportNo} and ActiveIndicator = 1 and BusinessType = #{req.businessType}
  </update>

  <delete id="deleteByReportNo">
    delete from tb_order_attachment where ReportNo = #{req.reportNo} and BusinessType = #{req.businessType}
  </delete>

</mapper>