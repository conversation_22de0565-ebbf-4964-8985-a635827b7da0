<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.BossOrderInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="BOSSOrderNo" property="BOSSOrderNo" jdbcType="VARCHAR" />
    <result column="BOSSOrderStatus" property="BOSSOrderStatus" jdbcType="VARCHAR" />
    <result column="TrueOrderNo" property="trueOrderNo" jdbcType="VARCHAR" />
    <result column="TotalAmount" property="totalAmount" jdbcType="DECIMAL" />
    <result column="DeleteFlag" property="deleteFlag" jdbcType="BIT" />
    <result column="CustomerCompany" property="customerCompany" jdbcType="VARCHAR" />
    <result column="ErrorMessage" property="errorMessage" jdbcType="VARCHAR" />
    <result column="Hattribute1" property="hattribute1" jdbcType="VARCHAR" />
    <result column="Hattribute12" property="hattribute12" jdbcType="VARCHAR" />
    <result column="Hattribute13" property="hattribute13" jdbcType="VARCHAR" />
    <result column="Hattribute14" property="hattribute14" jdbcType="VARCHAR" />
    <result column="Hattribute15" property="hattribute15" jdbcType="VARCHAR" />
    <result column="Hattribute2" property="hattribute2" jdbcType="VARCHAR" />
    <result column="Hattribute4" property="hattribute4" jdbcType="VARCHAR" />
    <result column="Hattribute5" property="hattribute5" jdbcType="VARCHAR" />
    <result column="Hattribute6" property="hattribute6" jdbcType="VARCHAR" />
    <result column="Hattribute8" property="hattribute8" jdbcType="VARCHAR" />
    <result column="Hattribute10" property="hattribute10" jdbcType="VARCHAR" />
    <result column="Hattribute9" property="hattribute9" jdbcType="VARCHAR" />
    <result column="HbookedFlag" property="hbookedFlag" jdbcType="VARCHAR" />
    <result column="HcancelledFlag" property="hcancelledFlag" jdbcType="VARCHAR" />
    <result column="HclosedFlag" property="hclosedFlag" jdbcType="VARCHAR" />
    <result column="Hcontext" property="hcontext" jdbcType="VARCHAR" />
    <result column="HconversionType" property="hconversionType" jdbcType="VARCHAR" />
    <result column="HinvoicetoorgID" property="hinvoicetoorgID" jdbcType="INTEGER" />
    <result column="HoperationCode" property="hoperationCode" jdbcType="VARCHAR" />
    <result column="HorderCategory" property="horderCategory" jdbcType="VARCHAR" />
    <result column="HorderedDate" property="horderedDate" jdbcType="TIMESTAMP" />
    <result column="HorderSourceName" property="horderSourceName" jdbcType="VARCHAR" />
    <result column="HorderTypeName" property="horderTypeName" jdbcType="VARCHAR" />
    <result column="HorderNumberToAddLine" property="horderNumberToAddLine" jdbcType="VARCHAR" />
    <result column="HpaymentTermName" property="hpaymentTermName" jdbcType="VARCHAR" />
    <result column="HpriceListName" property="hpriceListName" jdbcType="VARCHAR" />
    <result column="HpushStatus" property="hpushStatus" jdbcType="INTEGER" />
    <result column="HsalesrepID" property="hsalesrepID" jdbcType="VARCHAR" />
    <result column="HshipFromOrgName" property="hshipFromOrgName" jdbcType="VARCHAR" />
    <result column="HshiptoorgID" property="hshiptoorgID" jdbcType="INTEGER" />
    <result column="HsoldFromOrgName" property="hsoldFromOrgName" jdbcType="VARCHAR" />
    <result column="HsoldToCust" property="hsoldToCust" jdbcType="VARCHAR" />
    <result column="HtaxFlag" property="htaxFlag" jdbcType="VARCHAR" />
    <result column="HtransactionalCurrCode" property="htransactionalCurrCode" jdbcType="VARCHAR" />
    <result column="OrgName" property="orgName" jdbcType="VARCHAR" />
    <result column="SuccessFlag" property="successFlag" jdbcType="BIT" />
    <result column="ReviseReason" property="reviseReason" jdbcType="VARCHAR" />
    <result column="CustomerID" property="customerID" jdbcType="VARCHAR" />
    <result column="StoreID" property="storeID" jdbcType="VARCHAR" />
    <result column="ShipName" property="shipName" jdbcType="VARCHAR" />
    <result column="BillName" property="billName" jdbcType="VARCHAR" />
    <result column="BuyerName" property="buyerName" jdbcType="VARCHAR" />
    <result column="Hattribute3" property="hattribute3" jdbcType="VARCHAR" />
    <result column="Hattribute7" property="hattribute7" jdbcType="VARCHAR" />
    <result column="Hattribute11" property="hattribute11" jdbcType="VARCHAR" />
    <result column="Hattribute16" property="hattribute16" jdbcType="VARCHAR" />
    <result column="HcustomerPoNumber" property="hcustomerPoNumber" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="HdelivertoorgID" property="hdelivertoorgID" jdbcType="INTEGER" />
    <result column="DeliverName" property="deliverName" jdbcType="VARCHAR" />
    <result column="HbillToContactID" property="hbillToContactID" jdbcType="INTEGER" />
    <result column="HbillContactName" property="hbillContactName" jdbcType="VARCHAR" />
    <result column="HshipToContactID" property="hshipToContactID" jdbcType="INTEGER" />
    <result column="HshipContactName" property="hshipContactName" jdbcType="VARCHAR" />
    <result column="HdeliverToContactID" property="hdeliverToContactID" jdbcType="INTEGER" />
    <result column="HdeliverContactName" property="hdeliverContactName" jdbcType="VARCHAR" />
    <result column="createByBossUser" property="createByBossUser" jdbcType="VARCHAR" />
    <result column="createByBossUserResponsibility" property="createByBossUserResponsibility" jdbcType="VARCHAR" />
    <result column="shipToAddress" property="shipToAddress" jdbcType="VARCHAR" />
    <result column="billToAddress" property="billToAddress" jdbcType="VARCHAR" />
    <result column="certificateReportNB" property="certificateReportNB" jdbcType="VARCHAR" />
    <result column="deliverToAddress" property="deliverToAddress" jdbcType="VARCHAR" />
    <result column="VersionID" property="versionID" jdbcType="INTEGER" />
    <result column="ToBossDate" property="toBossDate" jdbcType="TIMESTAMP" />
    <result column="ToBossBy" property="toBossBy" jdbcType="VARCHAR" />
    <result column="QuotationHeadId" property="quotationHeadId" jdbcType="VARCHAR" />
    <result column="HSalesPerson" property="HSalesPerson" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, BOSSOrderNo, BOSSOrderStatus, TrueOrderNo, TotalAmount, DeleteFlag, CustomerCompany, 
    ErrorMessage, Hattribute1, Hattribute12, Hattribute13, Hattribute14, Hattribute15, 
    Hattribute2, Hattribute4, Hattribute5, Hattribute6, Hattribute8, Hattribute10, Hattribute9, 
    HbookedFlag, HcancelledFlag, HclosedFlag, Hcontext, HconversionType, HinvoicetoorgID, 
    HoperationCode, HorderCategory, HorderedDate, HorderSourceName, HorderTypeName, HorderNumberToAddLine, 
    HpaymentTermName, HpriceListName, HpushStatus, HsalesrepID, HshipFromOrgName, HshiptoorgID, 
    HsoldFromOrgName, HsoldToCust, HtaxFlag, HtransactionalCurrCode, OrgName, SuccessFlag, 
    ReviseReason, CustomerID, StoreID, ShipName, BillName, BuyerName, Hattribute3, Hattribute7, 
    Hattribute11, Hattribute16, HcustomerPoNumber, ActiveIndicator, CreatedBy, CreatedDate, 
    ModifiedBy, ModifiedDate, HdelivertoorgID, DeliverName, HbillToContactID, HbillContactName, 
    HshipToContactID, HshipContactName, HdeliverToContactID, HdeliverContactName, createByBossUser, 
    createByBossUserResponsibility, shipToAddress, billToAddress, certificateReportNB, 
    deliverToAddress, VersionID, ToBossDate, ToBossBy, QuotationHeadId, HSalesPerson
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_boss_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_boss_order
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_boss_order
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoExample" >
    delete from tb_boss_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoPO" >
    insert into tb_boss_order (ID, BOSSOrderNo, BOSSOrderStatus, 
      TrueOrderNo, TotalAmount, DeleteFlag, 
      CustomerCompany, ErrorMessage, Hattribute1, 
      Hattribute12, Hattribute13, Hattribute14, 
      Hattribute15, Hattribute2, Hattribute4, 
      Hattribute5, Hattribute6, Hattribute8, 
      Hattribute10, Hattribute9, HbookedFlag, 
      HcancelledFlag, HclosedFlag, Hcontext, 
      HconversionType, HinvoicetoorgID, HoperationCode, 
      HorderCategory, HorderedDate, HorderSourceName, 
      HorderTypeName, HorderNumberToAddLine, 
      HpaymentTermName, HpriceListName, HpushStatus, 
      HsalesrepID, HshipFromOrgName, HshiptoorgID, 
      HsoldFromOrgName, HsoldToCust, HtaxFlag, 
      HtransactionalCurrCode, OrgName, SuccessFlag, 
      ReviseReason, CustomerID, StoreID, 
      ShipName, BillName, BuyerName, 
      Hattribute3, Hattribute7, Hattribute11, 
      Hattribute16, HcustomerPoNumber, ActiveIndicator, 
      CreatedBy, CreatedDate, ModifiedBy, 
      ModifiedDate, HdelivertoorgID, DeliverName, 
      HbillToContactID, HbillContactName, HshipToContactID, 
      HshipContactName, HdeliverToContactID, 
      HdeliverContactName, createByBossUser, 
      createByBossUserResponsibility, shipToAddress, 
      billToAddress, certificateReportNB, deliverToAddress, 
      VersionID, ToBossDate, ToBossBy, 
      QuotationHeadId, HSalesPerson)
    values (#{ID,jdbcType=VARCHAR}, #{BOSSOrderNo,jdbcType=VARCHAR}, #{BOSSOrderStatus,jdbcType=VARCHAR}, 
      #{trueOrderNo,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL}, #{deleteFlag,jdbcType=BIT}, 
      #{customerCompany,jdbcType=VARCHAR}, #{errorMessage,jdbcType=VARCHAR}, #{hattribute1,jdbcType=VARCHAR}, 
      #{hattribute12,jdbcType=VARCHAR}, #{hattribute13,jdbcType=VARCHAR}, #{hattribute14,jdbcType=VARCHAR}, 
      #{hattribute15,jdbcType=VARCHAR}, #{hattribute2,jdbcType=VARCHAR}, #{hattribute4,jdbcType=VARCHAR}, 
      #{hattribute5,jdbcType=VARCHAR}, #{hattribute6,jdbcType=VARCHAR}, #{hattribute8,jdbcType=VARCHAR}, 
      #{hattribute10,jdbcType=VARCHAR}, #{hattribute9,jdbcType=VARCHAR}, #{hbookedFlag,jdbcType=VARCHAR}, 
      #{hcancelledFlag,jdbcType=VARCHAR}, #{hclosedFlag,jdbcType=VARCHAR}, #{hcontext,jdbcType=VARCHAR}, 
      #{hconversionType,jdbcType=VARCHAR}, #{hinvoicetoorgID,jdbcType=INTEGER}, #{hoperationCode,jdbcType=VARCHAR}, 
      #{horderCategory,jdbcType=VARCHAR}, #{horderedDate,jdbcType=TIMESTAMP}, #{horderSourceName,jdbcType=VARCHAR}, 
      #{horderTypeName,jdbcType=VARCHAR}, #{horderNumberToAddLine,jdbcType=VARCHAR}, 
      #{hpaymentTermName,jdbcType=VARCHAR}, #{hpriceListName,jdbcType=VARCHAR}, #{hpushStatus,jdbcType=INTEGER}, 
      #{hsalesrepID,jdbcType=VARCHAR}, #{hshipFromOrgName,jdbcType=VARCHAR}, #{hshiptoorgID,jdbcType=INTEGER}, 
      #{hsoldFromOrgName,jdbcType=VARCHAR}, #{hsoldToCust,jdbcType=VARCHAR}, #{htaxFlag,jdbcType=VARCHAR}, 
      #{htransactionalCurrCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{successFlag,jdbcType=BIT}, 
      #{reviseReason,jdbcType=VARCHAR}, #{customerID,jdbcType=VARCHAR}, #{storeID,jdbcType=VARCHAR}, 
      #{shipName,jdbcType=VARCHAR}, #{billName,jdbcType=VARCHAR}, #{buyerName,jdbcType=VARCHAR}, 
      #{hattribute3,jdbcType=VARCHAR}, #{hattribute7,jdbcType=VARCHAR}, #{hattribute11,jdbcType=VARCHAR}, 
      #{hattribute16,jdbcType=VARCHAR}, #{hcustomerPoNumber,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=BIT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{hdelivertoorgID,jdbcType=INTEGER}, #{deliverName,jdbcType=VARCHAR}, 
      #{hbillToContactID,jdbcType=INTEGER}, #{hbillContactName,jdbcType=VARCHAR}, #{hshipToContactID,jdbcType=INTEGER}, 
      #{hshipContactName,jdbcType=VARCHAR}, #{hdeliverToContactID,jdbcType=INTEGER}, 
      #{hdeliverContactName,jdbcType=VARCHAR}, #{createByBossUser,jdbcType=VARCHAR}, 
      #{createByBossUserResponsibility,jdbcType=VARCHAR}, #{shipToAddress,jdbcType=VARCHAR}, 
      #{billToAddress,jdbcType=VARCHAR}, #{certificateReportNB,jdbcType=VARCHAR}, #{deliverToAddress,jdbcType=VARCHAR}, 
      #{versionID,jdbcType=INTEGER}, #{toBossDate,jdbcType=TIMESTAMP}, #{toBossBy,jdbcType=VARCHAR}, 
      #{quotationHeadId,jdbcType=VARCHAR}, #{HSalesPerson,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoPO" >
    insert into tb_boss_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="BOSSOrderNo != null" >
        BOSSOrderNo,
      </if>
      <if test="BOSSOrderStatus != null" >
        BOSSOrderStatus,
      </if>
      <if test="trueOrderNo != null" >
        TrueOrderNo,
      </if>
      <if test="totalAmount != null" >
        TotalAmount,
      </if>
      <if test="deleteFlag != null" >
        DeleteFlag,
      </if>
      <if test="customerCompany != null" >
        CustomerCompany,
      </if>
      <if test="errorMessage != null" >
        ErrorMessage,
      </if>
      <if test="hattribute1 != null" >
        Hattribute1,
      </if>
      <if test="hattribute12 != null" >
        Hattribute12,
      </if>
      <if test="hattribute13 != null" >
        Hattribute13,
      </if>
      <if test="hattribute14 != null" >
        Hattribute14,
      </if>
      <if test="hattribute15 != null" >
        Hattribute15,
      </if>
      <if test="hattribute2 != null" >
        Hattribute2,
      </if>
      <if test="hattribute4 != null" >
        Hattribute4,
      </if>
      <if test="hattribute5 != null" >
        Hattribute5,
      </if>
      <if test="hattribute6 != null" >
        Hattribute6,
      </if>
      <if test="hattribute8 != null" >
        Hattribute8,
      </if>
      <if test="hattribute10 != null" >
        Hattribute10,
      </if>
      <if test="hattribute9 != null" >
        Hattribute9,
      </if>
      <if test="hbookedFlag != null" >
        HbookedFlag,
      </if>
      <if test="hcancelledFlag != null" >
        HcancelledFlag,
      </if>
      <if test="hclosedFlag != null" >
        HclosedFlag,
      </if>
      <if test="hcontext != null" >
        Hcontext,
      </if>
      <if test="hconversionType != null" >
        HconversionType,
      </if>
      <if test="hinvoicetoorgID != null" >
        HinvoicetoorgID,
      </if>
      <if test="hoperationCode != null" >
        HoperationCode,
      </if>
      <if test="horderCategory != null" >
        HorderCategory,
      </if>
      <if test="horderedDate != null" >
        HorderedDate,
      </if>
      <if test="horderSourceName != null" >
        HorderSourceName,
      </if>
      <if test="horderTypeName != null" >
        HorderTypeName,
      </if>
      <if test="horderNumberToAddLine != null" >
        HorderNumberToAddLine,
      </if>
      <if test="hpaymentTermName != null" >
        HpaymentTermName,
      </if>
      <if test="hpriceListName != null" >
        HpriceListName,
      </if>
      <if test="hpushStatus != null" >
        HpushStatus,
      </if>
      <if test="hsalesrepID != null" >
        HsalesrepID,
      </if>
      <if test="hshipFromOrgName != null" >
        HshipFromOrgName,
      </if>
      <if test="hshiptoorgID != null" >
        HshiptoorgID,
      </if>
      <if test="hsoldFromOrgName != null" >
        HsoldFromOrgName,
      </if>
      <if test="hsoldToCust != null" >
        HsoldToCust,
      </if>
      <if test="htaxFlag != null" >
        HtaxFlag,
      </if>
      <if test="htransactionalCurrCode != null" >
        HtransactionalCurrCode,
      </if>
      <if test="orgName != null" >
        OrgName,
      </if>
      <if test="successFlag != null" >
        SuccessFlag,
      </if>
      <if test="reviseReason != null" >
        ReviseReason,
      </if>
      <if test="customerID != null" >
        CustomerID,
      </if>
      <if test="storeID != null" >
        StoreID,
      </if>
      <if test="shipName != null" >
        ShipName,
      </if>
      <if test="billName != null" >
        BillName,
      </if>
      <if test="buyerName != null" >
        BuyerName,
      </if>
      <if test="hattribute3 != null" >
        Hattribute3,
      </if>
      <if test="hattribute7 != null" >
        Hattribute7,
      </if>
      <if test="hattribute11 != null" >
        Hattribute11,
      </if>
      <if test="hattribute16 != null" >
        Hattribute16,
      </if>
      <if test="hcustomerPoNumber != null" >
        HcustomerPoNumber,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="hdelivertoorgID != null" >
        HdelivertoorgID,
      </if>
      <if test="deliverName != null" >
        DeliverName,
      </if>
      <if test="hbillToContactID != null" >
        HbillToContactID,
      </if>
      <if test="hbillContactName != null" >
        HbillContactName,
      </if>
      <if test="hshipToContactID != null" >
        HshipToContactID,
      </if>
      <if test="hshipContactName != null" >
        HshipContactName,
      </if>
      <if test="hdeliverToContactID != null" >
        HdeliverToContactID,
      </if>
      <if test="hdeliverContactName != null" >
        HdeliverContactName,
      </if>
      <if test="createByBossUser != null" >
        createByBossUser,
      </if>
      <if test="createByBossUserResponsibility != null" >
        createByBossUserResponsibility,
      </if>
      <if test="shipToAddress != null" >
        shipToAddress,
      </if>
      <if test="billToAddress != null" >
        billToAddress,
      </if>
      <if test="certificateReportNB != null" >
        certificateReportNB,
      </if>
      <if test="deliverToAddress != null" >
        deliverToAddress,
      </if>
      <if test="versionID != null" >
        VersionID,
      </if>
      <if test="toBossDate != null" >
        ToBossDate,
      </if>
      <if test="toBossBy != null" >
        ToBossBy,
      </if>
      <if test="quotationHeadId != null" >
        QuotationHeadId,
      </if>
      <if test="HSalesPerson != null" >
        HSalesPerson,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="BOSSOrderNo != null" >
        #{BOSSOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="BOSSOrderStatus != null" >
        #{BOSSOrderStatus,jdbcType=VARCHAR},
      </if>
      <if test="trueOrderNo != null" >
        #{trueOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="customerCompany != null" >
        #{customerCompany,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null" >
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="hattribute1 != null" >
        #{hattribute1,jdbcType=VARCHAR},
      </if>
      <if test="hattribute12 != null" >
        #{hattribute12,jdbcType=VARCHAR},
      </if>
      <if test="hattribute13 != null" >
        #{hattribute13,jdbcType=VARCHAR},
      </if>
      <if test="hattribute14 != null" >
        #{hattribute14,jdbcType=VARCHAR},
      </if>
      <if test="hattribute15 != null" >
        #{hattribute15,jdbcType=VARCHAR},
      </if>
      <if test="hattribute2 != null" >
        #{hattribute2,jdbcType=VARCHAR},
      </if>
      <if test="hattribute4 != null" >
        #{hattribute4,jdbcType=VARCHAR},
      </if>
      <if test="hattribute5 != null" >
        #{hattribute5,jdbcType=VARCHAR},
      </if>
      <if test="hattribute6 != null" >
        #{hattribute6,jdbcType=VARCHAR},
      </if>
      <if test="hattribute8 != null" >
        #{hattribute8,jdbcType=VARCHAR},
      </if>
      <if test="hattribute10 != null" >
        #{hattribute10,jdbcType=VARCHAR},
      </if>
      <if test="hattribute9 != null" >
        #{hattribute9,jdbcType=VARCHAR},
      </if>
      <if test="hbookedFlag != null" >
        #{hbookedFlag,jdbcType=VARCHAR},
      </if>
      <if test="hcancelledFlag != null" >
        #{hcancelledFlag,jdbcType=VARCHAR},
      </if>
      <if test="hclosedFlag != null" >
        #{hclosedFlag,jdbcType=VARCHAR},
      </if>
      <if test="hcontext != null" >
        #{hcontext,jdbcType=VARCHAR},
      </if>
      <if test="hconversionType != null" >
        #{hconversionType,jdbcType=VARCHAR},
      </if>
      <if test="hinvoicetoorgID != null" >
        #{hinvoicetoorgID,jdbcType=INTEGER},
      </if>
      <if test="hoperationCode != null" >
        #{hoperationCode,jdbcType=VARCHAR},
      </if>
      <if test="horderCategory != null" >
        #{horderCategory,jdbcType=VARCHAR},
      </if>
      <if test="horderedDate != null" >
        #{horderedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="horderSourceName != null" >
        #{horderSourceName,jdbcType=VARCHAR},
      </if>
      <if test="horderTypeName != null" >
        #{horderTypeName,jdbcType=VARCHAR},
      </if>
      <if test="horderNumberToAddLine != null" >
        #{horderNumberToAddLine,jdbcType=VARCHAR},
      </if>
      <if test="hpaymentTermName != null" >
        #{hpaymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="hpriceListName != null" >
        #{hpriceListName,jdbcType=VARCHAR},
      </if>
      <if test="hpushStatus != null" >
        #{hpushStatus,jdbcType=INTEGER},
      </if>
      <if test="hsalesrepID != null" >
        #{hsalesrepID,jdbcType=VARCHAR},
      </if>
      <if test="hshipFromOrgName != null" >
        #{hshipFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="hshiptoorgID != null" >
        #{hshiptoorgID,jdbcType=INTEGER},
      </if>
      <if test="hsoldFromOrgName != null" >
        #{hsoldFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="hsoldToCust != null" >
        #{hsoldToCust,jdbcType=VARCHAR},
      </if>
      <if test="htaxFlag != null" >
        #{htaxFlag,jdbcType=VARCHAR},
      </if>
      <if test="htransactionalCurrCode != null" >
        #{htransactionalCurrCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="successFlag != null" >
        #{successFlag,jdbcType=BIT},
      </if>
      <if test="reviseReason != null" >
        #{reviseReason,jdbcType=VARCHAR},
      </if>
      <if test="customerID != null" >
        #{customerID,jdbcType=VARCHAR},
      </if>
      <if test="storeID != null" >
        #{storeID,jdbcType=VARCHAR},
      </if>
      <if test="shipName != null" >
        #{shipName,jdbcType=VARCHAR},
      </if>
      <if test="billName != null" >
        #{billName,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null" >
        #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="hattribute3 != null" >
        #{hattribute3,jdbcType=VARCHAR},
      </if>
      <if test="hattribute7 != null" >
        #{hattribute7,jdbcType=VARCHAR},
      </if>
      <if test="hattribute11 != null" >
        #{hattribute11,jdbcType=VARCHAR},
      </if>
      <if test="hattribute16 != null" >
        #{hattribute16,jdbcType=VARCHAR},
      </if>
      <if test="hcustomerPoNumber != null" >
        #{hcustomerPoNumber,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hdelivertoorgID != null" >
        #{hdelivertoorgID,jdbcType=INTEGER},
      </if>
      <if test="deliverName != null" >
        #{deliverName,jdbcType=VARCHAR},
      </if>
      <if test="hbillToContactID != null" >
        #{hbillToContactID,jdbcType=INTEGER},
      </if>
      <if test="hbillContactName != null" >
        #{hbillContactName,jdbcType=VARCHAR},
      </if>
      <if test="hshipToContactID != null" >
        #{hshipToContactID,jdbcType=INTEGER},
      </if>
      <if test="hshipContactName != null" >
        #{hshipContactName,jdbcType=VARCHAR},
      </if>
      <if test="hdeliverToContactID != null" >
        #{hdeliverToContactID,jdbcType=INTEGER},
      </if>
      <if test="hdeliverContactName != null" >
        #{hdeliverContactName,jdbcType=VARCHAR},
      </if>
      <if test="createByBossUser != null" >
        #{createByBossUser,jdbcType=VARCHAR},
      </if>
      <if test="createByBossUserResponsibility != null" >
        #{createByBossUserResponsibility,jdbcType=VARCHAR},
      </if>
      <if test="shipToAddress != null" >
        #{shipToAddress,jdbcType=VARCHAR},
      </if>
      <if test="billToAddress != null" >
        #{billToAddress,jdbcType=VARCHAR},
      </if>
      <if test="certificateReportNB != null" >
        #{certificateReportNB,jdbcType=VARCHAR},
      </if>
      <if test="deliverToAddress != null" >
        #{deliverToAddress,jdbcType=VARCHAR},
      </if>
      <if test="versionID != null" >
        #{versionID,jdbcType=INTEGER},
      </if>
      <if test="toBossDate != null" >
        #{toBossDate,jdbcType=TIMESTAMP},
      </if>
      <if test="toBossBy != null" >
        #{toBossBy,jdbcType=VARCHAR},
      </if>
      <if test="quotationHeadId != null" >
        #{quotationHeadId,jdbcType=VARCHAR},
      </if>
      <if test="HSalesPerson != null" >
        #{HSalesPerson,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_boss_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_boss_order
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.BOSSOrderNo != null" >
        BOSSOrderNo = #{record.BOSSOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.BOSSOrderStatus != null" >
        BOSSOrderStatus = #{record.BOSSOrderStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.trueOrderNo != null" >
        TrueOrderNo = #{record.trueOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.totalAmount != null" >
        TotalAmount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.deleteFlag != null" >
        DeleteFlag = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.customerCompany != null" >
        CustomerCompany = #{record.customerCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMessage != null" >
        ErrorMessage = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute1 != null" >
        Hattribute1 = #{record.hattribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute12 != null" >
        Hattribute12 = #{record.hattribute12,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute13 != null" >
        Hattribute13 = #{record.hattribute13,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute14 != null" >
        Hattribute14 = #{record.hattribute14,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute15 != null" >
        Hattribute15 = #{record.hattribute15,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute2 != null" >
        Hattribute2 = #{record.hattribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute4 != null" >
        Hattribute4 = #{record.hattribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute5 != null" >
        Hattribute5 = #{record.hattribute5,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute6 != null" >
        Hattribute6 = #{record.hattribute6,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute8 != null" >
        Hattribute8 = #{record.hattribute8,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute10 != null" >
        Hattribute10 = #{record.hattribute10,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute9 != null" >
        Hattribute9 = #{record.hattribute9,jdbcType=VARCHAR},
      </if>
      <if test="record.hbookedFlag != null" >
        HbookedFlag = #{record.hbookedFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.hcancelledFlag != null" >
        HcancelledFlag = #{record.hcancelledFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.hclosedFlag != null" >
        HclosedFlag = #{record.hclosedFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.hcontext != null" >
        Hcontext = #{record.hcontext,jdbcType=VARCHAR},
      </if>
      <if test="record.hconversionType != null" >
        HconversionType = #{record.hconversionType,jdbcType=VARCHAR},
      </if>
      <if test="record.hinvoicetoorgID != null" >
        HinvoicetoorgID = #{record.hinvoicetoorgID,jdbcType=INTEGER},
      </if>
      <if test="record.hoperationCode != null" >
        HoperationCode = #{record.hoperationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.horderCategory != null" >
        HorderCategory = #{record.horderCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.horderedDate != null" >
        HorderedDate = #{record.horderedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.horderSourceName != null" >
        HorderSourceName = #{record.horderSourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.horderTypeName != null" >
        HorderTypeName = #{record.horderTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.horderNumberToAddLine != null" >
        HorderNumberToAddLine = #{record.horderNumberToAddLine,jdbcType=VARCHAR},
      </if>
      <if test="record.hpaymentTermName != null" >
        HpaymentTermName = #{record.hpaymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="record.hpriceListName != null" >
        HpriceListName = #{record.hpriceListName,jdbcType=VARCHAR},
      </if>
      <if test="record.hpushStatus != null" >
        HpushStatus = #{record.hpushStatus,jdbcType=INTEGER},
      </if>
      <if test="record.hsalesrepID != null" >
        HsalesrepID = #{record.hsalesrepID,jdbcType=VARCHAR},
      </if>
      <if test="record.hshipFromOrgName != null" >
        HshipFromOrgName = #{record.hshipFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.hshiptoorgID != null" >
        HshiptoorgID = #{record.hshiptoorgID,jdbcType=INTEGER},
      </if>
      <if test="record.hsoldFromOrgName != null" >
        HsoldFromOrgName = #{record.hsoldFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.hsoldToCust != null" >
        HsoldToCust = #{record.hsoldToCust,jdbcType=VARCHAR},
      </if>
      <if test="record.htaxFlag != null" >
        HtaxFlag = #{record.htaxFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.htransactionalCurrCode != null" >
        HtransactionalCurrCode = #{record.htransactionalCurrCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null" >
        OrgName = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.successFlag != null" >
        SuccessFlag = #{record.successFlag,jdbcType=BIT},
      </if>
      <if test="record.reviseReason != null" >
        ReviseReason = #{record.reviseReason,jdbcType=VARCHAR},
      </if>
      <if test="record.customerID != null" >
        CustomerID = #{record.customerID,jdbcType=VARCHAR},
      </if>
      <if test="record.storeID != null" >
        StoreID = #{record.storeID,jdbcType=VARCHAR},
      </if>
      <if test="record.shipName != null" >
        ShipName = #{record.shipName,jdbcType=VARCHAR},
      </if>
      <if test="record.billName != null" >
        BillName = #{record.billName,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerName != null" >
        BuyerName = #{record.buyerName,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute3 != null" >
        Hattribute3 = #{record.hattribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute7 != null" >
        Hattribute7 = #{record.hattribute7,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute11 != null" >
        Hattribute11 = #{record.hattribute11,jdbcType=VARCHAR},
      </if>
      <if test="record.hattribute16 != null" >
        Hattribute16 = #{record.hattribute16,jdbcType=VARCHAR},
      </if>
      <if test="record.hcustomerPoNumber != null" >
        HcustomerPoNumber = #{record.hcustomerPoNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.hdelivertoorgID != null" >
        HdelivertoorgID = #{record.hdelivertoorgID,jdbcType=INTEGER},
      </if>
      <if test="record.deliverName != null" >
        DeliverName = #{record.deliverName,jdbcType=VARCHAR},
      </if>
      <if test="record.hbillToContactID != null" >
        HbillToContactID = #{record.hbillToContactID,jdbcType=INTEGER},
      </if>
      <if test="record.hbillContactName != null" >
        HbillContactName = #{record.hbillContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.hshipToContactID != null" >
        HshipToContactID = #{record.hshipToContactID,jdbcType=INTEGER},
      </if>
      <if test="record.hshipContactName != null" >
        HshipContactName = #{record.hshipContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.hdeliverToContactID != null" >
        HdeliverToContactID = #{record.hdeliverToContactID,jdbcType=INTEGER},
      </if>
      <if test="record.hdeliverContactName != null" >
        HdeliverContactName = #{record.hdeliverContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.createByBossUser != null" >
        createByBossUser = #{record.createByBossUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createByBossUserResponsibility != null" >
        createByBossUserResponsibility = #{record.createByBossUserResponsibility,jdbcType=VARCHAR},
      </if>
      <if test="record.shipToAddress != null" >
        shipToAddress = #{record.shipToAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.billToAddress != null" >
        billToAddress = #{record.billToAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.certificateReportNB != null" >
        certificateReportNB = #{record.certificateReportNB,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverToAddress != null" >
        deliverToAddress = #{record.deliverToAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.versionID != null" >
        VersionID = #{record.versionID,jdbcType=INTEGER},
      </if>
      <if test="record.toBossDate != null" >
        ToBossDate = #{record.toBossDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.toBossBy != null" >
        ToBossBy = #{record.toBossBy,jdbcType=VARCHAR},
      </if>
      <if test="record.quotationHeadId != null" >
        QuotationHeadId = #{record.quotationHeadId,jdbcType=VARCHAR},
      </if>
      <if test="record.HSalesPerson != null" >
        HSalesPerson = #{record.HSalesPerson,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_boss_order
    set ID = #{record.ID,jdbcType=VARCHAR},
      BOSSOrderNo = #{record.BOSSOrderNo,jdbcType=VARCHAR},
      BOSSOrderStatus = #{record.BOSSOrderStatus,jdbcType=VARCHAR},
      TrueOrderNo = #{record.trueOrderNo,jdbcType=VARCHAR},
      TotalAmount = #{record.totalAmount,jdbcType=DECIMAL},
      DeleteFlag = #{record.deleteFlag,jdbcType=BIT},
      CustomerCompany = #{record.customerCompany,jdbcType=VARCHAR},
      ErrorMessage = #{record.errorMessage,jdbcType=VARCHAR},
      Hattribute1 = #{record.hattribute1,jdbcType=VARCHAR},
      Hattribute12 = #{record.hattribute12,jdbcType=VARCHAR},
      Hattribute13 = #{record.hattribute13,jdbcType=VARCHAR},
      Hattribute14 = #{record.hattribute14,jdbcType=VARCHAR},
      Hattribute15 = #{record.hattribute15,jdbcType=VARCHAR},
      Hattribute2 = #{record.hattribute2,jdbcType=VARCHAR},
      Hattribute4 = #{record.hattribute4,jdbcType=VARCHAR},
      Hattribute5 = #{record.hattribute5,jdbcType=VARCHAR},
      Hattribute6 = #{record.hattribute6,jdbcType=VARCHAR},
      Hattribute8 = #{record.hattribute8,jdbcType=VARCHAR},
      Hattribute10 = #{record.hattribute10,jdbcType=VARCHAR},
      Hattribute9 = #{record.hattribute9,jdbcType=VARCHAR},
      HbookedFlag = #{record.hbookedFlag,jdbcType=VARCHAR},
      HcancelledFlag = #{record.hcancelledFlag,jdbcType=VARCHAR},
      HclosedFlag = #{record.hclosedFlag,jdbcType=VARCHAR},
      Hcontext = #{record.hcontext,jdbcType=VARCHAR},
      HconversionType = #{record.hconversionType,jdbcType=VARCHAR},
      HinvoicetoorgID = #{record.hinvoicetoorgID,jdbcType=INTEGER},
      HoperationCode = #{record.hoperationCode,jdbcType=VARCHAR},
      HorderCategory = #{record.horderCategory,jdbcType=VARCHAR},
      HorderedDate = #{record.horderedDate,jdbcType=TIMESTAMP},
      HorderSourceName = #{record.horderSourceName,jdbcType=VARCHAR},
      HorderTypeName = #{record.horderTypeName,jdbcType=VARCHAR},
      HorderNumberToAddLine = #{record.horderNumberToAddLine,jdbcType=VARCHAR},
      HpaymentTermName = #{record.hpaymentTermName,jdbcType=VARCHAR},
      HpriceListName = #{record.hpriceListName,jdbcType=VARCHAR},
      HpushStatus = #{record.hpushStatus,jdbcType=INTEGER},
      HsalesrepID = #{record.hsalesrepID,jdbcType=VARCHAR},
      HshipFromOrgName = #{record.hshipFromOrgName,jdbcType=VARCHAR},
      HshiptoorgID = #{record.hshiptoorgID,jdbcType=INTEGER},
      HsoldFromOrgName = #{record.hsoldFromOrgName,jdbcType=VARCHAR},
      HsoldToCust = #{record.hsoldToCust,jdbcType=VARCHAR},
      HtaxFlag = #{record.htaxFlag,jdbcType=VARCHAR},
      HtransactionalCurrCode = #{record.htransactionalCurrCode,jdbcType=VARCHAR},
      OrgName = #{record.orgName,jdbcType=VARCHAR},
      SuccessFlag = #{record.successFlag,jdbcType=BIT},
      ReviseReason = #{record.reviseReason,jdbcType=VARCHAR},
      CustomerID = #{record.customerID,jdbcType=VARCHAR},
      StoreID = #{record.storeID,jdbcType=VARCHAR},
      ShipName = #{record.shipName,jdbcType=VARCHAR},
      BillName = #{record.billName,jdbcType=VARCHAR},
      BuyerName = #{record.buyerName,jdbcType=VARCHAR},
      Hattribute3 = #{record.hattribute3,jdbcType=VARCHAR},
      Hattribute7 = #{record.hattribute7,jdbcType=VARCHAR},
      Hattribute11 = #{record.hattribute11,jdbcType=VARCHAR},
      Hattribute16 = #{record.hattribute16,jdbcType=VARCHAR},
      HcustomerPoNumber = #{record.hcustomerPoNumber,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      HdelivertoorgID = #{record.hdelivertoorgID,jdbcType=INTEGER},
      DeliverName = #{record.deliverName,jdbcType=VARCHAR},
      HbillToContactID = #{record.hbillToContactID,jdbcType=INTEGER},
      HbillContactName = #{record.hbillContactName,jdbcType=VARCHAR},
      HshipToContactID = #{record.hshipToContactID,jdbcType=INTEGER},
      HshipContactName = #{record.hshipContactName,jdbcType=VARCHAR},
      HdeliverToContactID = #{record.hdeliverToContactID,jdbcType=INTEGER},
      HdeliverContactName = #{record.hdeliverContactName,jdbcType=VARCHAR},
      createByBossUser = #{record.createByBossUser,jdbcType=VARCHAR},
      createByBossUserResponsibility = #{record.createByBossUserResponsibility,jdbcType=VARCHAR},
      shipToAddress = #{record.shipToAddress,jdbcType=VARCHAR},
      billToAddress = #{record.billToAddress,jdbcType=VARCHAR},
      certificateReportNB = #{record.certificateReportNB,jdbcType=VARCHAR},
      deliverToAddress = #{record.deliverToAddress,jdbcType=VARCHAR},
      VersionID = #{record.versionID,jdbcType=INTEGER},
      ToBossDate = #{record.toBossDate,jdbcType=TIMESTAMP},
      ToBossBy = #{record.toBossBy,jdbcType=VARCHAR},
      QuotationHeadId = #{record.quotationHeadId,jdbcType=VARCHAR},
      HSalesPerson = #{record.HSalesPerson,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoPO" >
    update tb_boss_order
    <set >
      <if test="BOSSOrderNo != null" >
        BOSSOrderNo = #{BOSSOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="BOSSOrderStatus != null" >
        BOSSOrderStatus = #{BOSSOrderStatus,jdbcType=VARCHAR},
      </if>
      <if test="trueOrderNo != null" >
        TrueOrderNo = #{trueOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null" >
        TotalAmount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null" >
        DeleteFlag = #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="customerCompany != null" >
        CustomerCompany = #{customerCompany,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null" >
        ErrorMessage = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="hattribute1 != null" >
        Hattribute1 = #{hattribute1,jdbcType=VARCHAR},
      </if>
      <if test="hattribute12 != null" >
        Hattribute12 = #{hattribute12,jdbcType=VARCHAR},
      </if>
      <if test="hattribute13 != null" >
        Hattribute13 = #{hattribute13,jdbcType=VARCHAR},
      </if>
      <if test="hattribute14 != null" >
        Hattribute14 = #{hattribute14,jdbcType=VARCHAR},
      </if>
      <if test="hattribute15 != null" >
        Hattribute15 = #{hattribute15,jdbcType=VARCHAR},
      </if>
      <if test="hattribute2 != null" >
        Hattribute2 = #{hattribute2,jdbcType=VARCHAR},
      </if>
      <if test="hattribute4 != null" >
        Hattribute4 = #{hattribute4,jdbcType=VARCHAR},
      </if>
      <if test="hattribute5 != null" >
        Hattribute5 = #{hattribute5,jdbcType=VARCHAR},
      </if>
      <if test="hattribute6 != null" >
        Hattribute6 = #{hattribute6,jdbcType=VARCHAR},
      </if>
      <if test="hattribute8 != null" >
        Hattribute8 = #{hattribute8,jdbcType=VARCHAR},
      </if>
      <if test="hattribute10 != null" >
        Hattribute10 = #{hattribute10,jdbcType=VARCHAR},
      </if>
      <if test="hattribute9 != null" >
        Hattribute9 = #{hattribute9,jdbcType=VARCHAR},
      </if>
      <if test="hbookedFlag != null" >
        HbookedFlag = #{hbookedFlag,jdbcType=VARCHAR},
      </if>
      <if test="hcancelledFlag != null" >
        HcancelledFlag = #{hcancelledFlag,jdbcType=VARCHAR},
      </if>
      <if test="hclosedFlag != null" >
        HclosedFlag = #{hclosedFlag,jdbcType=VARCHAR},
      </if>
      <if test="hcontext != null" >
        Hcontext = #{hcontext,jdbcType=VARCHAR},
      </if>
      <if test="hconversionType != null" >
        HconversionType = #{hconversionType,jdbcType=VARCHAR},
      </if>
      <if test="hinvoicetoorgID != null" >
        HinvoicetoorgID = #{hinvoicetoorgID,jdbcType=INTEGER},
      </if>
      <if test="hoperationCode != null" >
        HoperationCode = #{hoperationCode,jdbcType=VARCHAR},
      </if>
      <if test="horderCategory != null" >
        HorderCategory = #{horderCategory,jdbcType=VARCHAR},
      </if>
      <if test="horderedDate != null" >
        HorderedDate = #{horderedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="horderSourceName != null" >
        HorderSourceName = #{horderSourceName,jdbcType=VARCHAR},
      </if>
      <if test="horderTypeName != null" >
        HorderTypeName = #{horderTypeName,jdbcType=VARCHAR},
      </if>
      <if test="horderNumberToAddLine != null" >
        HorderNumberToAddLine = #{horderNumberToAddLine,jdbcType=VARCHAR},
      </if>
      <if test="hpaymentTermName != null" >
        HpaymentTermName = #{hpaymentTermName,jdbcType=VARCHAR},
      </if>
      <if test="hpriceListName != null" >
        HpriceListName = #{hpriceListName,jdbcType=VARCHAR},
      </if>
      <if test="hpushStatus != null" >
        HpushStatus = #{hpushStatus,jdbcType=INTEGER},
      </if>
      <if test="hsalesrepID != null" >
        HsalesrepID = #{hsalesrepID,jdbcType=VARCHAR},
      </if>
      <if test="hshipFromOrgName != null" >
        HshipFromOrgName = #{hshipFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="hshiptoorgID != null" >
        HshiptoorgID = #{hshiptoorgID,jdbcType=INTEGER},
      </if>
      <if test="hsoldFromOrgName != null" >
        HsoldFromOrgName = #{hsoldFromOrgName,jdbcType=VARCHAR},
      </if>
      <if test="hsoldToCust != null" >
        HsoldToCust = #{hsoldToCust,jdbcType=VARCHAR},
      </if>
      <if test="htaxFlag != null" >
        HtaxFlag = #{htaxFlag,jdbcType=VARCHAR},
      </if>
      <if test="htransactionalCurrCode != null" >
        HtransactionalCurrCode = #{htransactionalCurrCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        OrgName = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="successFlag != null" >
        SuccessFlag = #{successFlag,jdbcType=BIT},
      </if>
      <if test="reviseReason != null" >
        ReviseReason = #{reviseReason,jdbcType=VARCHAR},
      </if>
      <if test="customerID != null" >
        CustomerID = #{customerID,jdbcType=VARCHAR},
      </if>
      <if test="storeID != null" >
        StoreID = #{storeID,jdbcType=VARCHAR},
      </if>
      <if test="shipName != null" >
        ShipName = #{shipName,jdbcType=VARCHAR},
      </if>
      <if test="billName != null" >
        BillName = #{billName,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null" >
        BuyerName = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="hattribute3 != null" >
        Hattribute3 = #{hattribute3,jdbcType=VARCHAR},
      </if>
      <if test="hattribute7 != null" >
        Hattribute7 = #{hattribute7,jdbcType=VARCHAR},
      </if>
      <if test="hattribute11 != null" >
        Hattribute11 = #{hattribute11,jdbcType=VARCHAR},
      </if>
      <if test="hattribute16 != null" >
        Hattribute16 = #{hattribute16,jdbcType=VARCHAR},
      </if>
      <if test="hcustomerPoNumber != null" >
        HcustomerPoNumber = #{hcustomerPoNumber,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hdelivertoorgID != null" >
        HdelivertoorgID = #{hdelivertoorgID,jdbcType=INTEGER},
      </if>
      <if test="deliverName != null" >
        DeliverName = #{deliverName,jdbcType=VARCHAR},
      </if>
      <if test="hbillToContactID != null" >
        HbillToContactID = #{hbillToContactID,jdbcType=INTEGER},
      </if>
      <if test="hbillContactName != null" >
        HbillContactName = #{hbillContactName,jdbcType=VARCHAR},
      </if>
      <if test="hshipToContactID != null" >
        HshipToContactID = #{hshipToContactID,jdbcType=INTEGER},
      </if>
      <if test="hshipContactName != null" >
        HshipContactName = #{hshipContactName,jdbcType=VARCHAR},
      </if>
      <if test="hdeliverToContactID != null" >
        HdeliverToContactID = #{hdeliverToContactID,jdbcType=INTEGER},
      </if>
      <if test="hdeliverContactName != null" >
        HdeliverContactName = #{hdeliverContactName,jdbcType=VARCHAR},
      </if>
      <if test="createByBossUser != null" >
        createByBossUser = #{createByBossUser,jdbcType=VARCHAR},
      </if>
      <if test="createByBossUserResponsibility != null" >
        createByBossUserResponsibility = #{createByBossUserResponsibility,jdbcType=VARCHAR},
      </if>
      <if test="shipToAddress != null" >
        shipToAddress = #{shipToAddress,jdbcType=VARCHAR},
      </if>
      <if test="billToAddress != null" >
        billToAddress = #{billToAddress,jdbcType=VARCHAR},
      </if>
      <if test="certificateReportNB != null" >
        certificateReportNB = #{certificateReportNB,jdbcType=VARCHAR},
      </if>
      <if test="deliverToAddress != null" >
        deliverToAddress = #{deliverToAddress,jdbcType=VARCHAR},
      </if>
      <if test="versionID != null" >
        VersionID = #{versionID,jdbcType=INTEGER},
      </if>
      <if test="toBossDate != null" >
        ToBossDate = #{toBossDate,jdbcType=TIMESTAMP},
      </if>
      <if test="toBossBy != null" >
        ToBossBy = #{toBossBy,jdbcType=VARCHAR},
      </if>
      <if test="quotationHeadId != null" >
        QuotationHeadId = #{quotationHeadId,jdbcType=VARCHAR},
      </if>
      <if test="HSalesPerson != null" >
        HSalesPerson = #{HSalesPerson,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderInfoPO" >
    update tb_boss_order
    set BOSSOrderNo = #{BOSSOrderNo,jdbcType=VARCHAR},
      BOSSOrderStatus = #{BOSSOrderStatus,jdbcType=VARCHAR},
      TrueOrderNo = #{trueOrderNo,jdbcType=VARCHAR},
      TotalAmount = #{totalAmount,jdbcType=DECIMAL},
      DeleteFlag = #{deleteFlag,jdbcType=BIT},
      CustomerCompany = #{customerCompany,jdbcType=VARCHAR},
      ErrorMessage = #{errorMessage,jdbcType=VARCHAR},
      Hattribute1 = #{hattribute1,jdbcType=VARCHAR},
      Hattribute12 = #{hattribute12,jdbcType=VARCHAR},
      Hattribute13 = #{hattribute13,jdbcType=VARCHAR},
      Hattribute14 = #{hattribute14,jdbcType=VARCHAR},
      Hattribute15 = #{hattribute15,jdbcType=VARCHAR},
      Hattribute2 = #{hattribute2,jdbcType=VARCHAR},
      Hattribute4 = #{hattribute4,jdbcType=VARCHAR},
      Hattribute5 = #{hattribute5,jdbcType=VARCHAR},
      Hattribute6 = #{hattribute6,jdbcType=VARCHAR},
      Hattribute8 = #{hattribute8,jdbcType=VARCHAR},
      Hattribute10 = #{hattribute10,jdbcType=VARCHAR},
      Hattribute9 = #{hattribute9,jdbcType=VARCHAR},
      HbookedFlag = #{hbookedFlag,jdbcType=VARCHAR},
      HcancelledFlag = #{hcancelledFlag,jdbcType=VARCHAR},
      HclosedFlag = #{hclosedFlag,jdbcType=VARCHAR},
      Hcontext = #{hcontext,jdbcType=VARCHAR},
      HconversionType = #{hconversionType,jdbcType=VARCHAR},
      HinvoicetoorgID = #{hinvoicetoorgID,jdbcType=INTEGER},
      HoperationCode = #{hoperationCode,jdbcType=VARCHAR},
      HorderCategory = #{horderCategory,jdbcType=VARCHAR},
      HorderedDate = #{horderedDate,jdbcType=TIMESTAMP},
      HorderSourceName = #{horderSourceName,jdbcType=VARCHAR},
      HorderTypeName = #{horderTypeName,jdbcType=VARCHAR},
      HorderNumberToAddLine = #{horderNumberToAddLine,jdbcType=VARCHAR},
      HpaymentTermName = #{hpaymentTermName,jdbcType=VARCHAR},
      HpriceListName = #{hpriceListName,jdbcType=VARCHAR},
      HpushStatus = #{hpushStatus,jdbcType=INTEGER},
      HsalesrepID = #{hsalesrepID,jdbcType=VARCHAR},
      HshipFromOrgName = #{hshipFromOrgName,jdbcType=VARCHAR},
      HshiptoorgID = #{hshiptoorgID,jdbcType=INTEGER},
      HsoldFromOrgName = #{hsoldFromOrgName,jdbcType=VARCHAR},
      HsoldToCust = #{hsoldToCust,jdbcType=VARCHAR},
      HtaxFlag = #{htaxFlag,jdbcType=VARCHAR},
      HtransactionalCurrCode = #{htransactionalCurrCode,jdbcType=VARCHAR},
      OrgName = #{orgName,jdbcType=VARCHAR},
      SuccessFlag = #{successFlag,jdbcType=BIT},
      ReviseReason = #{reviseReason,jdbcType=VARCHAR},
      CustomerID = #{customerID,jdbcType=VARCHAR},
      StoreID = #{storeID,jdbcType=VARCHAR},
      ShipName = #{shipName,jdbcType=VARCHAR},
      BillName = #{billName,jdbcType=VARCHAR},
      BuyerName = #{buyerName,jdbcType=VARCHAR},
      Hattribute3 = #{hattribute3,jdbcType=VARCHAR},
      Hattribute7 = #{hattribute7,jdbcType=VARCHAR},
      Hattribute11 = #{hattribute11,jdbcType=VARCHAR},
      Hattribute16 = #{hattribute16,jdbcType=VARCHAR},
      HcustomerPoNumber = #{hcustomerPoNumber,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      HdelivertoorgID = #{hdelivertoorgID,jdbcType=INTEGER},
      DeliverName = #{deliverName,jdbcType=VARCHAR},
      HbillToContactID = #{hbillToContactID,jdbcType=INTEGER},
      HbillContactName = #{hbillContactName,jdbcType=VARCHAR},
      HshipToContactID = #{hshipToContactID,jdbcType=INTEGER},
      HshipContactName = #{hshipContactName,jdbcType=VARCHAR},
      HdeliverToContactID = #{hdeliverToContactID,jdbcType=INTEGER},
      HdeliverContactName = #{hdeliverContactName,jdbcType=VARCHAR},
      createByBossUser = #{createByBossUser,jdbcType=VARCHAR},
      createByBossUserResponsibility = #{createByBossUserResponsibility,jdbcType=VARCHAR},
      shipToAddress = #{shipToAddress,jdbcType=VARCHAR},
      billToAddress = #{billToAddress,jdbcType=VARCHAR},
      certificateReportNB = #{certificateReportNB,jdbcType=VARCHAR},
      deliverToAddress = #{deliverToAddress,jdbcType=VARCHAR},
      VersionID = #{versionID,jdbcType=INTEGER},
      ToBossDate = #{toBossDate,jdbcType=TIMESTAMP},
      ToBossBy = #{toBossBy,jdbcType=VARCHAR},
      QuotationHeadId = #{quotationHeadId,jdbcType=VARCHAR},
      HSalesPerson = #{HSalesPerson,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>