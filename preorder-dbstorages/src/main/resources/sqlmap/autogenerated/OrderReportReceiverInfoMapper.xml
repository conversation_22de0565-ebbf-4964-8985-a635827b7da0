<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.OrderReportReceiverInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="GeneralOrderID" property="generalOrderID" jdbcType="VARCHAR" />
    <result column="CustomerID" property="customerID" jdbcType="VARCHAR" />
    <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
    <result column="ReportHeader" property="reportHeader" jdbcType="VARCHAR" />
    <result column="ReportDeliveredTo" property="reportDeliveredTo" jdbcType="VARCHAR" />
    <result column="ReportReceiverName" property="reportReceiverName" jdbcType="VARCHAR" />
    <result column="ReportReceiverPhone" property="reportReceiverPhone" jdbcType="VARCHAR" />
    <result column="ReceiverType" property="receiverType" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="ReportInfoLock" property="reportInfoLock" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, GeneralOrderID, CustomerID, ReportNo, ReportHeader, ReportDeliveredTo, ReportReceiverName, 
    ReportReceiverPhone, ReceiverType, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, 
    ModifiedDate, LanguageId, `ReportInfoLock`
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_order_report_receiver
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_order_report_receiver
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_order_report_receiver
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoExample" >
    delete from tb_order_report_receiver
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoPO" >
    insert into tb_order_report_receiver (ID, GeneralOrderID, CustomerID, 
      ReportNo, ReportHeader, ReportDeliveredTo, 
      ReportReceiverName, ReportReceiverPhone, 
      ReceiverType, ActiveIndicator, CreatedBy, 
      CreatedDate, ModifiedBy, ModifiedDate, 
      LanguageId, `ReportInfoLock`)
    values (#{ID,jdbcType=VARCHAR}, #{generalOrderID,jdbcType=VARCHAR}, #{customerID,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{reportHeader,jdbcType=VARCHAR}, #{reportDeliveredTo,jdbcType=VARCHAR}, 
      #{reportReceiverName,jdbcType=VARCHAR}, #{reportReceiverPhone,jdbcType=VARCHAR}, 
      #{receiverType,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{languageId,jdbcType=INTEGER}, #{reportInfoLock,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoPO" >
    insert into tb_order_report_receiver
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID,
      </if>
      <if test="customerID != null" >
        CustomerID,
      </if>
      <if test="reportNo != null" >
        ReportNo,
      </if>
      <if test="reportHeader != null" >
        ReportHeader,
      </if>
      <if test="reportDeliveredTo != null" >
        ReportDeliveredTo,
      </if>
      <if test="reportReceiverName != null" >
        ReportReceiverName,
      </if>
      <if test="reportReceiverPhone != null" >
        ReportReceiverPhone,
      </if>
      <if test="receiverType != null" >
        ReceiverType,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="reportInfoLock != null" >
        `ReportInfoLock`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="generalOrderID != null" >
        #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="customerID != null" >
        #{customerID,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportHeader != null" >
        #{reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="reportDeliveredTo != null" >
        #{reportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="reportReceiverName != null" >
        #{reportReceiverName,jdbcType=VARCHAR},
      </if>
      <if test="reportReceiverPhone != null" >
        #{reportReceiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverType != null" >
        #{receiverType,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="reportInfoLock != null" >
        #{reportInfoLock,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_order_report_receiver
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_order_report_receiver
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.generalOrderID != null" >
        GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.customerID != null" >
        CustomerID = #{record.customerID,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null" >
        ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportHeader != null" >
        ReportHeader = #{record.reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDeliveredTo != null" >
        ReportDeliveredTo = #{record.reportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportReceiverName != null" >
        ReportReceiverName = #{record.reportReceiverName,jdbcType=VARCHAR},
      </if>
      <if test="record.reportReceiverPhone != null" >
        ReportReceiverPhone = #{record.reportReceiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverType != null" >
        ReceiverType = #{record.receiverType,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.reportInfoLock != null" >
        `ReportInfoLock` = #{record.reportInfoLock,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_order_report_receiver
    set ID = #{record.ID,jdbcType=VARCHAR},
      GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      CustomerID = #{record.customerID,jdbcType=VARCHAR},
      ReportNo = #{record.reportNo,jdbcType=VARCHAR},
      ReportHeader = #{record.reportHeader,jdbcType=VARCHAR},
      ReportDeliveredTo = #{record.reportDeliveredTo,jdbcType=VARCHAR},
      ReportReceiverName = #{record.reportReceiverName,jdbcType=VARCHAR},
      ReportReceiverPhone = #{record.reportReceiverPhone,jdbcType=VARCHAR},
      ReceiverType = #{record.receiverType,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      `ReportInfoLock` = #{record.reportInfoLock,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoPO" >
    update tb_order_report_receiver
    <set >
      <if test="generalOrderID != null" >
        GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="customerID != null" >
        CustomerID = #{customerID,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null" >
        ReportNo = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportHeader != null" >
        ReportHeader = #{reportHeader,jdbcType=VARCHAR},
      </if>
      <if test="reportDeliveredTo != null" >
        ReportDeliveredTo = #{reportDeliveredTo,jdbcType=VARCHAR},
      </if>
      <if test="reportReceiverName != null" >
        ReportReceiverName = #{reportReceiverName,jdbcType=VARCHAR},
      </if>
      <if test="reportReceiverPhone != null" >
        ReportReceiverPhone = #{reportReceiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverType != null" >
        ReceiverType = #{receiverType,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="reportInfoLock != null" >
        `ReportInfoLock` = #{reportInfoLock,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.OrderReportReceiverInfoPO" >
    update tb_order_report_receiver
    set GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      CustomerID = #{customerID,jdbcType=VARCHAR},
      ReportNo = #{reportNo,jdbcType=VARCHAR},
      ReportHeader = #{reportHeader,jdbcType=VARCHAR},
      ReportDeliveredTo = #{reportDeliveredTo,jdbcType=VARCHAR},
      ReportReceiverName = #{reportReceiverName,jdbcType=VARCHAR},
      ReportReceiverPhone = #{reportReceiverPhone,jdbcType=VARCHAR},
      ReceiverType = #{receiverType,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      LanguageId = #{languageId,jdbcType=INTEGER},
      `ReportInfoLock` = #{reportInfoLock,jdbcType=INTEGER}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>