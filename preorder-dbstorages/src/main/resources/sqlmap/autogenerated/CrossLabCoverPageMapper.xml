<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.CrossLabCoverPageMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPagePO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="orderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="transferDate" property="transferDate" jdbcType="TIMESTAMP" />
    <result column="confirmDate" property="confirmDate" jdbcType="TIMESTAMP" />
    <result column="confirmBy" property="confirmBy" jdbcType="VARCHAR" />
    <result column="completeDate" property="completeDate" jdbcType="TIMESTAMP" />
    <result column="completeBy" property="completeBy" jdbcType="VARCHAR" />
    <result column="frontPageRequired" property="frontPageRequired" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, orderNo, `status`, transferDate, confirmDate, confirmBy, completeDate, completeBy, 
    frontPageRequired
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPageExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_order_cross_lab_coverpage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_order_cross_lab_coverpage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_order_cross_lab_coverpage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPageExample" >
    delete from tb_order_cross_lab_coverpage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPagePO" >
    insert into tb_order_cross_lab_coverpage (id, orderNo, `status`, 
      transferDate, confirmDate, confirmBy, 
      completeDate, completeBy, frontPageRequired
      )
    values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{transferDate,jdbcType=TIMESTAMP}, #{confirmDate,jdbcType=TIMESTAMP}, #{confirmBy,jdbcType=VARCHAR}, 
      #{completeDate,jdbcType=TIMESTAMP}, #{completeBy,jdbcType=VARCHAR}, #{frontPageRequired,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPagePO" >
    insert into tb_order_cross_lab_coverpage
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orderNo != null" >
        orderNo,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="transferDate != null" >
        transferDate,
      </if>
      <if test="confirmDate != null" >
        confirmDate,
      </if>
      <if test="confirmBy != null" >
        confirmBy,
      </if>
      <if test="completeDate != null" >
        completeDate,
      </if>
      <if test="completeBy != null" >
        completeBy,
      </if>
      <if test="frontPageRequired != null" >
        frontPageRequired,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="transferDate != null" >
        #{transferDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmDate != null" >
        #{confirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmBy != null" >
        #{confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="completeDate != null" >
        #{completeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="completeBy != null" >
        #{completeBy,jdbcType=VARCHAR},
      </if>
      <if test="frontPageRequired != null" >
        #{frontPageRequired,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPageExample" resultType="java.lang.Integer" >
    select count(*) from tb_order_cross_lab_coverpage
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_order_cross_lab_coverpage
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        orderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.transferDate != null" >
        transferDate = #{record.transferDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmDate != null" >
        confirmDate = #{record.confirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmBy != null" >
        confirmBy = #{record.confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="record.completeDate != null" >
        completeDate = #{record.completeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeBy != null" >
        completeBy = #{record.completeBy,jdbcType=VARCHAR},
      </if>
      <if test="record.frontPageRequired != null" >
        frontPageRequired = #{record.frontPageRequired,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_order_cross_lab_coverpage
    set id = #{record.id,jdbcType=BIGINT},
      orderNo = #{record.orderNo,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      transferDate = #{record.transferDate,jdbcType=TIMESTAMP},
      confirmDate = #{record.confirmDate,jdbcType=TIMESTAMP},
      confirmBy = #{record.confirmBy,jdbcType=VARCHAR},
      completeDate = #{record.completeDate,jdbcType=TIMESTAMP},
      completeBy = #{record.completeBy,jdbcType=VARCHAR},
      frontPageRequired = #{record.frontPageRequired,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPagePO" >
    update tb_order_cross_lab_coverpage
    <set >
      <if test="orderNo != null" >
        orderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="transferDate != null" >
        transferDate = #{transferDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmDate != null" >
        confirmDate = #{confirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmBy != null" >
        confirmBy = #{confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="completeDate != null" >
        completeDate = #{completeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="completeBy != null" >
        completeBy = #{completeBy,jdbcType=VARCHAR},
      </if>
      <if test="frontPageRequired != null" >
        frontPageRequired = #{frontPageRequired,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CrossLabCoverPagePO" >
    update tb_order_cross_lab_coverpage
    set orderNo = #{orderNo,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      transferDate = #{transferDate,jdbcType=TIMESTAMP},
      confirmDate = #{confirmDate,jdbcType=TIMESTAMP},
      confirmBy = #{confirmBy,jdbcType=VARCHAR},
      completeDate = #{completeDate,jdbcType=TIMESTAMP},
      completeBy = #{completeBy,jdbcType=VARCHAR},
      frontPageRequired = #{frontPageRequired,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>