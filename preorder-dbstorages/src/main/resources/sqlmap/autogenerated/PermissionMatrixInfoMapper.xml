<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.PermissionMatrixInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="action_code" property="actionCode" jdbcType="VARCHAR" />
    <result column="action_name" property="actionName" jdbcType="VARCHAR" />
    <result column="object" property="object" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="value" property="value" jdbcType="VARCHAR" />
    <result column="label" property="label" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, action_code, action_name, `object`, `type`, `value`, `label`, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_permission_matrix
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from tb_permission_matrix
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_permission_matrix
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoExample" >
    delete from tb_permission_matrix
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoPO" >
    insert into tb_permission_matrix (id, action_code, action_name,
      `object`, `type`, `value`,
      `label`, remark)
    values (#{id,jdbcType=VARCHAR}, #{actionCode,jdbcType=VARCHAR}, #{actionName,jdbcType=VARCHAR},
      #{object,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR},
      #{label,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoPO" >
    insert into tb_permission_matrix
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="actionCode != null" >
        action_code,
      </if>
      <if test="actionName != null" >
        action_name,
      </if>
      <if test="object != null" >
        `object`,
      </if>
      <if test="type != null" >
        `type`,
      </if>
      <if test="value != null" >
        `value`,
      </if>
      <if test="label != null" >
        `label`,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="actionCode != null" >
        #{actionCode,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null" >
        #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="object != null" >
        #{object,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="label != null" >
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_permission_matrix
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_permission_matrix
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.actionCode != null" >
        action_code = #{record.actionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.actionName != null" >
        action_name = #{record.actionName,jdbcType=VARCHAR},
      </if>
      <if test="record.object != null" >
        `object` = #{record.object,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null" >
        `value` = #{record.value,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null" >
        `label` = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_permission_matrix
    set id = #{record.id,jdbcType=VARCHAR},
      action_code = #{record.actionCode,jdbcType=VARCHAR},
      action_name = #{record.actionName,jdbcType=VARCHAR},
      `object` = #{record.object,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      `value` = #{record.value,jdbcType=VARCHAR},
      `label` = #{record.label,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoPO" >
    update tb_permission_matrix
    <set >
      <if test="actionCode != null" >
        action_code = #{actionCode,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null" >
        action_name = #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="object != null" >
        `object` = #{object,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        `value` = #{value,jdbcType=VARCHAR},
      </if>
      <if test="label != null" >
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.PermissionMatrixInfoPO" >
    update tb_permission_matrix
    set action_code = #{actionCode,jdbcType=VARCHAR},
      action_name = #{actionName,jdbcType=VARCHAR},
      `object` = #{object,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `value` = #{value,jdbcType=VARCHAR},
      `label` = #{label,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>