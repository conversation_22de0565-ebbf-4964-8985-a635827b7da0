<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.BossOrderTaxInvoiceInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="tax_invoice_no" property="taxInvoiceNo" jdbcType="VARCHAR" />
    <result column="tax_invoice_code" property="taxInvoiceCode" jdbcType="VARCHAR" />
    <result column="tax_invoice_date" property="taxInvoiceDate" jdbcType="TIMESTAMP" />
    <result column="tax_invoice_amount" property="taxInvoiceAmount" jdbcType="DECIMAL" />
    <result column="tax_invoice_delivered_awb_no" property="taxInvoiceDeliveredAwbNo" jdbcType="VARCHAR" />
    <result column="tax_invoice_delivered_by" property="taxInvoiceDeliveredBy" jdbcType="VARCHAR" />
    <result column="tax_invoice_delivered_date" property="taxInvoiceDeliveredDate" jdbcType="TIMESTAMP" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, invoice_no, tax_invoice_no, tax_invoice_code, tax_invoice_date, tax_invoice_amount, 
    tax_invoice_delivered_awb_no, tax_invoice_delivered_by, tax_invoice_delivered_date, 
    created_date, created_by, modified_date, modified_by, active_indicator
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_boss_order_tax_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_boss_order_tax_invoice
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_boss_order_tax_invoice
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoExample" >
    delete from tb_boss_order_tax_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoPO" >
    insert into tb_boss_order_tax_invoice (id, invoice_no, tax_invoice_no, 
      tax_invoice_code, tax_invoice_date, tax_invoice_amount, 
      tax_invoice_delivered_awb_no, tax_invoice_delivered_by, 
      tax_invoice_delivered_date, created_date, 
      created_by, modified_date, modified_by, 
      active_indicator)
    values (#{id,jdbcType=VARCHAR}, #{invoiceNo,jdbcType=VARCHAR}, #{taxInvoiceNo,jdbcType=VARCHAR}, 
      #{taxInvoiceCode,jdbcType=VARCHAR}, #{taxInvoiceDate,jdbcType=TIMESTAMP}, #{taxInvoiceAmount,jdbcType=DECIMAL}, 
      #{taxInvoiceDeliveredAwbNo,jdbcType=VARCHAR}, #{taxInvoiceDeliveredBy,jdbcType=VARCHAR}, 
      #{taxInvoiceDeliveredDate,jdbcType=TIMESTAMP}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{activeIndicator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoPO" >
    insert into tb_boss_order_tax_invoice
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="invoiceNo != null" >
        invoice_no,
      </if>
      <if test="taxInvoiceNo != null" >
        tax_invoice_no,
      </if>
      <if test="taxInvoiceCode != null" >
        tax_invoice_code,
      </if>
      <if test="taxInvoiceDate != null" >
        tax_invoice_date,
      </if>
      <if test="taxInvoiceAmount != null" >
        tax_invoice_amount,
      </if>
      <if test="taxInvoiceDeliveredAwbNo != null" >
        tax_invoice_delivered_awb_no,
      </if>
      <if test="taxInvoiceDeliveredBy != null" >
        tax_invoice_delivered_by,
      </if>
      <if test="taxInvoiceDeliveredDate != null" >
        tax_invoice_delivered_date,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceNo != null" >
        #{taxInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceCode != null" >
        #{taxInvoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceDate != null" >
        #{taxInvoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taxInvoiceAmount != null" >
        #{taxInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxInvoiceDeliveredAwbNo != null" >
        #{taxInvoiceDeliveredAwbNo,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceDeliveredBy != null" >
        #{taxInvoiceDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceDeliveredDate != null" >
        #{taxInvoiceDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_boss_order_tax_invoice
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_boss_order_tax_invoice
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null" >
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInvoiceNo != null" >
        tax_invoice_no = #{record.taxInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInvoiceCode != null" >
        tax_invoice_code = #{record.taxInvoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInvoiceDate != null" >
        tax_invoice_date = #{record.taxInvoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taxInvoiceAmount != null" >
        tax_invoice_amount = #{record.taxInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxInvoiceDeliveredAwbNo != null" >
        tax_invoice_delivered_awb_no = #{record.taxInvoiceDeliveredAwbNo,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInvoiceDeliveredBy != null" >
        tax_invoice_delivered_by = #{record.taxInvoiceDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInvoiceDeliveredDate != null" >
        tax_invoice_delivered_date = #{record.taxInvoiceDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_boss_order_tax_invoice
    set id = #{record.id,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      tax_invoice_no = #{record.taxInvoiceNo,jdbcType=VARCHAR},
      tax_invoice_code = #{record.taxInvoiceCode,jdbcType=VARCHAR},
      tax_invoice_date = #{record.taxInvoiceDate,jdbcType=TIMESTAMP},
      tax_invoice_amount = #{record.taxInvoiceAmount,jdbcType=DECIMAL},
      tax_invoice_delivered_awb_no = #{record.taxInvoiceDeliveredAwbNo,jdbcType=VARCHAR},
      tax_invoice_delivered_by = #{record.taxInvoiceDeliveredBy,jdbcType=VARCHAR},
      tax_invoice_delivered_date = #{record.taxInvoiceDeliveredDate,jdbcType=TIMESTAMP},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoPO" >
    update tb_boss_order_tax_invoice
    <set >
      <if test="invoiceNo != null" >
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceNo != null" >
        tax_invoice_no = #{taxInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceCode != null" >
        tax_invoice_code = #{taxInvoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceDate != null" >
        tax_invoice_date = #{taxInvoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taxInvoiceAmount != null" >
        tax_invoice_amount = #{taxInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxInvoiceDeliveredAwbNo != null" >
        tax_invoice_delivered_awb_no = #{taxInvoiceDeliveredAwbNo,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceDeliveredBy != null" >
        tax_invoice_delivered_by = #{taxInvoiceDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="taxInvoiceDeliveredDate != null" >
        tax_invoice_delivered_date = #{taxInvoiceDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.BossOrderTaxInvoiceInfoPO" >
    update tb_boss_order_tax_invoice
    set invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      tax_invoice_no = #{taxInvoiceNo,jdbcType=VARCHAR},
      tax_invoice_code = #{taxInvoiceCode,jdbcType=VARCHAR},
      tax_invoice_date = #{taxInvoiceDate,jdbcType=TIMESTAMP},
      tax_invoice_amount = #{taxInvoiceAmount,jdbcType=DECIMAL},
      tax_invoice_delivered_awb_no = #{taxInvoiceDeliveredAwbNo,jdbcType=VARCHAR},
      tax_invoice_delivered_by = #{taxInvoiceDeliveredBy,jdbcType=VARCHAR},
      tax_invoice_delivered_date = #{taxInvoiceDeliveredDate,jdbcType=TIMESTAMP},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>