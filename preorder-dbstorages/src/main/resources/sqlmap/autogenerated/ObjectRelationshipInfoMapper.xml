<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.ObjectRelationshipInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="root_no" property="rootNo" jdbcType="VARCHAR" />
    <result column="primary_type" property="primaryType" jdbcType="VARCHAR" />
    <result column="primary_id" property="primaryId" jdbcType="VARCHAR" />
    <result column="foreign_type" property="foreignType" jdbcType="VARCHAR" />
    <result column="foreign_id" property="foreignId" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="BIT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, root_no, primary_type, primary_id, foreign_type, foreign_id, active_indicator, 
    created_by, created_date, modified_by, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_object_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_object_relationship
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_object_relationship
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoExample" >
    delete from tb_object_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoPO" >
    insert into tb_object_relationship (id, root_no, primary_type, 
      primary_id, foreign_type, foreign_id, 
      active_indicator, created_by, created_date, 
      modified_by, modified_date)
    values (#{id,jdbcType=VARCHAR}, #{rootNo,jdbcType=VARCHAR}, #{primaryType,jdbcType=VARCHAR}, 
      #{primaryId,jdbcType=VARCHAR}, #{foreignType,jdbcType=VARCHAR}, #{foreignId,jdbcType=VARCHAR}, 
      #{activeIndicator,jdbcType=BIT}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoPO" >
    insert into tb_object_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="rootNo != null" >
        root_no,
      </if>
      <if test="primaryType != null" >
        primary_type,
      </if>
      <if test="primaryId != null" >
        primary_id,
      </if>
      <if test="foreignType != null" >
        foreign_type,
      </if>
      <if test="foreignId != null" >
        foreign_id,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="rootNo != null" >
        #{rootNo,jdbcType=VARCHAR},
      </if>
      <if test="primaryType != null" >
        #{primaryType,jdbcType=VARCHAR},
      </if>
      <if test="primaryId != null" >
        #{primaryId,jdbcType=VARCHAR},
      </if>
      <if test="foreignType != null" >
        #{foreignType,jdbcType=VARCHAR},
      </if>
      <if test="foreignId != null" >
        #{foreignId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_object_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_object_relationship
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.rootNo != null" >
        root_no = #{record.rootNo,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryType != null" >
        primary_type = #{record.primaryType,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryId != null" >
        primary_id = #{record.primaryId,jdbcType=VARCHAR},
      </if>
      <if test="record.foreignType != null" >
        foreign_type = #{record.foreignType,jdbcType=VARCHAR},
      </if>
      <if test="record.foreignId != null" >
        foreign_id = #{record.foreignId,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_object_relationship
    set id = #{record.id,jdbcType=VARCHAR},
      root_no = #{record.rootNo,jdbcType=VARCHAR},
      primary_type = #{record.primaryType,jdbcType=VARCHAR},
      primary_id = #{record.primaryId,jdbcType=VARCHAR},
      foreign_type = #{record.foreignType,jdbcType=VARCHAR},
      foreign_id = #{record.foreignId,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=BIT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoPO" >
    update tb_object_relationship
    <set >
      <if test="rootNo != null" >
        root_no = #{rootNo,jdbcType=VARCHAR},
      </if>
      <if test="primaryType != null" >
        primary_type = #{primaryType,jdbcType=VARCHAR},
      </if>
      <if test="primaryId != null" >
        primary_id = #{primaryId,jdbcType=VARCHAR},
      </if>
      <if test="foreignType != null" >
        foreign_type = #{foreignType,jdbcType=VARCHAR},
      </if>
      <if test="foreignId != null" >
        foreign_id = #{foreignId,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.ObjectRelationshipInfoPO" >
    update tb_object_relationship
    set root_no = #{rootNo,jdbcType=VARCHAR},
      primary_type = #{primaryType,jdbcType=VARCHAR},
      primary_id = #{primaryId,jdbcType=VARCHAR},
      foreign_type = #{foreignType,jdbcType=VARCHAR},
      foreign_id = #{foreignId,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>