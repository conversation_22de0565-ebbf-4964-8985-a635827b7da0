<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.preorder.dbstorages.mybatis.mapper.CareLabelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="ProductInstanceID" property="productInstanceID" jdbcType="VARCHAR" />
    <result column="CareInstruction" property="careInstruction" jdbcType="VARCHAR" />
    <result column="RadioType" property="radioType" jdbcType="TINYINT" />
    <result column="SelectCountry" property="selectCountry" jdbcType="VARCHAR" />
    <result column="SelectImgIDs" property="selectImgIDs" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="GeneralOrderID" property="generalOrderID" jdbcType="VARCHAR" />
    <result column="CareLabelSeq" property="careLabelSeq" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, ProductInstanceID, CareInstruction, RadioType, SelectCountry, SelectImgIDs, ActiveIndicator, 
    CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, GeneralOrderID, CareLabelSeq
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_carelabel_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_carelabel_instance
    where ID = #{ID,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_carelabel_instance
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoExample" >
    delete from tb_carelabel_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoPO" >
    insert into tb_carelabel_instance (ID, ProductInstanceID, CareInstruction, 
      RadioType, SelectCountry, SelectImgIDs, 
      ActiveIndicator, CreatedBy, CreatedDate, 
      ModifiedBy, ModifiedDate, GeneralOrderID, 
      CareLabelSeq)
    values (#{ID,jdbcType=VARCHAR}, #{productInstanceID,jdbcType=VARCHAR}, #{careInstruction,jdbcType=VARCHAR}, 
      #{radioType,jdbcType=TINYINT}, #{selectCountry,jdbcType=VARCHAR}, #{selectImgIDs,jdbcType=VARCHAR}, 
      #{activeIndicator,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{generalOrderID,jdbcType=VARCHAR}, 
      #{careLabelSeq,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoPO" >
    insert into tb_carelabel_instance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="productInstanceID != null" >
        ProductInstanceID,
      </if>
      <if test="careInstruction != null" >
        CareInstruction,
      </if>
      <if test="radioType != null" >
        RadioType,
      </if>
      <if test="selectCountry != null" >
        SelectCountry,
      </if>
      <if test="selectImgIDs != null" >
        SelectImgIDs,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID,
      </if>
      <if test="careLabelSeq != null" >
        CareLabelSeq,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="productInstanceID != null" >
        #{productInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="careInstruction != null" >
        #{careInstruction,jdbcType=VARCHAR},
      </if>
      <if test="radioType != null" >
        #{radioType,jdbcType=TINYINT},
      </if>
      <if test="selectCountry != null" >
        #{selectCountry,jdbcType=VARCHAR},
      </if>
      <if test="selectImgIDs != null" >
        #{selectImgIDs,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generalOrderID != null" >
        #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="careLabelSeq != null" >
        #{careLabelSeq,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_carelabel_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_carelabel_instance
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.productInstanceID != null" >
        ProductInstanceID = #{record.productInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="record.careInstruction != null" >
        CareInstruction = #{record.careInstruction,jdbcType=VARCHAR},
      </if>
      <if test="record.radioType != null" >
        RadioType = #{record.radioType,jdbcType=TINYINT},
      </if>
      <if test="record.selectCountry != null" >
        SelectCountry = #{record.selectCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.selectImgIDs != null" >
        SelectImgIDs = #{record.selectImgIDs,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.generalOrderID != null" >
        GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="record.careLabelSeq != null" >
        CareLabelSeq = #{record.careLabelSeq,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_carelabel_instance
    set ID = #{record.ID,jdbcType=VARCHAR},
      ProductInstanceID = #{record.productInstanceID,jdbcType=VARCHAR},
      CareInstruction = #{record.careInstruction,jdbcType=VARCHAR},
      RadioType = #{record.radioType,jdbcType=TINYINT},
      SelectCountry = #{record.selectCountry,jdbcType=VARCHAR},
      SelectImgIDs = #{record.selectImgIDs,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      GeneralOrderID = #{record.generalOrderID,jdbcType=VARCHAR},
      CareLabelSeq = #{record.careLabelSeq,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoPO" >
    update tb_carelabel_instance
    <set >
      <if test="productInstanceID != null" >
        ProductInstanceID = #{productInstanceID,jdbcType=VARCHAR},
      </if>
      <if test="careInstruction != null" >
        CareInstruction = #{careInstruction,jdbcType=VARCHAR},
      </if>
      <if test="radioType != null" >
        RadioType = #{radioType,jdbcType=TINYINT},
      </if>
      <if test="selectCountry != null" >
        SelectCountry = #{selectCountry,jdbcType=VARCHAR},
      </if>
      <if test="selectImgIDs != null" >
        SelectImgIDs = #{selectImgIDs,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generalOrderID != null" >
        GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      </if>
      <if test="careLabelSeq != null" >
        CareLabelSeq = #{careLabelSeq,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.preorder.dbstorages.mybatis.model.CareLabelInfoPO" >
    update tb_carelabel_instance
    set ProductInstanceID = #{productInstanceID,jdbcType=VARCHAR},
      CareInstruction = #{careInstruction,jdbcType=VARCHAR},
      RadioType = #{radioType,jdbcType=TINYINT},
      SelectCountry = #{selectCountry,jdbcType=VARCHAR},
      SelectImgIDs = #{selectImgIDs,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=TINYINT},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      GeneralOrderID = #{generalOrderID,jdbcType=VARCHAR},
      CareLabelSeq = #{careLabelSeq,jdbcType=INTEGER}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
</mapper>