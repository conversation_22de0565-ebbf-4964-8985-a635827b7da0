package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@XmlType(name = "ArrayOfTWOADCaseTestItem")
public class ArrayOfTWOADCaseTestItem implements Serializable {
    private List<TWOADCaseTestItem> twoadCaseTestItem;

    @XmlElement(name = "TWOADCaseTestItem", nillable = true)
    public List<TWOADCaseTestItem> getTwoadCaseTestItem() {
        if (twoadCaseTestItem == null) {
            twoadCaseTestItem = new ArrayList<TWOADCaseTestItem>();
        }
        return this.twoadCaseTestItem;
    }

    public void setTwoadCaseTestItem(List<TWOADCaseTestItem> twoadCaseTestItem) {
        this.twoadCaseTestItem = twoadCaseTestItem;
    }
}