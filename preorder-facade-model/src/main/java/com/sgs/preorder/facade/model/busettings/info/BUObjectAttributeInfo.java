package com.sgs.preorder.facade.model.busettings.info;

import com.sgs.framework.core.base.BaseProductLine;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "Bu Setting配置对象", description = "Bu Setting配置对象")
public class BUObjectAttributeInfo extends BaseProductLine {

    private static final long serialVersionUID = 8708790858866234181L;

    /**
     * Object Id
     */
    @ApiModelProperty(value = "编码")
    private String id;

    /**
     * parent_id VARCHAR(50)<br>
     *
     */
    private String parentId;

    /**
     * attribute_code VARCHAR(100)<br>
     *
     */
    private String attributeId;
    /**
     * attribute_code VARCHAR(100)<br>
     *
     */
    private String attributeCode;

    /**
     * attribute_name VARCHAR(100)<br>
     *
     */
    private String attributeName;

    /**
     * type SMALLINT(5)<br>
     * 1:section;2:field;3:object;
     */
    private Short type;

    /**
     * reference_object VARCHAR(100)<br>
     *
     */
    private String referenceObject;

    /**
     * sort INTEGER(10)<br>
     *
     */
    private Integer sort;

    /**
     * is_display SMALLINT(5)<br>
     *
     */
    private Integer isDisplay;

    private String isDisplayCondition;

    /**
     * is_required SMALLINT(5)<br>
     *
     */
    private Integer isRequired;

    private String isRequiredCondition;
    /**
     * regular VARCHAR(200)<br>
     *
     */
    private String regular;

    /**
     * default_value VARCHAR(200)<br>
     *
     */
    private String defaultValue;

    private String function;

    private String defaultValueModel;

    /**
     * is_expand SMALLINT(5)<br>
     *
     */
    private Integer isExpand;

    private String component;

    /**
     * is_display SMALLINT(5)<br>
     *
     */
    private Integer isFixed;

    /**
     * is_expand SMALLINT(5)<br>
     *
     */
    private String sameAsOrder;

    private Map<String,String> buParam;

    private String service;

    private Integer isGeneral;

    private List<BUObjectAttributeInfo> subAttributes;

    private List<BUObjectAttributeInfo> subSettingList;
}
