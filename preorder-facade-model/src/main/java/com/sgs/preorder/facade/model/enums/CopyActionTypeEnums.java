package com.sgs.preorder.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName : CopyActionTypeEnums
 * @Description ://四种copy的动作枚举
 * @Authoer : vincent.zhi
 * @Date : 2019/8/12 10:07
 **/
public enum CopyActionTypeEnums {

	/**
	 * copy 全部
	*/
	CopyAll,
	/**
	 * 根据section copy
	 * */
	CopyBySection,
	/**
	 * AmendReport
	 * */
	AmendReport,
	/**
	 * InternalSub
	 */
	InternalSub,
	
	SplitReport,
	/**
	 * copy 全部 FOR HL
	 */
	CopyAllForGPO;



	public static boolean contains(String typeName){
		if(StringUtils.isBlank(typeName)){
			return false;
		}
		CopyActionTypeEnums[] values = CopyActionTypeEnums.values();
		boolean cont = false;
		for (CopyActionTypeEnums typeEnums : values) {
			if(	typeEnums.name().equalsIgnoreCase(typeName)){
				cont = true;
			};
		}
		return cont;
	}

	public static CopyActionTypeEnums get(String typeName){
		CopyActionTypeEnums res  = null;
		if(!contains(typeName)){
			return res;
		}
		CopyActionTypeEnums[] values = CopyActionTypeEnums.values();
		for (CopyActionTypeEnums typeEnums : values) {
			if(	typeEnums.name().equalsIgnoreCase(typeName)){
				res = typeEnums;
			};
		}
		return res;
	}
}
