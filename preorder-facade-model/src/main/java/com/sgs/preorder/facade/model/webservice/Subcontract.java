package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.util.Date;

public class Subcontract implements Serializable {

	private String CaseNo;
	private String SubcontractNo;
	private String ReportNo;
	private String SubcontractTo;
	private Date SubcontractDate;
	private Date SubcontractExpectedDueDate;
	private String SubcontractTypeName;
	private String SubcontractStatusName;

	@XmlElement(name = "CaseNo")
	public String getCaseNo() {
		return CaseNo;
	}

	public void setCaseNo(String caseNo) {
		CaseNo = caseNo;
	}

	@XmlElement(name = "SubcontractNo")
	public String getSubcontractNo() {
		return SubcontractNo;
	}

	public void setSubcontractNo(String subcontractNo) {
		SubcontractNo = subcontractNo;
	}

	@XmlElement(name = "ReportNo")
	public String getReportNo() {
		return ReportNo;
	}

	public void setReportNo(String reportNo) {
		ReportNo = reportNo;
	}

	@XmlElement(name = "SubcontractTo")
	public String getSubcontractTo() {
		return SubcontractTo;
	}

	public void setSubcontractTo(String subcontractTo) {
		SubcontractTo = subcontractTo;
	}

	@XmlElement(name = "SubcontractDate")
	public Date getSubcontractDate() {
		return SubcontractDate;
	}

	public void setSubcontractDate(Date subcontractDate) {
		SubcontractDate = subcontractDate;
	}

	@XmlElement(name = "SubcontractExpectedDueDate")
	public Date getSubcontractExpectedDueDate() {
		return SubcontractExpectedDueDate;
	}

	public void setSubcontractExpectedDueDate(Date subcontractExpectedDueDate) {
		SubcontractExpectedDueDate = subcontractExpectedDueDate;
	}

	@XmlElement(name = "SubcontractTypeName")
	public String getSubcontractTypeName() {
		return SubcontractTypeName;
	}

	public void setSubcontractTypeName(String subcontractTypeName) {
		SubcontractTypeName = subcontractTypeName;
	}

	@XmlElement(name = "SubcontractStatusName")
	public String getSubcontractStatusName() {
		return SubcontractStatusName;
	}

	public void setSubcontractStatusName(String subcontractStatusName) {
		SubcontractStatusName = subcontractStatusName;
	}
}
