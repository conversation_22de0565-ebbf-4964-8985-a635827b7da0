package com.sgs.preorder.facade.model.busettings.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel(value = "Bu Object Template配置对象", description = "Bu Object Template配置对象")
public class BuObjectTemplateReq extends BaseRequest {

    @ApiModelProperty(value = "模板编码")
    private String id;

    @ApiModelProperty(value = "对象编码")
    @Deprecated
    private String object;

    @ApiModelProperty(value = "对象编码")
    private String objectId;

    @ApiModelProperty(value = "相关参数")
    private Map<String,Object> params;

    private boolean showALLAttribute;

}
