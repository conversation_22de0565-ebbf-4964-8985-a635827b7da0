package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@XmlType(name = "ArrayOfTWOADJobInfo")
public class ArrayOfTWOADJobInfo implements Serializable {

    private List<TWOADJobInfo> twoadJobInfo;

    @XmlElement(name = "TWOADJobInfo", nillable = true)
    public List<TWOADJobInfo> getTwoadJobInfo() {
        if (twoadJobInfo == null) {
            twoadJobInfo = new ArrayList<TWOADJobInfo>();
        }
        return this.twoadJobInfo;
    }

    public void setTwoadJobInfo(List<TWOADJobInfo> twoadJobInfo) {
        this.twoadJobInfo = twoadJobInfo;
    }
}
