package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TWOADCaseTestItem")
public class TWOADCaseTestItem {
    private String TestItemName;
    private String TestMethodName;
    private String LabSectionName;
    private String TestItemID;
    private Integer TestMethodID;

    @XmlElement(name = "TestItemName")
    public String getTestItemName() {
        return TestItemName;
    }

    public void setTestItemName(String testItemName) {
        TestItemName = testItemName;
    }

    @XmlElement(name = "TestMethodName")
    public String getTestMethodName() {
        return TestMethodName;
    }

    public void setTestMethodName(String testMethodName) {
        TestMethodName = testMethodName;
    }

    @XmlElement(name = "LabSectionName")
    public String getLabSectionName() {
        return LabSectionName;
    }

    public void setLabSectionName(String labSectionName) {
        LabSectionName = labSectionName;
    }

    @XmlElement(name = "TestItemID")
    public String getTestItemID() {
        return TestItemID;
    }

    public void setTestItemID(String testItemID) {
        TestItemID = testItemID;
    }

    @XmlElement(name = "TestMethodID")
    public Integer getTestMethodID() {
        return TestMethodID;
    }

    public void setTestMethodID(Integer testMethodID) {
        TestMethodID = testMethodID;
    }
}
