package com.sgs.preorder.facade.model.comparator;

import com.sgs.preorder.facade.model.info.ProductSampleInfo;

import java.util.Comparator;

public class ProductSampleComparator implements Comparator<ProductSampleInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    public ProductSampleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(ProductSampleInfo o1, ProductSampleInfo o2) {
        /*if(isAsc){
            return Integer.compare(o1.getSortIndex(),o2.getSortIndex());
        }
        return Integer.compare(o2.getSortIndex(),o1.getSortIndex());*/
        int index = o1.getProductNo() - o2.getProductNo();
        if (index < 0) {
            return isAsc ? -1 : 1;
        } else if (index > 0) {
            return isAsc ? 1 : -1;
        } else {
            return o1.getId().compareTo(o2.getId());
        }
    }
}
