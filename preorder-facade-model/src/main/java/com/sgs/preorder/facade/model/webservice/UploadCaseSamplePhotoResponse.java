package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 *
 */
@XmlType(name = "UploadCaseSamplePhotoResponse")
public class UploadCaseSamplePhotoResponse implements Serializable {

	private int uploadCaseSamplePhotoResult;
	protected String errMsg;

	@XmlElement(name = "UploadCaseSamplePhotoResult")
	public int getUploadCaseSamplePhotoResult() {
		return uploadCaseSamplePhotoResult;
	}

	public void setUploadCaseSamplePhotoResult(int uploadCaseSamplePhotoResult) {
		this.uploadCaseSamplePhotoResult = uploadCaseSamplePhotoResult;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}
}
