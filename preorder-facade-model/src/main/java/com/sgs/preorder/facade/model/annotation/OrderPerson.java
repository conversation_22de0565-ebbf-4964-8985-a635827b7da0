package com.sgs.preorder.facade.model.annotation;


import com.sgs.preorder.facade.model.enums.OrderPersonType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.ANNOTATION_TYPE, ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface OrderPerson {
    OrderPersonType value() default OrderPersonType.NONE;
}
