package com.sgs.preorder.facade.model.ordercopy;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.preorder.facade.model.common.StringUtil;

public class OriginalSampleCopyInfo {
    /**
     *
     */
    private String sampleId;
    /**
     *
     */
    @JsonIgnore
    private String oldRefSampleId;
    /**
     *
     */
    private String refSampleId;
    /**
     *
     */
    private String sampleNo;
    /**
     *
     */
    private String description;
    /**
     *
     */
    private String color;
    /**
     *
     */
    private String endUse;
    /**
     *
     */
    private String composition;
    /**
     *
     */
    @JsonIgnore
    private int languageId;

    public String getSampleId() {
        return sampleId;
    }

    public void setSampleId(String sampleId) {
        this.sampleId = sampleId;
    }

    public String getOldRefSampleId() {
        return oldRefSampleId;
    }

    public void setOldRefSampleId(String oldRefSampleId) {
        this.oldRefSampleId = oldRefSampleId;
    }

    public String getRefSampleId() {
        return refSampleId;
    }

    public void setRefSampleId(String refSampleId) {
        this.refSampleId = refSampleId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getEndUse() {
        return endUse;
    }

    public void setEndUse(String endUse) {
        this.endUse = endUse;
    }

    public String getComposition() {
        return composition;
    }

    public void setComposition(String composition) {
        this.composition = composition;
    }

    public int getLanguageId() {
        return languageId;
    }

    public void setLanguageId(int languageId) {
        this.languageId = languageId;
    }

    /**
     *
     * @return
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int hashCode = 1;

        hashCode = prime * hashCode + (StringUtil.hashCode(sampleId));
        hashCode = prime * hashCode + (StringUtil.hashCode(oldRefSampleId));
        hashCode = prime * hashCode + (StringUtil.hashCode(refSampleId));
        hashCode = prime * hashCode + (StringUtil.hashCode(sampleNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(description));
        hashCode = prime * hashCode + (StringUtil.hashCode(color));
        hashCode = prime * hashCode + (StringUtil.hashCode(endUse));
        hashCode = prime * hashCode + (StringUtil.hashCode(composition));

        return hashCode;
    }

    @Override
    public String toString() {
        return "SampleCopyInfo [sampleId=" + sampleId + ", oldRefSampleId=" + oldRefSampleId+ ", refSampleId=" + refSampleId
                + ", sampleNo=" + sampleNo + ", description=" + description
                + ", color=" + color + ", endUse=" + endUse + ", composition="
                + composition + "]";
    }
}
