package com.sgs.preorder.facade.model.info.user;

import com.sgs.framework.core.base.BaseProductLine;

public class UserLabBuInfo extends BaseProductLine {
    /**
     *
     */
    private Integer labId;
    /**
     *
     */
    private String labCode;
    /**
     *
     */
    private String otherCode;
    /**
     *
     */
    private String labName;
    /**
     *
     */
    private String labAddress;

    /**
     *
     */
    private Integer locationId;
    /**
     *
     */
    private String locationCode;
    /**
     *
     */
    private String locationName;

    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private String productLineName;

    /**
     *
     */
    private Long organizationID;
    /**
     *
     */
    private String organizationName;

    /**
     *
     */
    private String legalEntityCode;
    /**
     *
     */
    private String legalEntityName;

    /**
     *
     */
    private String productTypeCode;
    /**
     *
     */
    private String[] postfix;

    /**
     *
     */
    private String countryCode;

    /**
     *
     */
    private String teamCode;

    /**
     *
     */
    private String teamName;

    /**
     *productLineCode缩写,生成单号使用
     */
    private String shortCode;

    public String getOtherCode() {
        return otherCode;
    }

    public void setOtherCode(String otherCode) {
        this.otherCode = otherCode;
    }

    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    public Integer getLabId() {
        return labId;
    }

    public void setLabId(Integer labId) {
        this.labId = labId;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public String getLabAddress() {
        return labAddress;
    }

    public void setLabAddress(String labAddress) {
        this.labAddress = labAddress;
    }

    public Integer getLocationId() {
        return locationId;
    }

    public void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public String getProductLineName() {
        return productLineName;
    }

    public void setProductLineName(String productLineName) {
        this.productLineName = productLineName;
    }

    public Long getOrganizationID() {
        return organizationID;
    }

    public void setOrganizationID(Long organizationID) {
        this.organizationID = organizationID;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getLegalEntityCode() {
        return legalEntityCode;
    }

    public void setLegalEntityCode(String legalEntityCode) {
        this.legalEntityCode = legalEntityCode;
    }

    public String getLegalEntityName() {
        return legalEntityName;
    }

    public void setLegalEntityName(String legalEntityName) {
        this.legalEntityName = legalEntityName;
    }

    public String getProductTypeCode() {
        return productTypeCode;
    }

    public void setProductTypeCode(String productTypeCode) {
        this.productTypeCode = productTypeCode;
    }

    public String[] getPostfix() {
        return postfix;
    }

    public void setPostfix(String[] postfix) {
        this.postfix = postfix;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
}
