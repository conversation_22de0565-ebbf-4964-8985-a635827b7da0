package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@XmlType(name = "ArrayOfTWOADSubcontractInfo")
public class ArrayOfTWOADSubcontractInfo implements Serializable {

    private List<TWOADSubcontractInfo> twoadSubcontractInfo;

    @XmlElement(name = "TWOADSubcontractInfo", nillable = true)
    public List<TWOADSubcontractInfo> getTwoadSubcontractInfo() {
        if (twoadSubcontractInfo == null) {
            twoadSubcontractInfo = new ArrayList<TWOADSubcontractInfo>();
        }
        return this.twoadSubcontractInfo;
    }

    public void setTwoadSubcontractInfo(List<TWOADSubcontractInfo> twoadSubcontractInfo) {
        this.twoadSubcontractInfo = twoadSubcontractInfo;
    }
}