package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlType(name = "UploadCaseSamplePhoto")
public class UploadCaseSamplePhoto implements Serializable{

	private String userID;
	
	private String fileName;
	
	private Integer fileType;// samplePhoto = 1504 applicationPhoto 1510
	
	private byte[] imageData;

	public String getUserID() {
		return userID;
	}

	public void setUserID(String userID) {
		this.userID = userID;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public Integer getFileType() {
		return fileType;
	}

	public void setFileType(Integer fileType) {
		this.fileType = fileType;
	}

	public byte[] getImageData() {
		return imageData;
	}

	public void setImageData(byte[] imageData) {
		this.imageData = imageData;
	}
}
