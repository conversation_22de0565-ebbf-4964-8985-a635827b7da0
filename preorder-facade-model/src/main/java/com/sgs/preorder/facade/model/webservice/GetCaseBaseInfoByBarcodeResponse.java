package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.Date;

/**
 *
 */
@XmlType(name = "TWOADCaseInfo")
public class GetCaseBaseInfoByBarcodeResponse implements Serializable {

	private ArrayOfTWOADJobInfo jobs;
	private ArrayOfTWOADSubcontractInfo subcontracts;
	private ArrayOfTWOADCaseTestItem caseTestItems;
	private String caseNo;
	private String caseStatusName;
	private String applicantName;
	private String applicantCustomerNo;
	private String applicantCustomerEmail;
	private String applicantCustomerContactPerson;
	private String buyerName;
	private String buyerCustomerNo;
	private String payerName;
	private String payerCustomerNo;
	private String supplierName;
	private String supplierCustomerNo;
	private String serviceTypeName;
	private Date caseExpectDueDate;
	private Date caseCreateDate;
	private String caseCreatedByName;
	private Date caseActualStartDate;
	private String casePONo;
	private String caseStyleNo;
	private String caseResponsibleTeamName;
	private String responsibleCSName;
	private String caseRemark;
	private Integer sampleQuantity;
	private boolean isPhotoRequired;
	private String caseDelayReason;
	private String applicantReportRemark;
	private String applicantPDRemark;
	private String applicantCSRemark;
	private String applicantSampleCardRemark;
	private String applicantInvoiceRemark;
	private String payerReportRemark;
	private String payerPDRemark;
	private String payerCSRemark;
	private String payerSampleCardRemark;
	private String payerInvoiceRemark;
	private String buyerReportRemark;
	private String buyerPDRemark;
	private String buyerCSRemark;
	private String buyerSampleCardRemark;
	private String buyerInvoiceRemark;
	private Integer bUID;
	private String bUCode;
	private Integer sBUID;
	private String sBUCode;
	private Integer labID;
	private String labCode;
	private Integer reportStatusID;
	private String reportStatusName;

	@XmlElement(name = "Jobs")
	public ArrayOfTWOADJobInfo getJobs() {
		return jobs;
	}

	public void setJobs(ArrayOfTWOADJobInfo jobs) {
		this.jobs = jobs;
	}

	@XmlElement(name = "Subcontracts")
	public ArrayOfTWOADSubcontractInfo getSubcontracts() {
		return subcontracts;
	}

	public void setSubcontracts(ArrayOfTWOADSubcontractInfo subcontracts) {
		this.subcontracts = subcontracts;
	}

	@XmlElement(name = "CaseTestItems")
	public ArrayOfTWOADCaseTestItem getCaseTestItems() {
		return caseTestItems;
	}

	public void setCaseTestItems(ArrayOfTWOADCaseTestItem caseTestItems) {
		this.caseTestItems = caseTestItems;
	}

	@XmlElement(name = "CaseNo")
	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	@XmlElement(name = "CaseStatusName")
	public String getCaseStatusName() {
		return caseStatusName;
	}

	public void setCaseStatusName(String caseStatusName) {
		this.caseStatusName = caseStatusName;
	}

	@XmlElement(name = "ApplicantName")
	public String getApplicantName() {
		return applicantName;
	}

	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}

	@XmlElement(name = "ApplicantCustomerNo")
	public String getApplicantCustomerNo() {
		return applicantCustomerNo;
	}

	public void setApplicantCustomerNo(String applicantCustomerNo) {
		this.applicantCustomerNo = applicantCustomerNo;
	}

	@XmlElement(name = "ApplicantCustomerEmail")
	public String getApplicantCustomerEmail() {
		return applicantCustomerEmail;
	}

	public void setApplicantCustomerEmail(String applicantCustomerEmail) {
		this.applicantCustomerEmail = applicantCustomerEmail;
	}

	@XmlElement(name = "ApplicantCustomerContactPerson")
	public String getApplicantCustomerContactPerson() {
		return applicantCustomerContactPerson;
	}

	public void setApplicantCustomerContactPerson(String applicantCustomerContactPerson) {
		this.applicantCustomerContactPerson = applicantCustomerContactPerson;
	}

	@XmlElement(name = "BuyerName")
	public String getBuyerName() {
		return buyerName;
	}

	public void setBuyerName(String buyerName) {
		this.buyerName = buyerName;
	}

	@XmlElement(name = "BuyerCustomerNo")
	public String getBuyerCustomerNo() {
		return buyerCustomerNo;
	}

	public void setBuyerCustomerNo(String buyerCustomerNo) {
		this.buyerCustomerNo = buyerCustomerNo;
	}

	@XmlElement(name = "PayerName")
	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	@XmlElement(name = "PayerCustomerNo")
	public String getPayerCustomerNo() {
		return payerCustomerNo;
	}

	public void setPayerCustomerNo(String payerCustomerNo) {
		this.payerCustomerNo = payerCustomerNo;
	}

	@XmlElement(name = "SupplierName")
	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	@XmlElement(name = "SupplierCustomerNo")
	public String getSupplierCustomerNo() {
		return supplierCustomerNo;
	}

	public void setSupplierCustomerNo(String supplierCustomerNo) {
		this.supplierCustomerNo = supplierCustomerNo;
	}

	@XmlElement(name = "ServiceTypeName")
	public String getServiceTypeName() {
		return serviceTypeName;
	}

	public void setServiceTypeName(String serviceTypeName) {
		this.serviceTypeName = serviceTypeName;
	}

	@XmlElement(name = "CaseExpectDueDate")
	public Date getCaseExpectDueDate() {
		return caseExpectDueDate;
	}

	public void setCaseExpectDueDate(Date caseExpectDueDate) {
		this.caseExpectDueDate = caseExpectDueDate;
	}

	@XmlElement(name = "CaseCreateDate")
	public Date getCaseCreateDate() {
		return caseCreateDate;
	}

	public void setCaseCreateDate(Date caseCreateDate) {
		this.caseCreateDate = caseCreateDate;
	}

	@XmlElement(name = "CaseCreatedByName")
	public String getCaseCreatedByName() {
		return caseCreatedByName;
	}

	public void setCaseCreatedByName(String caseCreatedByName) {
		this.caseCreatedByName = caseCreatedByName;
	}

	@XmlElement(name = "CaseActualStartDate")
	public Date getCaseActualStartDate() {
		return caseActualStartDate;
	}

	public void setCaseActualStartDate(Date caseActualStartDate) {
		this.caseActualStartDate = caseActualStartDate;
	}

	@XmlElement(name = "CasePONo")
	public String getCasePONo() {
		return casePONo;
	}

	public void setCasePONo(String casePONo) {
		this.casePONo = casePONo;
	}

	@XmlElement(name = "CaseStyleNo")
	public String getCaseStyleNo() {
		return caseStyleNo;
	}

	public void setCaseStyleNo(String caseStyleNo) {
		this.caseStyleNo = caseStyleNo;
	}

	@XmlElement(name = "CaseResponsibleTeamName")
	public String getCaseResponsibleTeamName() {
		return caseResponsibleTeamName;
	}

	public void setCaseResponsibleTeamName(String caseResponsibleTeamName) {
		this.caseResponsibleTeamName = caseResponsibleTeamName;
	}

	@XmlElement(name = "ResponsibleCSName")
	public String getResponsibleCSName() {
		return responsibleCSName;
	}

	public void setResponsibleCSName(String responsibleCSName) {
		this.responsibleCSName = responsibleCSName;
	}

	@XmlElement(name = "CaseRemark")
	public String getCaseRemark() {
		return caseRemark;
	}

	public void setCaseRemark(String caseRemark) {
		this.caseRemark = caseRemark;
	}

	@XmlElement(name = "SampleQuantity")
	public Integer getSampleQuantity() {
		return sampleQuantity;
	}

	public void setSampleQuantity(Integer sampleQuantity) {
		this.sampleQuantity = sampleQuantity;
	}

	@XmlElement(name = "IsPhotoRequired")
	public boolean isPhotoRequired() {
		return isPhotoRequired;
	}

	public void setPhotoRequired(boolean photoRequired) {
		this.isPhotoRequired = photoRequired;
	}

	@XmlElement(name = "CaseDelayReason")
	public String getCaseDelayReason() {
		return caseDelayReason;
	}

	public void setCaseDelayReason(String caseDelayReason) {
		this.caseDelayReason = caseDelayReason;
	}

	@XmlElement(name = "ApplicantReportRemark")
	public String getApplicantReportRemark() {
		return applicantReportRemark;
	}

	public void setApplicantReportRemark(String applicantReportRemark) {
		this.applicantReportRemark = applicantReportRemark;
	}

	@XmlElement(name = "ApplicantPDRemark")
	public String getApplicantPDRemark() {
		return applicantPDRemark;
	}

	public void setApplicantPDRemark(String applicantPDRemark) {
		this.applicantPDRemark = applicantPDRemark;
	}

	@XmlElement(name = "ApplicantCSRemark")
	public String getApplicantCSRemark() {
		return applicantCSRemark;
	}

	public void setApplicantCSRemark(String applicantCSRemark) {
		this.applicantCSRemark = applicantCSRemark;
	}

	@XmlElement(name = "ApplicantSampleCardRemark")
	public String getApplicantSampleCardRemark() {
		return applicantSampleCardRemark;
	}

	public void setApplicantSampleCardRemark(String applicantSampleCardRemark) {
		this.applicantSampleCardRemark = applicantSampleCardRemark;
	}

	@XmlElement(name = "ApplicantInvoiceRemark")
	public String getApplicantInvoiceRemark() {
		return applicantInvoiceRemark;
	}

	public void setApplicantInvoiceRemark(String applicantInvoiceRemark) {
		this.applicantInvoiceRemark = applicantInvoiceRemark;
	}

	@XmlElement(name = "PayerReportRemark")
	public String getPayerReportRemark() {
		return payerReportRemark;
	}

	public void setPayerReportRemark(String payerReportRemark) {
		this.payerReportRemark = payerReportRemark;
	}

	@XmlElement(name = "PayerPDRemark")
	public String getPayerPDRemark() {
		return payerPDRemark;
	}

	public void setPayerPDRemark(String payerPDRemark) {
		this.payerPDRemark = payerPDRemark;
	}

	@XmlElement(name = "PayerCSRemark")
	public String getPayerCSRemark() {
		return payerCSRemark;
	}

	public void setPayerCSRemark(String payerCSRemark) {
		this.payerCSRemark = payerCSRemark;
	}

	@XmlElement(name = "PayerSampleCardRemark")
	public String getPayerSampleCardRemark() {
		return payerSampleCardRemark;
	}

	public void setPayerSampleCardRemark(String payerSampleCardRemark) {
		this.payerSampleCardRemark = payerSampleCardRemark;
	}

	@XmlElement(name = "PayerInvoiceRemark")
	public String getPayerInvoiceRemark() {
		return payerInvoiceRemark;
	}

	public void setPayerInvoiceRemark(String payerInvoiceRemark) {
		this.payerInvoiceRemark = payerInvoiceRemark;
	}

	@XmlElement(name = "BuyerReportRemark")
	public String getBuyerReportRemark() {
		return buyerReportRemark;
	}

	public void setBuyerReportRemark(String buyerReportRemark) {
		this.buyerReportRemark = buyerReportRemark;
	}

	@XmlElement(name = "BuyerPDRemark")
	public String getBuyerPDRemark() {
		return buyerPDRemark;
	}

	public void setBuyerPDRemark(String buyerPDRemark) {
		this.buyerPDRemark = buyerPDRemark;
	}

	@XmlElement(name = "BuyerCSRemark")
	public String getBuyerCSRemark() {
		return buyerCSRemark;
	}

	public void setBuyerCSRemark(String buyerCSRemark) {
		this.buyerCSRemark = buyerCSRemark;
	}

	@XmlElement(name = "BuyerSampleCardRemark")
	public String getBuyerSampleCardRemark() {
		return buyerSampleCardRemark;
	}

	public void setBuyerSampleCardRemark(String buyerSampleCardRemark) {
		this.buyerSampleCardRemark = buyerSampleCardRemark;
	}

	@XmlElement(name = "BuyerInvoiceRemark")
	public String getBuyerInvoiceRemark() {
		return buyerInvoiceRemark;
	}

	public void setBuyerInvoiceRemark(String buyerInvoiceRemark) {
		this.buyerInvoiceRemark = buyerInvoiceRemark;
	}

	@XmlElement(name = "BUID")
	public Integer getBUID() {
		return bUID;
	}

	public void setBUID(Integer bUID) {
		this.bUID = bUID;
	}

	@XmlElement(name = "BUCode")
	public String getBUCode() {
		return bUCode;
	}

	public void setBUCode(String bUCode) {
		this.bUCode = bUCode;
	}

	@XmlElement(name = "SBUID")
	public Integer getSBUID() {
		return sBUID;
	}

	public void setSBUID(Integer sBUID) {
		this.sBUID = sBUID;
	}

	@XmlElement(name = "SBUCode")
	public String getSBUCode() {
		return sBUCode;
	}

	public void setSBUCode(String sBUCode) {
		this.sBUCode = sBUCode;
	}

	@XmlElement(name = "LabID")
	public Integer getLabID() {
		return labID;
	}

	public void setLabID(Integer labID) {
		this.labID = labID;
	}

	@XmlElement(name = "LabCode")
	public String getLabCode() {
		return labCode;
	}

	public void setLabCode(String labCode) {
		this.labCode = labCode;
	}

	@XmlElement(name = "ReportStatusID")
	public Integer getReportStatusID() {
		return reportStatusID;
	}

	public void setReportStatusID(Integer reportStatusID) {
		this.reportStatusID = reportStatusID;
	}

	@XmlElement(name = "ReportStatusName")
	public String getReportStatusName() {
		return reportStatusName;
	}

	public void setReportStatusName(String reportStatusName) {
		this.reportStatusName = reportStatusName;
	}
}
