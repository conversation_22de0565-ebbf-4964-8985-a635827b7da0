package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
		"strBarcode",
		"processID",
		"userID"
})
@XmlRootElement(name = "AddCaseTimeTrack")
public class AddCaseTimeTrack implements Serializable{

	/**
	 * 支持多个以逗号隔开。
	 */
	private String strBarcode;
	/**
	 * 	114 Sample Separation
	 *	221 Consolidation Out
	 */
	private Integer processID;
	
	private String userID;

	public String getStrBarcode() {
		return strBarcode;
	}

	public void setStrBarcode(String strBarcode) {
		this.strBarcode = strBarcode;
	}

	public Integer getProcessID() {
		return processID;
	}

	public void setProcessID(Integer processID) {
		this.processID = processID;
	}

	public String getUserID() {
		return userID;
	}

	public void setUserID(String userID) {
		this.userID = userID;
	}
}
