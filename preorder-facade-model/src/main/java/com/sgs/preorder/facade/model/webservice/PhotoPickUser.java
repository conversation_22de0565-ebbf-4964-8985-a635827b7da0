package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlType(name = "PhotoPickUser")
public class PhotoPickUser implements Serializable {
    private String userAccountID;
    private String userName;
    private int labID;
    private String LabCode;
    /*@XmlAttribute(name = "BuSubTypeID")*/
    private int buSubTypeID;
    /*@XmlElement(name = "BuSubTypeName")*/
    private String buSubTypeName;

    @XmlElement(name = "UserAccountID")
    public String getUserAccountID() {
        return userAccountID;
    }

    public void setUserAccountID(String userAccountID) {
        this.userAccountID = userAccountID;
    }

    @XmlElement(name = "UserName")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @XmlElement(name = "LabID")
    public int getLabID() {
        return labID;
    }

    public void setLabID(int labID) {
        this.labID = labID;
    }

    @XmlElement(name = "LabCode")
    public String getLabCode() {
        return LabCode;
    }

    public void setLabCode(String labCode) {
        LabCode = labCode;
    }

    @XmlElement(name = "BuSubTypeID")
    public int getBuSubTypeID() {
        return buSubTypeID;
    }

    public void setBuSubTypeID(int buSubTypeID) {
        this.buSubTypeID = buSubTypeID;
    }

    @XmlElement(name = "BuSubTypeName")
    public String getBuSubTypeName() {
        return buSubTypeName;
    }

    public void setBuSubTypeName(String buSubTypeName) {
        this.buSubTypeName = buSubTypeName;
    }
}
