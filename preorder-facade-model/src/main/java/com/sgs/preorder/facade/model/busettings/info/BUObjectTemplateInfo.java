package com.sgs.preorder.facade.model.busettings.info;

import com.sgs.preorder.facade.model.common.BaseProductLineDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "Bu Object Template配置对象", description = "Bu Object Template配置对象")
public class BUObjectTemplateInfo extends BaseProductLineDTO {

    @ApiModelProperty(value = "编码")
    private String id;

    private String locationCode;

    private String objectId;

    @Deprecated
    private String object;

    private String description;

    private Integer activeIndicator;

    private List<BUObjectAttributeInfo> objectAttributes;

    private List<BUObjectAttributeInfo> objectSettingList;

}
