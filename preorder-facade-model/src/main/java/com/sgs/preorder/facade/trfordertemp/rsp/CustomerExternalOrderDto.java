//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.sgs.preorder.facade.trfordertemp.rsp;

import com.alibaba.fastjson.JSON;
import com.sgs.preorder.core.order.dto.ExternalOrderDto;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class CustomerExternalOrderDto extends ExternalOrderDto {
    public void setExtInfo(Object extInfo) {
        if (Objects.isNull(extInfo)) {
            this.setExtInfo(JSON.toJSONString(extInfo));
        }
    }
}
