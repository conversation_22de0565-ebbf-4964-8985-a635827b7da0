package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "domainANDuser",
        "password"
})
@XmlRootElement(name = "GetValidateUser")
public class GetValidateUser implements Serializable {
    /**
     *
     */
    private String domainANDuser;
    /**
     *
     */
    private String password;

    public String getDomainANDuser() {
        return domainANDuser;
    }

    public void setDomainANDuser(String domainANDuser) {
        this.domainANDuser = domainANDuser;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
