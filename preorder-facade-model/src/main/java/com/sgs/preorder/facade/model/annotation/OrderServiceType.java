package com.sgs.preorder.facade.model.annotation;

import com.sgs.preorder.facade.model.enums.OrderBizType;
import com.sgs.preorder.facade.model.enums.OrderType;

import java.lang.annotation.*;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface OrderServiceType {
    /**
     *
     * @return
     */
    OrderType[] orderType();
    /**
     *
     * @return
     */
    OrderBizType bizType();
}
