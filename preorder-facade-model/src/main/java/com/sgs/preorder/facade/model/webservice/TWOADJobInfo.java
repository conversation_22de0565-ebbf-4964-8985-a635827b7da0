package com.sgs.preorder.facade.model.webservice;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.Date;

@XmlType(name = "TWOADJobInfo")
public class TWOADJobInfo implements Serializable {
    private String CaseNo;
    private String JobNo;
    private String JobBarCode;
    private String ReportNo;
    private String JobStatusName;
    private String LabGroupName;
    private String LabSectionName;
    private Date JobCreateDate;
    private Date JobExpectDueDate;
    private String TestLocationName;
    private Date LabInDate;
    private Date LabOutDate;
    private Date DownloadForLabDate;
    private Date DownloadForReportDate;
    private Date UploadForReportDate;
    private Date RegenerateForLabDate;
    private String SampleTransferTo;
    private String SampleTransferDate;
    private String JobCreatedByName;
    private String JobRemarks;
    private String JobResponsible;

    @XmlElement(name = "CaseNo")
    public String getCaseNo() {
        return CaseNo;
    }

    public void setCaseNo(String caseNo) {
        CaseNo = caseNo;
    }

    @XmlElement(name = "JobNo")
    public String getJobNo() {
        return JobNo;
    }

    public void setJobNo(String jobNo) {
        JobNo = jobNo;
    }

    @XmlElement(name = "JobBarCode")
    public String getJobBarCode() {
        return JobBarCode;
    }

    public void setJobBarCode(String jobBarCode) {
        JobBarCode = jobBarCode;
    }

    @XmlElement(name = "ReportNo")
    public String getReportNo() {
        return ReportNo;
    }

    public void setReportNo(String reportNo) {
        ReportNo = reportNo;
    }

    @XmlElement(name = "JobStatusName")
    public String getJobStatusName() {
        return JobStatusName;
    }

    public void setJobStatusName(String jobStatusName) {
        JobStatusName = jobStatusName;
    }

    @XmlElement(name = "LabGroupName")
    public String getLabGroupName() {
        return LabGroupName;
    }

    public void setLabGroupName(String labGroupName) {
        LabGroupName = labGroupName;
    }

    @XmlElement(name = "LabSectionName")
    public String getLabSectionName() {
        return LabSectionName;
    }

    public void setLabSectionName(String labSectionName) {
        LabSectionName = labSectionName;
    }

    @XmlElement(name = "JobCreateDate", required = true)
    @XmlSchemaType(name = "dateTime")
    public Date getJobCreateDate() {
        return JobCreateDate;
    }

    public void setJobCreateDate(Date jobCreateDate) {
        JobCreateDate = jobCreateDate;
    }

    @XmlElement(name = "JobExpectDueDate")
    public Date getJobExpectDueDate() {
        return JobExpectDueDate;
    }

    public void setJobExpectDueDate(Date jobExpectDueDate) {
        JobExpectDueDate = jobExpectDueDate;
    }

    @XmlElement(name = "TestLocationName")
    public String getTestLocationName() {
        return TestLocationName;
    }

    public void setTestLocationName(String testLocationName) {
        TestLocationName = testLocationName;
    }

    @XmlElement(name = "LabInDate")
    public Date getLabInDate() {
        return LabInDate;
    }

    public void setLabInDate(Date labInDate) {
        LabInDate = labInDate;
    }

    @XmlElement(name = "LabOutDate")
    public Date getLabOutDate() {
        return LabOutDate;
    }

    public void setLabOutDate(Date labOutDate) {
        LabOutDate = labOutDate;
    }

    @XmlElement(name = "DownloadForLabDate")
    public Date getDownloadForLabDate() {
        return DownloadForLabDate;
    }

    public void setDownloadForLabDate(Date downloadForLabDate) {
        DownloadForLabDate = downloadForLabDate;
    }

    @XmlElement(name = "DownloadForReportDate")
    public Date getDownloadForReportDate() {
        return DownloadForReportDate;
    }

    public void setDownloadForReportDate(Date downloadForReportDate) {
        DownloadForReportDate = downloadForReportDate;
    }

    @XmlElement(name = "UploadForReportDate")
    public Date getUploadForReportDate() {
        return UploadForReportDate;
    }

    public void setUploadForReportDate(Date uploadForReportDate) {
        UploadForReportDate = uploadForReportDate;
    }

    @XmlElement(name = "RegenerateForLabDate")
    public Date getRegenerateForLabDate() {
        return RegenerateForLabDate;
    }

    public void setRegenerateForLabDate(Date regenerateForLabDate) {
        RegenerateForLabDate = regenerateForLabDate;
    }

    @XmlElement(name = "SampleTransferTo")
    public String getSampleTransferTo() {
        return SampleTransferTo;
    }

    public void setSampleTransferTo(String sampleTransferTo) {
        SampleTransferTo = sampleTransferTo;
    }

    @XmlElement(name = "SampleTransferDate")
    public String getSampleTransferDate() {
        return SampleTransferDate;
    }

    public void setSampleTransferDate(String sampleTransferDate) {
        SampleTransferDate = sampleTransferDate;
    }

    @XmlElement(name = "JobCreatedByName")
    public String getJobCreatedByName() {
        return JobCreatedByName;
    }

    public void setJobCreatedByName(String jobCreatedByName) {
        JobCreatedByName = jobCreatedByName;
    }

    @XmlElement(name = "JobRemarks")
    public String getJobRemarks() {
        return JobRemarks;
    }

    public void setJobRemarks(String jobRemarks) {
        JobRemarks = jobRemarks;
    }

    @XmlElement(name = "JobResponsible")
    public String getJobResponsible() {
        return JobResponsible;
    }

    public void setJobResponsible(String jobResponsible) {
        JobResponsible = jobResponsible;
    }
}
