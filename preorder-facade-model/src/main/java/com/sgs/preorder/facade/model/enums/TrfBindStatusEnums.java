package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TrfBindStatusEnums {
    BIND_TRF(1, "bindTrf"),
    UNBIND_TRF(2,"unbindTrf"),
    UNBIND_TIC(3,"unbindTIC")
    ;

    private final int status;
    private final String message;

    TrfBindStatusEnums(int status, String message) {
        this.status = status;
        this.message = message;
    }
    public int getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, TrfBindStatusEnums> maps = new HashMap<Integer, TrfBindStatusEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TrfBindStatusEnums enu : TrfBindStatusEnums.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static TrfBindStatusEnums findStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    public static boolean checkStatus(Integer status, TrfBindStatusEnums...trfBindStatus){
        if (status == null || !maps.containsKey(status.intValue()) || trfBindStatus == null || trfBindStatus.length <= 0) {
            return false;
        }
        for (TrfBindStatusEnums bindStatus : trfBindStatus){
            if (status.intValue() == bindStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    public boolean check(TrfBindStatusEnums... status){
        if (status == null || status.length <= 0){
            return false;
        }
        for (TrfBindStatusEnums s : status){
            if (this.getStatus() == s.getStatus()){
                return true;
            }
        }
        return false;
    }
}
