package com.sgs.preorder.facade.trfordertemp.rsp;


import com.sgs.preorder.core.order.dto.*;
import com.sgs.preorder.facade.model.info.CustomerInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PreOrderDto implements Serializable {
    private OrderHeadersDto headers;
    private List<ExternalOrderDto> referenceList;
    private OrderProductSampleDto productSample;
    private List<OrderTestlineDto> testlineList;
    private List<OrderMatrixDto> testMatrixList;
    private OrderServiceRequirementDto serviceRequirement;
    private List<OrderReportDto> reportList;
    private List<OrderPersonDto> personList;
    private QuotationDto quotation;
    private List<CustomerInfo> customerList;
    private Integer refSystemId;
}