package com.sgs.preorder.dbstorages.mybatis.model;

import java.util.Date;

public class ReportInfoPO {
    /**
     * ID VARCHAR(36) 必填<br>
     * UUID
     */
    private String ID;

    /**
     * GeneralOrderID VARCHAR(36) 必填<br>
     * FK TB_Order
     */
    private String generalOrderID;

    /**
     * ReportNo VARCHAR(50) 必填<br>
     * no of report
     */
    private String reportNo;

    /**
     * ReportStatus INTEGER(10)<br>
     * 
     */
    private Integer reportStatus;


    private Integer approveStatus;

    /**
     * ReportDescription VARCHAR(500)<br>
     * description of report
     */
    private String reportDescription;

    /**
     * ReportApprovalDate TIMESTAMP(19)<br>
     * approved date of report
     */
    private Date reportApprovalDate;

    /**
     * ApproverBy VARCHAR(50)<br>
     * 
     */
    private String approverBy;

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 
     */
    private String orderNo;

    /**
     * ReportConclusion VARCHAR(50)<br>
     * record the report overall conclusion. Pass/ Fail 
     */
    private String reportConclusion;

    /**
     * SoftCopyDeliveryDate TIMESTAMP(19)<br>
     * record the date time of soft copy delivery
     */
    private Date softCopyDeliveryDate;

    /**
     * ActiveIndicator TINYINT(3) 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * CreatedBy VARCHAR(50)<br>
     * Founder of the UserName
     */
    private String createdBy;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * Creation time
     */
    private Date createdDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * Edit the UserName (like CreateBy for the first time)
     */
    private String modifiedBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * Edit the Date (like CreateDate for the first time)
     */
    private Date modifiedDate;

    private Integer workFlow;

    private String externalReportNo;

    private Integer sealFlag;

    public Integer getWorkFlow() {
        return workFlow;
    }

    public void setWorkFlow(Integer workFlow) {
        this.workFlow = workFlow;
    }

    /**
     * ID VARCHAR(36) 必填<br>
     * 获得 UUID
     */
    public String getID() {
        return ID;
    }

    /**
     * ID VARCHAR(36) 必填<br>
     * 设置 UUID
     */
    public void setID(String ID) {
        this.ID = ID == null ? null : ID.trim();
    }

    /**
     * GeneralOrderID VARCHAR(36) 必填<br>
     * 获得 FK TB_Order
     */
    public String getGeneralOrderID() {
        return generalOrderID;
    }

    /**
     * GeneralOrderID VARCHAR(36) 必填<br>
     * 设置 FK TB_Order
     */
    public void setGeneralOrderID(String generalOrderID) {
        this.generalOrderID = generalOrderID == null ? null : generalOrderID.trim();
    }

    /**
     * ReportNo VARCHAR(50) 必填<br>
     * 获得 no of report
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * ReportNo VARCHAR(50) 必填<br>
     * 设置 no of report
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * ReportStatus INTEGER(10)<br>
     * 获得 
     */
    public Integer getReportStatus() {
        return reportStatus;
    }

    /**
     * ReportStatus INTEGER(10)<br>
     * 设置 
     */
    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    /**
     * ReportDescription VARCHAR(500)<br>
     * 获得 description of report
     */
    public String getReportDescription() {
        return reportDescription;
    }

    /**
     * ReportDescription VARCHAR(500)<br>
     * 设置 description of report
     */
    public void setReportDescription(String reportDescription) {
        this.reportDescription = reportDescription == null ? null : reportDescription.trim();
    }

    /**
     * ReportApprovalDate TIMESTAMP(19)<br>
     * 获得 approved date of report
     */
    public Date getReportApprovalDate() {
        return reportApprovalDate;
    }

    /**
     * ReportApprovalDate TIMESTAMP(19)<br>
     * 设置 approved date of report
     */
    public void setReportApprovalDate(Date reportApprovalDate) {
        this.reportApprovalDate = reportApprovalDate;
    }

    /**
     * ApproverBy VARCHAR(50)<br>
     * 获得 
     */
    public String getApproverBy() {
        return approverBy;
    }

    /**
     * ApproverBy VARCHAR(50)<br>
     * 设置 
     */
    public void setApproverBy(String approverBy) {
        this.approverBy = approverBy == null ? null : approverBy.trim();
    }

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 获得 
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 设置 
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * ReportConclusion VARCHAR(50)<br>
     * 获得 record the report overall conclusion. Pass/ Fail 
     */
    public String getReportConclusion() {
        return reportConclusion;
    }

    /**
     * ReportConclusion VARCHAR(50)<br>
     * 设置 record the report overall conclusion. Pass/ Fail 
     */
    public void setReportConclusion(String reportConclusion) {
        this.reportConclusion = reportConclusion == null ? null : reportConclusion.trim();
    }

    /**
     * SoftCopyDeliveryDate TIMESTAMP(19)<br>
     * 获得 record the date time of soft copy delivery
     */
    public Date getSoftCopyDeliveryDate() {
        return softCopyDeliveryDate;
    }

    /**
     * SoftCopyDeliveryDate TIMESTAMP(19)<br>
     * 设置 record the date time of soft copy delivery
     */
    public void setSoftCopyDeliveryDate(Date softCopyDeliveryDate) {
        this.softCopyDeliveryDate = softCopyDeliveryDate;
    }

    /**
     * ActiveIndicator TINYINT(3) 默认值[1] 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator TINYINT(3) 默认值[1] 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 Founder of the UserName
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 Founder of the UserName
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 Creation time
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 Creation time
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 Edit the UserName (like CreateBy for the first time)
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 Edit the UserName (like CreateBy for the first time)
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 Edit the Date (like CreateDate for the first time)
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 Edit the Date (like CreateDate for the first time)
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getExternalReportNo() {
        return externalReportNo;
    }

    public void setExternalReportNo(String externalReportNo) {
        this.externalReportNo = externalReportNo;
    }

    public Integer getSealFlag() {
        return sealFlag;
    }

    public void setSealFlag(Integer sealFlag) {
        this.sealFlag = sealFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ID=").append(ID);
        sb.append(", generalOrderID=").append(generalOrderID);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", reportStatus=").append(reportStatus);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", reportDescription=").append(reportDescription);
        sb.append(", reportApprovalDate=").append(reportApprovalDate);
        sb.append(", approverBy=").append(approverBy);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", reportConclusion=").append(reportConclusion);
        sb.append(", softCopyDeliveryDate=").append(softCopyDeliveryDate);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", externalReportNo=").append(externalReportNo);
        sb.append("]");
        return sb.toString();
    }
}