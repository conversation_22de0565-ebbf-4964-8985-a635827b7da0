package com.sgs.preorder.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 金额序列号转换
 */
public class MoneySerialize extends JsonSerializer {

    @Override
    public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if(o != null && o instanceof BigDecimal){
            BigDecimal bigDecomal = (BigDecimal) o;
            DecimalFormat formatter = new DecimalFormat("###,##0.00");
            formatter.setRoundingMode(RoundingMode.HALF_UP);
            jsonGenerator.writeString(formatter.format(bigDecomal));
        }
    }
}