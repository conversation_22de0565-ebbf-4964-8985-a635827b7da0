package com.sgs.usermanagement.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;

@Data
public class QueryUserInfoReq extends BaseRequest {

    private String systemName = "General PreOrder";


    /**
     * 分页信息
     */
    private Integer page;
    private Integer rows;

    /**
     * 查询条件
     */
    private String regionAccount;
    private String labCode;
    private String telephone;
    private String email;
    private String teamCode;

    /**
     *
     * dimensions条件
     */
    private List<QueryUserDimension> dimensions;
    /**
     *  1 表示dimensions 中的条件是且的关系
     *  0或者不传值 表示dimensions 中的条件是或的关系
     */
    private Integer dimensionFilterByAnd;
    //
    private boolean accurate = false;
    //是否允许查询失效的用户，默认不传，或传0只查有效的，传1时可以查询失效的
    private Integer isAll;
}
