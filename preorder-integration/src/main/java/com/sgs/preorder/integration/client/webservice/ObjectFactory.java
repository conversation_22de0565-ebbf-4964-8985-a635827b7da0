
package com.sgs.preorder.integration.client.webservice;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.sgs.preorder.integration.client.webservice package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.sgs.preorder.integration.client.webservice
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link InsOtsCaseNOResponse }
     * 
     */
    public InsOtsCaseNOResponse createInsOtsCaseNOResponse() {
        return new InsOtsCaseNOResponse();
    }

    /**
     * Create an instance of {@link UpdJobLabOutResponse }
     * 
     */
    public UpdJobLabOutResponse createUpdJobLabOutResponse() {
        return new UpdJobLabOutResponse();
    }

    /**
     * Create an instance of {@link UpdJobLabOut }
     * 
     */
    public UpdJobLabOut createUpdJobLabOut() {
        return new UpdJobLabOut();
    }

    /**
     * Create an instance of {@link UpdSubcontractStatusResponse }
     * 
     */
    public UpdSubcontractStatusResponse createUpdSubcontractStatusResponse() {
        return new UpdSubcontractStatusResponse();
    }

    /**
     * Create an instance of {@link UpdSubcontractStatus }
     * 
     */
    public UpdSubcontractStatus createUpdSubcontractStatus() {
        return new UpdSubcontractStatus();
    }

    /**
     * Create an instance of {@link InsOtsCaseNO }
     * 
     */
    public InsOtsCaseNO createInsOtsCaseNO() {
        return new InsOtsCaseNO();
    }

}
