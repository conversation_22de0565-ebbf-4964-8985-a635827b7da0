package com.sgs.preorder.integration.client;

import com.sgs.extsystem.facade.CustomerConfigFacade;
import com.sgs.extsystem.facade.model.customer.req.CustomerConfigReq;
import com.sgs.extsystem.facade.model.customer.req.TrfInfoReq;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.extsystem.facade.model.customer.rsp.TrfInfoRsp;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class IntegrationCustomerClient {

    private Logger logger = LoggerFactory.getLogger(IntegrationCustomerClient.class);

    @Autowired
    private CustomerConfigFacade customerConfigFacade;

    /**
     *
     * @param reqObject
     * @return
     */
     public CustomResult<CustomerConfigRsp> getIntegrationCustomerConfig(CustomerConfigReq reqObject){
        CustomResult rspResult = new CustomResult();
        try {
            BaseResponse<CustomerConfigRsp> baseResponse = customerConfigFacade.getIntegrationCustomerConfig(reqObject);
            rspResult.setSuccess(baseResponse.getStatus() == 200);
            rspResult.setData(baseResponse.getData());
            rspResult.setStackTrace(baseResponse.getStackTrace());
            rspResult.setMsg(baseResponse.getMessage());
        }catch (Exception ex){
            logger.error("IntegrationCustomerClient.getIntegrationCustomerConfig 查询第三方系统配置表({})信息异常：{}.", reqObject, ex.getMessage(), ex);
        }
        return rspResult;
    }

    /**
     *
     * @param refSystemId
     * @param productLineId
     * @param productLineCode
     * @return
     */
    public CustomerConfigRsp getIntegrationCustomerConfigByRefId(Integer refSystemId, Integer productLineId, String productLineCode){
        CustomerConfigReq reqObject = new CustomerConfigReq();
        reqObject.setRefSystemId(refSystemId);
        reqObject.setProductLineId(productLineId);
        reqObject.setProductLineCode(productLineCode);
        return this.getCustomerConfigInfo(reqObject);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomerConfigRsp getCustomerConfigInfo(CustomerConfigReq reqObject){
        try {
            BaseResponse<CustomerConfigRsp> baseResponse = customerConfigFacade.getIntegrationCustomerConfig(reqObject);
            return baseResponse.getData();
        }catch (Exception ex){
            logger.error("IntegrationCustomerClient.getIntegrationCustomerConfig 查询第三方系统配置表({})信息异常：{}.", reqObject.getRefSystemId(), ex.getMessage(), ex);
        }
        return null;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult<TrfInfoRsp> getTrfInfo(TrfInfoReq reqObject){
         CustomResult rspResult = new CustomResult();
         try {
             BaseResponse<TrfInfoRsp> baseResponse = customerConfigFacade.getTrfInfo(reqObject);
             rspResult.setSuccess(baseResponse.getStatus() == 200);
             rspResult.setData(baseResponse.getData());
             rspResult.setStackTrace(baseResponse.getStackTrace());
             rspResult.setMsg(baseResponse.getMessage());
         }catch (Exception e){
             logger.error("IntegrationCustomerClient。getTrfInfo 获取TRF信息({})异常:{}",reqObject,e.getMessage(),e);
         }
         return rspResult;
    }




}
