package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.otsnotes.facade.OrderCopyFacade;
import com.sgs.otsnotes.facade.model.req.copy.SyncOrderCopyInfo;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.core.util.CopyOrderUtils;
import com.sgs.preorder.facade.model.ordercopy.NoCopyTestLineInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OrderCopyClient {
    private static final Logger logger = LoggerFactory.getLogger(OrderCopyClient.class);
    @Autowired
    private OrderCopyFacade orderCopyFacade;

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult orderCopyInfo(SyncOrderCopyInfo reqObject){
        logger.info("OrderCopyClient.orderCopyInfo,Req:{}", JSON.toJSONString(reqObject));
        CustomResult rspResult = new CustomResult(false);
        reqObject.setRequestId(Constants.getRequestId());
        reqObject.setProductLineCode(CopyOrderUtils.getTargetProductLineCode());
        reqObject.setSourceBuCode(CopyOrderUtils.getSourceProductLineCode());
        reqObject.setTargetBuCode(CopyOrderUtils.getTargetProductLineCode());
        try {
            BaseResponse<List<com.sgs.otsnotes.facade.model.ordercopy.NoCopyTestLineInfo>> baseResponse = orderCopyFacade.orderCopyInfo(reqObject);
            logger.info("OrderCopyClient.orderCopyInfo,Rsp:{}",JSON.toJSONString(baseResponse));
            rspResult.setSuccess(baseResponse.getStatus() == 200);
            if (baseResponse.getData() != null){
                List<NoCopyTestLineInfo> noCopyTestLines = Lists.newArrayList();
                baseResponse.getData().forEach(testLine->{
                    NoCopyTestLineInfo noCopyTestLine = new NoCopyTestLineInfo();
                    BeanUtils.copyProperties(testLine, noCopyTestLine);
                    noCopyTestLines.add(noCopyTestLine);
                });
                rspResult.setData(noCopyTestLines);
            }
            rspResult.setStackTrace(baseResponse.getStackTrace());
            rspResult.setMsg(baseResponse.getMessage());
            return rspResult;
        }catch (Exception ex){
            logger.error("OrderCopyClient.orderCopyInfo 同步Copy订单({})信息异常：{}.", reqObject.getOrderNo(), ex.getMessage(), ex);
        }
        return rspResult;
    }
}
