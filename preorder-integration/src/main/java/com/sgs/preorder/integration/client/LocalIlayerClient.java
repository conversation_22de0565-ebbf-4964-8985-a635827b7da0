package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.serialize.CustomDateDeserializer;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.dto.order.OrderDetailDto;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.dto.starlims.ToStarLimsData;
import com.sgs.preorder.facade.model.info.OrderHeaderInfo;
import com.sgs.preorder.facade.model.localilayer.req.SyncGetInfoRequest;
import com.sgs.preorder.integration.client.dto.IlayerBody;
import com.sgs.preorder.integration.client.dto.TrfResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 */
@Component
@AllArgsConstructor
@Slf4j
public class LocalIlayerClient {
    private static final Logger logger = LoggerFactory.getLogger(LocalIlayerClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private SystemLogHelper systemLogHelper;

    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();

        objectMapper.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 解析.NET客戶端传入的时间格式
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleModule module = new SimpleModule();
        module.addDeserializer(Date.class, new CustomDateDeserializer());
        objectMapper.registerModule(module);
        objectMapper.setDateFormat(dateFormat);
    }

    public List<OrderInfoDto> queryBusinessLog(Date preBeginDate) {
        String url = String.format("%s/openapi/BusinessLog/QueryBusinessLog", interfaceConfig.getLocalIlayerUrl());
        Map<String, String> params = new HashMap<String, String>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MINUTE, -15);
        String mDateTime = formatter.format(c.getTime());
        params.put("sourceApplicationCode", "HLPreOrder");
        params.put("targetApplicationCode", "StarLims");
        params.put("buCode", "HL");
        params.put("targetSchemaCode", "FolderCreation");
        params.put("processIndicator", "1");
        params.put("sendDateTo", formatter.format(preBeginDate));
        params.put("sendDateFrom", mDateTime);
        params.put("processResultIndicator", "0");
        params.put("pageIndex", "1");
        params.put("pageSize", "100");

        String paramResult = null;
        try {
            paramResult = HttpClientUtil.postJson(url, params);
        } catch (Exception e) {
            return Lists.newArrayList();
        }

        String rows = JSON.parseObject(paramResult).getString("returnList");
        JSONArray jsonArray = JSONArray.parseArray(rows);
        int size = jsonArray.size();
        List<OrderInfoDto> orders = Lists.newArrayList();
        if (size > 0) {
            for (int i = 0; i < size; i++) {
                String tmp = jsonArray.get(i).toString();
                OrderInfoDto order = new OrderInfoDto();
                order.setOrderNo(JSON.parseObject(tmp).getString("object_number"));
                order.setRemark(JSON.parseObject(tmp).getString("remark"));
                orders.add(order);
            }
        }
        return orders;
    }


    public BaseResponse toStarlims(ToStarLimsData toStarLimsData) {
        String url = String.format("%s/openapi/sync/process", interfaceConfig.getLocalIlayerUrl());
        BaseResponse baseResponse = new BaseResponse();
        Map<String, Object> paramsMap = new HashMap<>();
        try {
            paramsMap.put("data", toStarLimsData);
            log.info("tostalims params:{}", JSON.toJSON(paramsMap));
            String postJson = HttpClientUtil.postJson(url, JSON.toJSON(paramsMap));
            log.info("tostalims resp:{}", postJson);
            baseResponse = JSON.parseObject(postJson, BaseResponse.class);
            return baseResponse;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            baseResponse.setMessage("Call Localipayer intefface is fail");
            return BaseResponse.newFailInstance("Call Tostarlims interface is fail");
        } finally {
            SystemLog systemLog = new SystemLog();
            systemLog.setObjectType("order");
            systemLog.setObjectNo(toStarLimsData.getObjectNumber());
            systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            systemLog.setType(SystemLogType.API.getType());
            systemLog.setRemark("To Starlims");
            systemLog.setRequest(JSON.toJSONString(paramsMap));
            systemLog.setResponse(JSON.toJSONString(baseResponse));
            systemLogHelper.save(systemLog);
        }
    }

    public BaseResponse<String> createTrf(OrderDetailDto orderDetailDto) {
        BaseResponse<String> baseResponse = new BaseResponse<String>();
        OrderHeaderInfo headers = orderDetailDto.getHeaders();
        IlayerBody ilayerBody = new IlayerBody();
        ilayerBody.setObjectNumber(headers.getOrderNo());
        ilayerBody.setApplicationMappingCode("GPOOrderToSGSMartTRF");
        ilayerBody.setBu(headers.getBuCode());
        String url = String.format("%s/openapi/sync/process", interfaceConfig.getLocalIlayerUrl());
        try {
            log.info("createTrf request orderNo={}", headers.getOrderNo());
            ilayerBody.setBody(orderDetailDto);
            String fullJSON = objectMapper.writeValueAsString(ilayerBody);
            String result = HttpClientUtil.postJson(url, fullJSON);
            log.info("TRF返回值对象={}",result);
            if (Func.isEmpty(result)) {
                baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错,返回值为空");
                return baseResponse;
            }
            BaseResponse<TrfResult> btr = JSON.parseObject(result, new TypeReference<BaseResponse<TrfResult>>() {
            });
            if (Func.isEmpty(btr)) {
                baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错，Json转Object出错");
                return baseResponse;
            }
            if (!Func.equalsSafe(ResponseCode.SUCCESS.getCode(), btr.getStatus()) || Func.isEmpty(btr.getData())) {
                baseResponse = BaseResponse.newFailInstance(StringUtils.defaultString(btr.getMessage(), "请求createTrf接口报错，ResponseCode不是Success或者为空"));
                return baseResponse;
            }
            TrfResult trf = btr.getData();
            if (Func.isEmpty(trf) || Func.isEmpty(trf.getData())) {
                String message = StringUtils.defaultString(trf.getMessage(), trf.getMsg());
                baseResponse = BaseResponse.newFailInstance(StringUtils.defaultString(message, "请求createTrf接口报错，返回结果Data为空"));
                return baseResponse;
            }
            baseResponse.setData(trf.getData());
            return baseResponse;
        } catch (Exception e) {
            log.error("请求createTrf接口报错:{}",e);
            baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错" + e.toString());
            return baseResponse;
        }
    }

    public BaseResponse syncGetInfo(SyncGetInfoRequest reqObject, Integer timeout) {
        BaseResponse result = new BaseResponse();
        try {
            String url = String.format("%s/openapi/sync/getInfo", interfaceConfig.getLocalIlayerUrl());
            result = HttpClientUtil.post(url, reqObject, BaseResponse.class, timeout);
            if (!result.isSuccess()) {
                logger.error("sync getInfo失败 : {}", result.getMessage());
                return BaseResponse.newFailInstance(result.getMessage());
            }
            return result;
        } catch (Exception e) {
            logger.error("sync getInfo失败 : {}", e.getMessage(), e);
            return BaseResponse.newFailInstance(e.getMessage());
        } finally {
            SystemLog systemLog = new SystemLog();
            systemLog.setObjectType("order");
            systemLog.setObjectNo(reqObject.getObjectNumber());
            systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            systemLog.setType(SystemLogType.API.getType());
            systemLog.setOperationType("LocalIlayer GetOrderDetail");
            systemLog.setRemark("LocalIlayer GetOrderDetail");
            systemLog.setRequest(JSON.toJSONString(reqObject));
            systemLog.setResponse(JSON.toJSONString(result));
            systemLogHelper.save(systemLog);
        }

    }
}
