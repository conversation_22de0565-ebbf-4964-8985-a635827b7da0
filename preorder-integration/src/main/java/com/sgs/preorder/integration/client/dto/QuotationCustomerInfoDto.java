package com.sgs.preorder.integration.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.base.BaseProductLine;
import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class QuotationCustomerInfoDto extends BaseProductLine {
    /**
     *
     */
    private String Id;
    /**
     * GeneralOrderID VARCHAR(36) 必填<br>
     * FK TB_Order
     */
    private String orderId;

    /**
     * CustomerID VARCHAR(36)<br>
     * relate to TB_Customer PK
     */
    private String customerId;

    /**
     * CustomerGroupID VARCHAR(50)<br>
     */
    private String customerGroupId;

    /**
     * CustomerAddressCN VARCHAR(500)<br>
     * The chinese address of customer
     */
   
    
    private String customerAddressCN;

    /**
     * CustomerAddressEN VARCHAR(500)<br>
     * The  english address of customer
     */
   
    
    private String customerAddressEN;

    /**
     * CustomerNameCN VARCHAR(250)<br>
     * The chinese name of customer
     */
   
    
    private String customerNameCN;

    /**
     * CustomerNameEN VARCHAR(250)<br>
     * The english name of customer
     */
   
    
    private String customerNameEN;

    /**
     * ContactPersonEmail VARCHAR(100)<br>
     * The email of the contact
     */
   
    
    private String contactPersonEmail;

    /**
     * ContactPersonFax VARCHAR(100)<br>
     * The fax of the contact
     */
   
    
    private String contactPersonFax;

    /**
     * ContactPersonPhone1 VARCHAR(50)<br>
     * The mobile of the contact
     */
   
    
    private String contactPersonPhone1;

    /**
     * ContactPersonName VARCHAR(50)<br>
     * The name of the contact
     */
   
    
    private String contactPersonName;

    /**
     * ContactPersonRemark VARCHAR(500)<br>
     * The remark of the contact
     */
    private String contactPersonRemark;

    /**
     * ContactPersonPhone2 VARCHAR(50)<br>
     * The telephones of the contact
     */
   
    
    private String contactPersonPhone2;

    /**
     * CustomerCredit VARCHAR(250)<br>
     * The credit of customer
     */
    private String customerCredit;

    /**
     * CustomerUsage VARCHAR(50)<br>
     * Usage: Applicant , Payer, Report to
     */
    private String customerUsage;

    /**
     * AccountID BIGINT(19)<br>
     * Customer Boss Account ID
     */
    private Long accountID;

    /**
     * BossNumber BIGINT(19)<br>
     * Customer Boss Number
     */
   
    
    private Long bossNumber;

    /**
     * ContactAddressID VARCHAR(36)<br>
     */
   
    
    private String contactAddressID;

    /**
     * BuyerGroup VARCHAR(50)<br>
     * TB_CustomerGroup FK
     */
   
    
    private String buyerGroup;

    /**
     * BuyerGroupName VARCHAR(100)<br>
     */
   
    
    private String buyerGroupName;

    /**
     * SupplierNo VARCHAR(50)<br>
     */
    private String supplierNo;

    /**
     * LogoCloudID VARCHAR(250)<br>
     */
//   
//    
    private String logoCloudID;

    /**
     * OrganizationName VARCHAR(500)<br>
     */
    private String organizationName;

    /**
     * IsAsApplicant TINYINT(3)<br>
     */
    private Integer isAsApplicant;

    /**
     * ReportDeliveredTo VARCHAR(500)<br>
     * Customer Report Delivered To
     */
    private String reportDeliveredTo;

    /**
     * FailedReportDeliveredTo VARCHAR(500)<br>
     * Customer Failed Report Delivered To
     */
    private String failedReportDeliveredTo;

    /**
     * BossSiteUseID BIGINT(19)<br>
     */
    private Long bossSiteUseID;

    /**
     * BossContactID BIGINT(19)<br>
     */
    private Long bossContactID;

    /**
     *
     */
    private String bossLocationCode;
    /**
     *
     */
    private String monthlyPayment;
    /**
     *
     */
    private String primaryFlag;

    /**
     *
     */
    @JsonIgnore
    private String modifiedBy;

    /**
     *
     */
    @JsonIgnore
    private Date modifiedDate;

    /**
     *
     */
    private int oldVersionId;

    /**
     *
     */
    private String paymentTermName;
    /**
     * customer是否可以修改 0-未锁定可以修改 1-锁定
     */
    private Integer CustomerLock;

    private String enquiryId;


}
