package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.facade.model.sci.dto.TrfDTO;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.req.sci.SciRequest;
import com.sgs.preorder.facade.model.req.sci.SciTrfDetailReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: SciClient
 * @projectName preorder-service
 * @description: TODO
 * @date 2024/1/2 15:01
 */
@Component
@AllArgsConstructor
@Slf4j
public class SciClient {
    @Autowired
    private InterfaceConfig interfaceConfig;

    public BaseResponse<TrfDTO> importTrf(SciRequest sciRequest) {
        if (Func.isEmpty(sciRequest) || Func.isEmpty(sciRequest.getBody())) {
            return BaseResponse.newFailInstance("param.miss", null);
        }
        SciTrfDetailReq trfDetailReq = (SciTrfDetailReq) sciRequest.getBody();
        if (Func.isEmpty(trfDetailReq.getTrfNo()) || Func.isEmpty(trfDetailReq.getRefSystemId())) {
            return BaseResponse.newFailInstance("param.miss.placeholder", new Object[]{"trfNo/RefSystemId"});
        }
        BaseResponse<TrfDTO> trfDTOBaseResponse = new BaseResponse<>();
        String importTrfUrl = interfaceConfig.getBaseUrl() + "/sci/v2/trf/importTrf";
        Map<String, Object> headers = new HashMap<>();
        headers.put("requestId", Func.randomUUID());
        headers.put("systemId", sciRequest.getSystemId());
        headers.put("labCode", sciRequest.getLabCode());
        headers.put("productLineCode", sciRequest.getProductLineCode());

        String responseBody = null;
        try {
            log.info("call sci importTrf url:{}, request Body:{},Header:{}",importTrfUrl,JSON.toJSONString(sciRequest.getBody()), JSON.toJSONString(headers));
            responseBody = HttpClientUtil.postJson(importTrfUrl, headers, JSON.toJSONString(sciRequest.getBody()), null);
            trfDTOBaseResponse = JSON.parseObject(responseBody, new TypeReference<BaseResponse<TrfDTO>>() {});
        } catch (Exception e) {
            log.error("call sci importTrf error:{}", e);
            return BaseResponse.newFailInstance(e.getMessage());
        }
        return trfDTOBaseResponse;
    }

}
