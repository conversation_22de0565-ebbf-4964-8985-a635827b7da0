package com.sgs.preorder.integration.client;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.otsnotes.facade.TestMatrixFacade;
import com.sgs.otsnotes.facade.model.info.matrix.TestMatrixInfo;
import com.sgs.otsnotes.facade.model.req.copy.CopyMatrixReportInfo;
import com.sgs.otsnotes.facade.model.req.copy.CopyMatrixReportReq;
import com.sgs.preorder.facade.model.ordercopy.CopyReportInfo;
import com.sgs.preorder.facade.model.ordercopy.CopyReportReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TestMatrixClient {
    private static final Logger logger = LoggerFactory.getLogger(TestMatrixClient.class);
    @Autowired
    private TestMatrixFacade testMatrixFacade;

    /**
     *
     * @param copyReport
     * @return
     */
    public CustomResult<List<CopyReportInfo>> getTestMatrixListForCopy(CopyReportReq copyReport){
        CustomResult rspResult = new CustomResult(false);
        try {
            CopyMatrixReportReq reqObject = new CopyMatrixReportReq();
            BeanUtils.copyProperties(copyReport, reqObject);
            List<TestMatrixInfo> testMatrixs = Lists.newArrayList();
            if (copyReport.getTestMatrixs() != null){
                copyReport.getTestMatrixs().forEach(testMatrix->{
                    TestMatrixInfo newTestMatrix = new TestMatrixInfo();
                    BeanUtils.copyProperties(testMatrix, newTestMatrix);
                    testMatrixs.add(newTestMatrix);
                });
            }
            reqObject.setTestMatrixs(testMatrixs);
            BaseResponse<List<CopyMatrixReportInfo>> baseResponse = testMatrixFacade.getTestMatrixListForCopy(reqObject);

            List<CopyReportInfo> copyReports = Lists.newArrayList();
            if (baseResponse.getData() != null){
                baseResponse.getData().forEach(copyReportInfo->{
                    CopyReportInfo newCopyReport = new CopyReportInfo();
                    BeanUtils.copyProperties(copyReportInfo, newCopyReport);
                    copyReports.add(newCopyReport);
                });
            }
            rspResult.setSuccess(baseResponse.getStatus() == 200);
            rspResult.setData(copyReports);
            rspResult.setMsg(baseResponse.getMessage());
            rspResult.setStackTrace(baseResponse.getStackTrace());
        }catch (Exception ex){
            logger.error("TestMatrixClient.getTestMatrixListForCopy 获取SplitReport({})信息异常：{}.", copyReport.getOrderNo(), ex.getMessage(), ex);
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }
}
