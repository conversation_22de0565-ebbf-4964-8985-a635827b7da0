package com.sgs.preorder.integration.client.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class QuotationPpInstanceDto {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.order_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.pp_version_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer ppVersionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.pp_no
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer ppNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.pp_client_ref_no
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String ppClientRefNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.pp_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String ppName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.pp_status
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String ppStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.currency
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String currency;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.price_attribute
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String priceAttribute;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.unit_price
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private BigDecimal unitPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.tat
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String tat;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.sample_size
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String sampleSize;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.remark
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.parent_pp_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer parentPpId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.active_indicator
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Boolean activeIndicator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.created_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Date createdDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.created_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String createdBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.modified_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Date modifiedDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.modified_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String modifiedBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.product_line
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String productLine;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.pp_version
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer ppVersion;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String serviceItemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.quotation_service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String quotationServiceItemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.bu_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String buCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_pp_instance.lab_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String labCode;

    private String locationCode;

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.id
     *
     * @return the value of tb_pe_quotation_pp_instance.id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.id
     *
     * @param id the value for tb_pe_quotation_pp_instance.id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.order_id
     *
     * @return the value of tb_pe_quotation_pp_instance.order_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.order_id
     *
     * @param orderId the value for tb_pe_quotation_pp_instance.order_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.pp_version_id
     *
     * @return the value of tb_pe_quotation_pp_instance.pp_version_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public Integer getPpVersionId() {
        return ppVersionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.pp_version_id
     *
     * @param ppVersionId the value for tb_pe_quotation_pp_instance.pp_version_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.pp_no
     *
     * @return the value of tb_pe_quotation_pp_instance.pp_no
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public Integer getPpNo() {
        return ppNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.pp_no
     *
     * @param ppNo the value for tb_pe_quotation_pp_instance.pp_no
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.pp_client_ref_no
     *
     * @return the value of tb_pe_quotation_pp_instance.pp_client_ref_no
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getPpClientRefNo() {
        return ppClientRefNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.pp_client_ref_no
     *
     * @param ppClientRefNo the value for tb_pe_quotation_pp_instance.pp_client_ref_no
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setPpClientRefNo(String ppClientRefNo) {
        this.ppClientRefNo = ppClientRefNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.pp_name
     *
     * @return the value of tb_pe_quotation_pp_instance.pp_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getPpName() {
        return ppName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.pp_name
     *
     * @param ppName the value for tb_pe_quotation_pp_instance.pp_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setPpName(String ppName) {
        this.ppName = ppName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.pp_status
     *
     * @return the value of tb_pe_quotation_pp_instance.pp_status
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getPpStatus() {
        return ppStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.pp_status
     *
     * @param ppStatus the value for tb_pe_quotation_pp_instance.pp_status
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setPpStatus(String ppStatus) {
        this.ppStatus = ppStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.currency
     *
     * @return the value of tb_pe_quotation_pp_instance.currency
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.currency
     *
     * @param currency the value for tb_pe_quotation_pp_instance.currency
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.price_attribute
     *
     * @return the value of tb_pe_quotation_pp_instance.price_attribute
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getPriceAttribute() {
        return priceAttribute;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.price_attribute
     *
     * @param priceAttribute the value for tb_pe_quotation_pp_instance.price_attribute
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setPriceAttribute(String priceAttribute) {
        this.priceAttribute = priceAttribute;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.unit_price
     *
     * @return the value of tb_pe_quotation_pp_instance.unit_price
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.unit_price
     *
     * @param unitPrice the value for tb_pe_quotation_pp_instance.unit_price
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.tat
     *
     * @return the value of tb_pe_quotation_pp_instance.tat
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getTat() {
        return tat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.tat
     *
     * @param tat the value for tb_pe_quotation_pp_instance.tat
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setTat(String tat) {
        this.tat = tat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.sample_size
     *
     * @return the value of tb_pe_quotation_pp_instance.sample_size
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getSampleSize() {
        return sampleSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.sample_size
     *
     * @param sampleSize the value for tb_pe_quotation_pp_instance.sample_size
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setSampleSize(String sampleSize) {
        this.sampleSize = sampleSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.remark
     *
     * @return the value of tb_pe_quotation_pp_instance.remark
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.remark
     *
     * @param remark the value for tb_pe_quotation_pp_instance.remark
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.parent_pp_id
     *
     * @return the value of tb_pe_quotation_pp_instance.parent_pp_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public Integer getParentPpId() {
        return parentPpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.parent_pp_id
     *
     * @param parentPpId the value for tb_pe_quotation_pp_instance.parent_pp_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setParentPpId(Integer parentPpId) {
        this.parentPpId = parentPpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.active_indicator
     *
     * @return the value of tb_pe_quotation_pp_instance.active_indicator
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public Boolean getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.active_indicator
     *
     * @param activeIndicator the value for tb_pe_quotation_pp_instance.active_indicator
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setActiveIndicator(Boolean activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.created_date
     *
     * @return the value of tb_pe_quotation_pp_instance.created_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.created_date
     *
     * @param createdDate the value for tb_pe_quotation_pp_instance.created_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.created_by
     *
     * @return the value of tb_pe_quotation_pp_instance.created_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.created_by
     *
     * @param createdBy the value for tb_pe_quotation_pp_instance.created_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.modified_date
     *
     * @return the value of tb_pe_quotation_pp_instance.modified_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.modified_date
     *
     * @param modifiedDate the value for tb_pe_quotation_pp_instance.modified_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.modified_by
     *
     * @return the value of tb_pe_quotation_pp_instance.modified_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.modified_by
     *
     * @param modifiedBy the value for tb_pe_quotation_pp_instance.modified_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.product_line
     *
     * @return the value of tb_pe_quotation_pp_instance.product_line
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getProductLine() {
        return productLine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.product_line
     *
     * @param productLine the value for tb_pe_quotation_pp_instance.product_line
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.pp_version
     *
     * @return the value of tb_pe_quotation_pp_instance.pp_version
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public Integer getPpVersion() {
        return ppVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.pp_version
     *
     * @param ppVersion the value for tb_pe_quotation_pp_instance.pp_version
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setPpVersion(Integer ppVersion) {
        this.ppVersion = ppVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.service_item_id
     *
     * @return the value of tb_pe_quotation_pp_instance.service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getServiceItemId() {
        return serviceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.service_item_id
     *
     * @param serviceItemId the value for tb_pe_quotation_pp_instance.service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setServiceItemId(String serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.quotation_service_item_id
     *
     * @return the value of tb_pe_quotation_pp_instance.quotation_service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getQuotationServiceItemId() {
        return quotationServiceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.quotation_service_item_id
     *
     * @param quotationServiceItemId the value for tb_pe_quotation_pp_instance.quotation_service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setQuotationServiceItemId(String quotationServiceItemId) {
        this.quotationServiceItemId = quotationServiceItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.bu_code
     *
     * @return the value of tb_pe_quotation_pp_instance.bu_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getBuCode() {
        return buCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.bu_code
     *
     * @param buCode the value for tb_pe_quotation_pp_instance.bu_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_pp_instance.lab_code
     *
     * @return the value of tb_pe_quotation_pp_instance.lab_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getLabCode() {
        return labCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_pp_instance.lab_code
     *
     * @param labCode the value for tb_pe_quotation_pp_instance.lab_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }
}