
package com.sgs.preorder.integration.client.webservice;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex typJava
 * 
 * <p>
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="InsOtsCaseNOResult" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "insOtsCaseNOResult"
})
@XmlRootElement(name = "InsOtsCaseNOResponse")
public class InsOtsCaseNOResponse {

    @XmlElement(name = "InsOtsCaseNOResult")
    protected int insOtsCaseNOResult;

    /**
     * ȡinsOtsCaseNOResult
     * 
     */
    public int getInsOtsCaseNOResult() {
        return insOtsCaseNOResult;
    }

    /**
     * insOtsCaseNOResult
     * 
     */
    public void setInsOtsCaseNOResult(int value) {
        this.insOtsCaseNOResult = value;
    }

}
