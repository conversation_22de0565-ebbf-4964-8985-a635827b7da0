package com.sgs.preorder.integration.client.setting.buparam.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.facade.model.req.BuParamReq;
import com.sgs.preorder.integration.client.FrameWorkClient;
import com.sgs.preorder.integration.client.setting.buparam.IBUParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class BUParamServiceImpl implements IBUParamService {

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Override
    public BaseResponse<String> getOrderNoMode(String buCode,String labCode) {
        String orderNoMode = Constants.BU_PARAM.ObjectNo.OrderNo.VALUE.DEFAULT;
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.ObjectNo.GROUP_CODE);
        buParamReq.setParamCode(Constants.BU_PARAM.ObjectNo.OrderNo.PARAM_CODE);
        buParamReq.setProductLineCode(buCode);
        buParamReq.setLabCode(labCode);
        List<BuParamValueRsp> buParamReqList = frameWorkClient.getBuParams(buParamReq);
        if(Func.isNotEmpty(buParamReqList)){
            orderNoMode = buParamReqList.stream().findFirst().get().getParamValue();
        }
        return BaseResponse.newSuccessInstance(orderNoMode);
    }
}
