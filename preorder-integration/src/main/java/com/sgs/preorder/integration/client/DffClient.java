package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.dff.facade.domain.req.DffAttrLanguageValueVO;
import com.sgs.dff.facade.domain.req.QueryDffLanguageValueReq;
import com.sgs.dff.facade.domain.rsp.BaseResponse;
import com.sgs.dff.facade.facadeService.IDffFacadeService;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.dto.dff.*;
import com.sgs.preorder.facade.model.info.EnquiryProductInfo;
import com.sgs.preorder.facade.model.info.EnquiryProductSampleInfo;
import com.sgs.preorder.facade.model.rsp.EnquiryProductSampleRsp;
import com.sgs.preorder.facade.model.rsp.MaterialConvertProductRsp;
import com.sgs.preorder.facade.model.rsp.MaterialConvertSampleRsp;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DffClient {
    private static final Logger logger = LoggerFactory.getLogger(DffClient.class);
    @Resource
    private InterfaceConfig interfaceConfig;
    @Resource
    private IDffFacadeService dffFacadeService;
    /**
     *
     * @param formIdSets
     * @return
     */
    public List<DffFormAttrDTO> getDffFormAttrByDffFormIdList(Set<String> formIdSets) {
        long startMillis = System.currentTimeMillis();
        try {
            String dffApiUrl = String.format("%s/DFFV2Api/dff/queryDffFormAttrByDffFormIDList", interfaceConfig.getBaseUrl());
            Map<String, Object> maps = Maps.newHashMap();
            maps.put("dffIDList", formIdSets);
            List<DffFormAttrDTO>dffFormAttrDTOS=HttpClientUtil.post(dffApiUrl, maps, DffFormAttrDTO.class);
            List<DffFormAttrDTO> dffFormAttrDTOList=Lists.newArrayList();
            dffFormAttrDTOS.forEach(dffFormAttrDTO->{
            	dffFormAttrDTO.setLanguageID(LanguageType.findCode(dffFormAttrDTO.getLanguageCode()).getLanguageId());
                dffFormAttrDTOList.add(dffFormAttrDTO);
                List<DffFormAttrDisplayDTO>dffFormAttrDisplayDTOS=dffFormAttrDTO.getDisplayNameMultLanguage();
                if (CollectionUtils.isNotEmpty(dffFormAttrDisplayDTOS)){
                    dffFormAttrDisplayDTOS=dffFormAttrDisplayDTOS.stream().filter(dffFormAttrDisplayDTO->{
                      return !StringUtils.equalsIgnoreCase(dffFormAttrDisplayDTO.getLanguageCode() ,LanguageType.English.getCode());
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(dffFormAttrDisplayDTOS)){
                        for (DffFormAttrDisplayDTO dffFormAttrDisplayDTO:dffFormAttrDisplayDTOS){
                            DffFormAttrDTO mutiLanguageDffFormAttrDisplayDTO=new DffFormAttrDTO();
                            try {
                                BeanUtils.copyProperties(mutiLanguageDffFormAttrDisplayDTO,dffFormAttrDTO);
                                mutiLanguageDffFormAttrDisplayDTO.setdFFAttrID(dffFormAttrDisplayDTO.getDffFormAttrId());
                                mutiLanguageDffFormAttrDisplayDTO.setDispalyName(dffFormAttrDisplayDTO.getDisplayName());
                                mutiLanguageDffFormAttrDisplayDTO.setLanguageCode(dffFormAttrDisplayDTO.getLanguageCode());
                                mutiLanguageDffFormAttrDisplayDTO.setLanguageID(LanguageType.findCode(dffFormAttrDisplayDTO.getLanguageCode()).getLanguageId());
                                dffFormAttrDTOList.add(mutiLanguageDffFormAttrDisplayDTO);
                            } catch (IllegalAccessException e) {
                                logger.error("DffClient.getDffFormAttrByDffFormIdList Error : {}", e.getMessage(), e);
                            } catch (InvocationTargetException e) {
                                logger.error("DffClient.getDffFormAttrByDffFormIdList Error : {}", e.getMessage(), e);
                            }

                        }
                    }
                }
            });
            return dffFormAttrDTOList;
        } catch (Exception e) {
            logger.error("DffClient.getDffFormAttrByDffFormIdList Error : {}", e.getMessage(), e);
        }finally {
            logger.info("DffClient.getDffFormAttrByDffFormIdList 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return null;
    }

    public String getDffNameByID(String dffFormID) {
        String dffName = "";
        String dffApiUrl = String.format("%s/DFFV2Api/dff/queryDffFormNoPage", interfaceConfig.getBaseUrl());
        Map<String, Object> maps = Maps.newHashMap();
        maps.put("id", dffFormID);
        try {
            String jsonStr=HttpClientUtil.post(dffApiUrl, maps);
            logger.info("getDffNameByID call {}  result is {}",dffApiUrl,jsonStr);
            List<DffFormDTO> dffFormAttrDTOS=JSON.parseArray(jsonStr,DffFormDTO.class);
            if (CollectionUtils.isNotEmpty(dffFormAttrDTOS)) {
                dffName = dffFormAttrDTOS.get(0).getName();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return dffName;
        }
        return dffName;

    }

    public List<DffFormComfirmDTO> getDffFormConfirmIdBatchByIds(Set<String> dffFormIds) {
        long startMillis = System.currentTimeMillis();
        List<DffFormComfirmDTO> dffFormComfirmDTOs=Lists.newArrayList();
        try {
            /**
             * 根据formID获取dff attr信息
             */
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("dffIDList", dffFormIds);
            String dffApiUrl = String.format("%s/DFFV2Api/dff/queryDffFormComfirmIdBatchByIds", interfaceConfig.getBaseUrl());
            String strResult=HttpClientUtil.postJson(dffApiUrl, map);
            JSONObject jsonObject = JSONObject.parseObject(strResult);
            dffFormComfirmDTOs =JSONObject.parseArray(jsonObject.getString("rows"),DffFormComfirmDTO.class);

        } catch (Exception e) {
            logger.error("DffClient.getDffFormComfirmIdBatchByIds Error : {}", e.getMessage(), e);
        }finally {
            logger.info("DffClient.getDffFormComfirmIdBatchByIds 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return dffFormComfirmDTOs;
    }

    public List<DFFFormRspDTO> queryDFF(String dffFormId,Integer languageId){
        List<DFFFormRspDTO> dffFormRspDTOS = Lists.newArrayList();
        try{
            Map<String,Object> params = Maps.newHashMap();
            params.put("id",dffFormId);
            params.put("languageID",languageId==null?1:languageId);
            LanguageType code = LanguageType.findLanguageId(languageId);
            params.put("languageCode",code!=null?code.getCode():null);
            String queryURL = String.format("%s/DFFV2Api/dff/queryDff",interfaceConfig.getBaseUrl());
            dffFormRspDTOS = HttpClientUtil.post(queryURL, params, new TypeReference<List<DFFFormRspDTO>>() {});
        }catch (Exception e){
            logger.error("query DFF ,DFFFormID:{} error",dffFormId,e);
        }
        return dffFormRspDTOS;

    }


    public List<DffAttrLanguageValueVO> queryAttrLanguageValue(String dffFormID, Integer languageId, Integer otherLanguageId, List<DffAttrLanguageValueVO> dffAttrLanguageValueVOList) {
        QueryDffLanguageValueReq objQueryDffLanguageValueReq=new QueryDffLanguageValueReq();
        objQueryDffLanguageValueReq.setSgsToken(SecurityUtil.getSgsToken());
        objQueryDffLanguageValueReq.setLanguageCode(LanguageType.findLanguageId(languageId).getCode());
        objQueryDffLanguageValueReq.setOtherLanguageCode(LanguageType.findLanguageId(otherLanguageId).getCode());
        objQueryDffLanguageValueReq.setDffFormId(dffFormID);
        objQueryDffLanguageValueReq.setDffAttrLanguageValueVOList(dffAttrLanguageValueVOList);
        logger.info("call dff queryAttrLanguageValue,req:{}",JSON.toJSONString(objQueryDffLanguageValueReq));
        BaseResponse<List<DffAttrLanguageValueVO>> baseResponse=dffFacadeService.queryAttrLanguageValue(objQueryDffLanguageValueReq);
        logger.info("call dff queryAttrLanguageValue,rsp:{}",JSON.toJSONString(baseResponse));
        if(baseResponse==null||baseResponse.getStatus()!=200){
            return Lists.newArrayList();
        }
        return baseResponse.getData();

    }

    /**
     * GPO2-7608 CommonService 提供接口获取 BU + dff 信息
     * @param dffFormId formId 发包的formId
     * @param hostBuCode hostBuCode 发包方BU
     * @param execFormBuCode execFormBuCode不为空时优先查接包dff信息，如果没有则找发包dff信息
     * @return
     */
    public List<DffSimpleDTO> querySimpleDff(String dffFormId, String hostBuCode, String execFormBuCode){
        List<DffSimpleDTO> dffSimpleDTOS = Lists.newArrayList();
        try{
            Map<String,Object> params = Maps.newHashMap();
            params.put("formId",dffFormId);
            params.put("hostBuCode",hostBuCode);
            params.put("execFormBuCode",execFormBuCode);
            // 查询出所有的 中英文 模板信息
            params.put("languageCode", "EN,CHI");
            String queryURL = String.format("%s/DFFV2Api/dff/querySimpleDff",interfaceConfig.getBaseUrl());
            String strResult=HttpClientUtil.postJson(queryURL, params);
            JSONObject jsonObject = JSONObject.parseObject(strResult);
            dffSimpleDTOS =JSONObject.parseArray(jsonObject.getString("data"),DffSimpleDTO.class);

        }catch (Exception e){
            logger.error("query DFF ,DFFFormID:{} error",dffFormId,e);
        }
        return dffSimpleDTOS;
    }

    public BaseResponse<EnquiryProductSampleRsp> customerFieldDataConvert(List<CustomerFieldConvertReq> customerFieldConvertReq){
        EnquiryProductSampleRsp enquiryProductSampleRsp = new EnquiryProductSampleRsp();
        try{
            String url = String.format("%s/DFFV2Api/dffCustomerFieldConfig/customerFieldDataConvert",interfaceConfig.getBaseUrl());
            String strResult=HttpClientUtil.postJson(url, customerFieldConvertReq);
            JSONObject jsonObject = JSONObject.parseObject(strResult);
            if(jsonObject.containsKey("result") && Func.isNotEmpty(jsonObject.get("result"))){
                JSONArray resultArray = JSONArray.parseArray(Func.toJson(jsonObject.get("result")));
                if(Func.isNotEmpty(resultArray)){
                    JSONObject resultJO = JSONObject.parseObject(Func.toJson(resultArray.get(0)));
                    if(resultJO.containsKey("product")){
                        MaterialConvertProductRsp materialConvertProductRsp = JSONObject.parseObject(Func.toJson(resultJO.get("product")), MaterialConvertProductRsp.class);
                        EnquiryProductInfo product = JSONObject.parseObject(Func.toJson(resultJO.get("product")), EnquiryProductInfo.class);
                        product.setDffFormID(materialConvertProductRsp.getDffFormID());
                        product.setdFFFormID(materialConvertProductRsp.getDffFormID());
                        enquiryProductSampleRsp.setProduct(product);
                    }
                    List<EnquiryProductSampleInfo> enquiryProductSampleInfoList = new ArrayList<>();
                    if(resultJO.containsKey("sampleList")){
                        String sampleListStr = Func.toJson(resultJO.get("sampleList"));
                        List<MaterialConvertSampleRsp> materialConvertSampleRspList = JSONObject.parseArray(sampleListStr, MaterialConvertSampleRsp.class);
                        for (MaterialConvertSampleRsp materialConvertSampleRsp : materialConvertSampleRspList) {
                            EnquiryProductSampleInfo enquiryProductSampleInfo = JSONObject.parseObject(Func.toJson(materialConvertSampleRsp), EnquiryProductSampleInfo.class);
                            enquiryProductSampleInfo.setExternalSampleId(materialConvertSampleRsp.getMaterialId());
                            enquiryProductSampleInfo.setDffFormID(materialConvertSampleRsp.getDffGridId());
                            enquiryProductSampleInfo.setdFFFormID(materialConvertSampleRsp.getDffGridId());
                            enquiryProductSampleInfoList.add(enquiryProductSampleInfo);
                        }
                    }
                    enquiryProductSampleRsp.setProductSamples(enquiryProductSampleInfoList);
                }
            }
        }catch (Exception e){
            logger.error("customerFieldDataConvert:{} error", Func.toJson(customerFieldConvertReq),e);
        }
        return BaseResponse.newSuccessInstance(enquiryProductSampleRsp);
    }
}
