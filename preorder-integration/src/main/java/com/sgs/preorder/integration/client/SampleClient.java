package com.sgs.preorder.integration.client;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.facade.SampleFacade;
import com.sgs.otsnotes.facade.model.info.sample.TestSampleDTO;
import com.sgs.otsnotes.facade.model.req.OrderSubContractReq;
import com.sgs.preorder.core.constants.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SampleClient {
    private static final Logger logger = LoggerFactory.getLogger(SampleClient.class);
    @Autowired
    private SampleFacade sampleFacade;

    /**
     *
     * @param orderNo
     * @param subContractNo
     * @return
     */
    public List<TestSampleDTO> getSubContractOriginalSampleList(String orderNo, String subContractNo, Boolean convertOriginalSample, String productLineCode){
        OrderSubContractReq reqObject = new OrderSubContractReq();
        reqObject.setRequestId(Constants.getRequestId());
        reqObject.setOrderNo(orderNo);
        reqObject.setSubContractNo(subContractNo);
        reqObject.setConvertOriginalSample(convertOriginalSample);
        reqObject.setProductLineCode(productLineCode);
        try {
            BaseResponse<List<TestSampleDTO>> rspResult = sampleFacade.getSubContractOriginalSampleList(reqObject);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("SampleClient.getSubContractOriginalSampleList orderNo:{}获取SubContract({})信息异常：{}.", orderNo, subContractNo, ex.getMessage());
        }
        return null;
    }
}
