package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.BeanHelper;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.dto.trims.MultiLanguageWarningDTO;
import com.sgs.preorder.facade.model.dto.trims.TrimsWarningDTO;
import com.sgs.preorder.facade.model.req.QueryCareLabelReq;
import com.sgs.preorder.facade.model.req.trims.CareLabelReq;
import com.sgs.preorder.facade.model.req.trims.WarningReq;
import com.sgs.preorder.facade.model.rsp.QueryCareLabelRsp;
import com.sgs.preorder.facade.model.rsp.trims.WarningRep;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TrimsClient {
    private static final Logger logger = LoggerFactory.getLogger(TrimsClient.class);

    @Autowired
    private InterfaceConfig interfaceConfig;

    public List<QueryCareLabelRsp> queryCareLabel(QueryCareLabelReq queryCareLabelReq) {
        try {
            String trimsApi = String.format("%s/queryCareLabel", interfaceConfig.getTrimsApiUrl());
            String jsonStr = HttpClientUtil.postJson(trimsApi, queryCareLabelReq);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }

            return JSONObject.parseArray(jsonStr, QueryCareLabelRsp.class);
        } catch (Exception ex){
            logger.error("TrimsClient.queryCareLabel 信息异常.", ex);
        }
        return null;
    }
    /**
     * 调用Trims接口查询Warning
     * @return
     */
    public List<WarningRep> getTrimsWarning(WarningReq warningDto){
        //Trims接口CustomerCode和CustomerType必须一起传
        List<WarningRep> warningDTOs=Lists.newArrayList();
        if(warningDto!=null){
            if(StringUtils.isBlank(warningDto.getCustomerCode())){
                if(!"General".equals(warningDto.getWarningType())){
                    return new ArrayList<>();//没有客户则返回空列表
                }else{
                    warningDto.setCustomerType(null);
                }
            }
            logger.info("Interface QueryWarning parame["+warningDto.toString()+"]");
            try {
                Map<String, Object> paramsMaps = BeanHelper.toHashMap(warningDto);
                String customerApiUrl = String.format("%s/queryWarning", interfaceConfig.getTrimsApiUrl());
                List<TrimsWarningDTO> list =HttpClientUtil.sendPost(customerApiUrl, warningDto, TrimsWarningDTO.class);
                if(CollectionUtils.isNotEmpty(list)){
                    for(TrimsWarningDTO trimsWarningDTO:list){
                        WarningRep warning=new WarningRep();
                        if (warningDto.getLanguageID()== LanguageType.English.getLanguageId()) {
                            BeanUtils.copyProperties(trimsWarningDTO, warning);
                            warning.setLanguageId(LanguageType.English.getLanguageId());
                            warningDTOs.add(warning);
                        }else{
                            List<MultiLanguageWarningDTO> otherLanguageItems=trimsWarningDTO.getOtherLanguageItems();
                            if (CollectionUtils.isNotEmpty(otherLanguageItems)) {
                                MultiLanguageWarningDTO multiLanguageWarningDTO=otherLanguageItems.stream().filter(dto->{
                                    return warningDto.getLanguageID()==dto.getLanguageId();
                                }).findFirst().orElse(null);
                                if (multiLanguageWarningDTO!=null) {
                                    warning.setLanguageId(warningDto.getLanguageID());
                                    warning.setWarningId(trimsWarningDTO.getWarningId());
                                    warning.setWarningDescription(multiLanguageWarningDTO.getMultiWarningDescription());
                                    warning.setWarningType(trimsWarningDTO.getWarningType());
                                    warningDTOs.add(warning);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error(e.getMessage(),e);
            }
        }
        return warningDTOs;
    }


    public Object careLabel(CareLabelReq care){
        List<Map> result  = Lists.newArrayList();

        logger.info("Staring  invoike  careLabel method.....");
        if (care == null) {
            return result;
        }
        try {
            Map<String, String> paramsMaps = BeanHelper.toHashMapString(care);
            String trimsApiUrl = String.format("%s/queryCareLabel", interfaceConfig.getTrimsApiUrl());
            result = HttpClientUtil.sendPost(trimsApiUrl, paramsMaps,Map.class);
            if(CollectionUtils.isEmpty(result)){
                return result;
            }

        } catch (Exception e) {
            // TODO: handle exception
            logger.error(e.getMessage(),e);
            return result;
        }
        if (result.isEmpty() || result.get(0) == null || result.get(0).get("careLabelDetail") == null) {
            return null;
        }
        List<Map<String, Object>> countryList = ((List<Map<String, Object>>) result.get(0).get("careLabelDetail"));
        for (Map<String, Object> map : countryList) {
            map.put("checked", false);
        }
        //构建排序器
        Ordering<Map<String, Object>> orderingBig = new Ordering<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> left, Map<String, Object> right) {
                if (!left.containsKey("sequenceNo") || !right.containsKey("sequenceNo")) {
                    return 0;
                }
                Integer no1 = left.get("sequenceNo") == null ? 0 : Integer.valueOf(left.get("sequenceNo").toString());
                Integer no2 = right.get("sequenceNo") == null ? 0 : Integer.valueOf(right.get("sequenceNo").toString());
                return no1 - no2;
            }
        };
        countryList = orderingBig.sortedCopy(countryList);
        //把careLabelDetailsDescription放到countryList中，不改变先前的代码结构(Tiger_Wang)
        Map<String, Object> careLabelDescMap = new HashMap<String, Object>();
        Map<String, Object> otherLanguageItemsMap = new HashMap<String, Object>();
        if(!StringUtils.isBlank(care.getCareLabelDetailDescFlag()) && care.getCareLabelDetailDescFlag().equals("true")){
            if(!ObjectUtils.isEmpty(result.get(0).get("careLabelDetailsDescription"))){
                careLabelDescMap.put("careLabelDetailsDescription", result.get(0).get("careLabelDetailsDescription"));
            }else{
                careLabelDescMap.put("careLabelDetailsDescription", "");
            }
            countryList.add(careLabelDescMap);

            // POSL-3175 careLabel 中文
            if (!ObjectUtils.isEmpty(result.get(0).get("otherLanguageItems"))) {
                otherLanguageItemsMap.put("otherLanguageItems", result.get(0).get("otherLanguageItems"));
            } else {
                otherLanguageItemsMap.put("otherLanguageItems", "");
            }
            countryList.add(otherLanguageItemsMap);
        }
        return countryList;
    }

    public Object queryCountry(){
        String trimsApiUrl = String.format("%s/queryCareLabelRegionCountry", interfaceConfig.getTrimsApiUrl());
        List<Map> jsonStr = null;
        try {
            jsonStr = HttpClientUtil.sendPost(trimsApiUrl, new Object(),Map.class);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return jsonStr;
    }

}
