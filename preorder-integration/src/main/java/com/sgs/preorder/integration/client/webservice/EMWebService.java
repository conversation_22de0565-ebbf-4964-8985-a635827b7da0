
package com.sgs.preorder.integration.client.webservice;

import javax.xml.namespace.QName;
import javax.xml.ws.*;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "EMWebService", targetNamespace = "http://tempuri.org/", wsdlLocation = "http://10.205.1.101/EMWebService/EMWebService.asmx?WSDL")
public class EMWebService
    extends Service
{
    private final static URL EMWEBSERVICE_WSDL_LOCATION;
    private final static WebServiceException EMWEBSERVICE_EXCEPTION;
    private final static QName EMWEBSERVICE_QNAME = new QName("http://tempuri.org/", "EMWebService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://10.205.1.101/EMWebService/EMWebService.asmx?WSDL");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        EMWEBSERVICE_WSDL_LOCATION = url;
        EMWEBSERVICE_EXCEPTION = e;
    }

    public EMWebService() {
        super(__getWsdlLocation(), EMWEBSERVICE_QNAME);
    }

    public EMWebService(WebServiceFeature... features) {
        super(__getWsdlLocation(), EMWEBSERVICE_QNAME, features);
    }

    public EMWebService(URL wsdlLocation) {
        super(wsdlLocation, EMWEBSERVICE_QNAME);
    }

    public EMWebService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, EMWEBSERVICE_QNAME, features);
    }

    public EMWebService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public EMWebService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns EMWebServiceSoap
     */
    @WebEndpoint(name = "EMWebServiceSoap")
    public EMWebServiceSoap getEMWebServiceSoap() {
        return super.getPort(new QName("http://tempuri.org/", "EMWebServiceSoap"), EMWebServiceSoap.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns EMWebServiceSoap
     */
    @WebEndpoint(name = "EMWebServiceSoap")
    public EMWebServiceSoap getEMWebServiceSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "EMWebServiceSoap"), EMWebServiceSoap.class, features);
    }

    /**
     *
     * @return
     *     returns EMWebServiceSoap
     */
    @WebEndpoint(name = "EMWebServiceSoap12")
    public EMWebServiceSoap getEMWebServiceSoap12() {
        return super.getPort(new QName("http://tempuri.org/", "EMWebServiceSoap12"), EMWebServiceSoap.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns EMWebServiceSoap
     */
    @WebEndpoint(name = "EMWebServiceSoap12")
    public EMWebServiceSoap getEMWebServiceSoap12(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "EMWebServiceSoap12"), EMWebServiceSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (EMWEBSERVICE_EXCEPTION!= null) {
            throw EMWEBSERVICE_EXCEPTION;
        }
        return EMWEBSERVICE_WSDL_LOCATION;
    }

}
