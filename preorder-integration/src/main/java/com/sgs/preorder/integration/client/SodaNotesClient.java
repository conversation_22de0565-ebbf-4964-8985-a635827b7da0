package com.sgs.preorder.integration.client;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.common.ResponseCode;
import com.sgs.preorder.facade.model.req.CancelSubContractReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: Joke.wang
 * @Date: 2022/3/31 16:06
 */
@Component
@Slf4j
public class SodaNotesClient {

    @Autowired
    private InterfaceConfig interfaceConfig;

    public CustomResult updateSubContractData(CancelSubContractReq reqObject) {
        try {
            String getSampleURL = String.format("%s/otsnotes2api/subcontract/updateSubContractData", interfaceConfig.getBaseUrl());
            BaseResponse baseResponse = HttpClientUtil.doPost(getSampleURL, reqObject, BaseResponse.class);
            if (ResponseCode.SUCCESS.getCode() == baseResponse.getStatus()) {
                return CustomResult.newSuccessInstance();
            }
        } catch (Exception ex) {
            log.error("SodaNotesClient.updateSubContractData 信息异常：{}.", ex);
        }
        return CustomResult.newSuccessInstance().fail("updateSubContractData 接口调用异常");
    }
}
