package com.sgs.preorder.integration.client;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.TestLineInstanceFacade;
import com.sgs.otsnotes.facade.model.dto.JobTestLineForEmDto;
import com.sgs.otsnotes.facade.model.req.TestLineBreakDownReq;
import com.sgs.otsnotes.facade.model.req.TestLineInstanceJobReq;
import com.sgs.otsnotes.facade.model.rsp.TestLineBreakDownRsp;
import com.sgs.preorder.core.constants.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TestLineInstanceClient {
    private static final Logger logger = LoggerFactory.getLogger(TestLineInstanceClient.class);
    @Autowired
    private TestLineInstanceFacade testLineInstanceFacade;

    /**
     *
     * @param jobNos
     * @return
     */
    public List<JobTestLineForEmDto> getTestLineInstanceByJobNoForEm(List<String> jobNos){
        TestLineInstanceJobReq jobReq = new TestLineInstanceJobReq();
        jobReq.setRequestId(Constants.getRequestId());
        jobReq.setJobNos(jobNos);
        try {
            BaseResponse<List<JobTestLineForEmDto>> rspResult = testLineInstanceFacade.getTestLineInstanceByJobNoForEm(jobReq);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("TestLineInstanceClient.getTestLineInstanceByJobNoForEm({})信息异常：{}.", jobNos, ex.getMessage(), ex);
        }
        return null;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public BaseResponse<TestLineBreakDownRsp> getTestLineBreakDownInfoList(TestLineBreakDownReq reqObject){
        try {
            if(Func.isEmpty(reqObject.getOrderNo())){
                return BaseResponse.newFailInstance("param.miss",new Object[]{"orderNo"});
            }
            return testLineInstanceFacade.getTestLineBreakDownInfoList(reqObject);
        }catch (Exception ex){
            logger.error("TestLineInstanceClient.getTestLineBreakDownInfoList({})信息异常：{}.", reqObject.getOrderNo(), ex.getMessage(), ex);
        }
        return null;
    }
}
