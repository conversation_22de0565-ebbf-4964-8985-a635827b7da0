package com.sgs.preorder.integration.client;

import com.google.common.collect.Maps;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.Constants;
import com.sgs.preorder.core.util.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * User Management
 * <AUTHOR>
 * @date 2021/1/5 18:04
 */
@Component
public class UserManagementClient {
    private static final Logger logger = LoggerFactory.getLogger(UserManagementClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;

    /**
     * 根据 resource code 判断当前用户是否有权限访问该资源
     * @param token
     * @param resource
     * @return
     */
    public Boolean isPermission(String token,String resource){
        String url = String.format("%s/UserManagementApi/resource/querySystemResourceBySystemName/"+token, interfaceConfig.getBaseUrl());
        Map<String, String> paramsMap = Maps.newHashMap();
        paramsMap.put("systemName", Constants.GPO_SYSTEM_NAME);
        logger.info(" isPermission   url:  "+ url);
        try {
            String permissionStr = HttpClientUtil.requestGet(url,paramsMap,5000);
            logger.info("permissionStr:  "+ permissionStr);
            return Func.isNotEmpty(permissionStr) && permissionStr.contains(resource);
        }catch (Exception e){
        }
        return false;
    }

}
