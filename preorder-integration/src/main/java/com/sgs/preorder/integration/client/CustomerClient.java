package com.sgs.preorder.integration.client;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.rpc.RpcException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.utils.HttpClient;
import com.sgs.core.utils.HttpClientPool;
import com.sgs.customer.rsp.QueryCustomerHostingSalesRsp;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.BeanHelper;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.dto.customer.*;
import com.sgs.preorder.facade.model.dto.dm.CustomerDepartmentDTO;
import com.sgs.preorder.facade.model.dto.dm.CustomerDepartmentFacadeDTO;
import com.sgs.preorder.facade.model.info.customer.CustomerWeightInfo;
import com.sgs.preorder.facade.model.req.CustomerTatReq;
import com.sgs.preorder.facade.model.req.customer.*;
import com.sgs.preorder.facade.model.rsp.PotentialCustomerRsp;
import com.sgs.preorder.facade.model.rsp.customer.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CustomerClient {
    private static final Logger logger = LoggerFactory.getLogger(CustomerClient.class);

    @Autowired
    private InterfaceConfig interfaceConfig;

    /**
     *
     * @param paramMaps
     * @return
     */
    public PotentialCustomerRsp addCustomerInfo(Map<String, Object> paramMaps) {
        try {
            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/savePotentialCustomer", interfaceConfig.getBaseUrl());
            PotentialCustomerRsp potentialCustomerRsp = HttpClientUtil.sendPost(customerApiUrl, paramMaps, PotentialCustomerRsp.class);
            logger.info("CustomerClient.addCustomerInfo rep : {}",potentialCustomerRsp);
            return potentialCustomerRsp;
        } catch (Exception ex) {
            logger.error("CustomerClient.addCustomerInfo error : {}", ex.getMessage(), ex);
        }
        return null;
    }

    /**
     *
     * @param reqParams
     * @return
     */
    public CustomerExtNewListRsp getCustomerDetailInfo(CustomerDetailReq reqParams){
        CustomerDetailRsp customerDetail = this.getCustomerDetailList(reqParams);
        if (customerDetail == null){
            logger.info("CustomerDetail_order_{}，对象为空或异常.", reqParams.getOrderNo());
            return null;
        }
        List<CustomerExtNewListRsp> customers = customerDetail.getCustomerExtNewList();
        if (customers == null || customers.isEmpty()){
            logger.info("CustomerExtNewList_order_{}，对象为空或异常.", reqParams.getOrderNo());
            return null;
        }
        return customers.get(0);
    }



    /**
     *
     * @param reqParams
     * @return
     */
    public CustomerDetailRsp getCustomerDetailList(CustomerDetailReq reqParams){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            Map<String, Object> paramsMaps = BeanHelper.toHashMap(reqParams);

            /*paramsMaps = Maps.newHashMap();
            paramsMaps.put("customerId", reqParams.getCustomerId());
            paramsMaps.put("buCode", reqParams.getBuCode());
            paramsMaps.put("locationCode", reqParams.getLocationCode());*/

            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/detail/query", interfaceConfig.getBaseUrl());

            String jsonStr = HttpClientUtil.post(customerApiUrl, paramsMaps);
            logger.info("customerDetail_order_{}，rspResult：{}.", reqParams.getOrderNo(), jsonStr);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            return JSON.parseObject(jsonStr, CustomerDetailRsp.class);
        }catch (Exception ex){
            logger.error("CustomerClient.getCustomerDetailInfo 获取订单(customerDetail_order_{})信息异常：{}.", reqParams.getOrderNo(), ex.getMessage(), ex);
        }finally {
            stopWatch.stop();
            logger.info("CustomerClient.getCustomerDetailInfo 订单(customerDetail_order_{})请求接口耗时：{}.", reqParams.getOrderNo(), stopWatch.getTime());
        }
        return null;
    }


    /**
     * 单独获取Ext customer的数据
     * @param reqParams
     * @return
     */
    public CustomerExtNewListRsp getCustomerExtInfoByBu(CustomerDetailReq reqParams){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            Map<String, Object> paramsMaps = BeanHelper.toHashMap(reqParams);

            /*paramsMaps = Maps.newHashMap();
            paramsMaps.put("customerId", reqParams.getCustomerId());
            paramsMaps.put("buCode", reqParams.getBuCode());
            paramsMaps.put("locationCode", reqParams.getLocationCode());*/

            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/queryCustomerExtInfoByBu", interfaceConfig.getBaseUrl());

            String jsonStr = HttpClientUtil.post(customerApiUrl, paramsMaps);
            //logger.debug("getCustomerExtInfoByBu reqParams_{}，rspResult：{}.", paramsMaps, jsonStr);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }

            JSONObject parseObject = JSONObject.parseObject(jsonStr);
            String rows = parseObject.getString("rows");
            List<CustomerExtNewListRsp> customerExtNewListRsps = JSONArray.parseArray(rows, CustomerExtNewListRsp.class);
            if(customerExtNewListRsps == null || customerExtNewListRsps.isEmpty()){
                logger.info("getCustomerExtInfoByBu reqParams_{} 接口获取数据Rows为null",paramsMaps);
                return null;
            }
            return customerExtNewListRsps.get(0);
        }catch (Exception ex){
            logger.error("CustomerClient.getCustomerExtInfoByBu 获取订单(customerDetail_order_{})信息异常：{}.", reqParams.getOrderNo(), ex.getMessage(), ex);
        }finally {
            stopWatch.stop();
            logger.info("CustomerClient.getCustomerExtInfoByBu 订单(customerDetail_order_{})请求接口耗时：{}.", reqParams.getOrderNo(), stopWatch.getTime());
        }
        return null;
    }

    /**
     *
     * @param reqParams
     * @return
     */
    public List<ContactAddressCustomerRsp> getContactAddressCustomerList(QueryContactAddressCustomerReq reqParams){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            Map<String, Object> paramsMaps = BeanHelper.toHashMap(reqParams);
            String contactNameParam = (String)paramsMaps.get("contactName");
            if (StringUtils.isNotBlank(contactNameParam)) {
                paramsMaps.remove("contactName");
            }
            /*paramsMaps = Maps.newHashMap();
            paramsMaps.put("customerId", reqParams.getCustomerId());
            paramsMaps.put("buCode", reqParams.getBuCode());
            paramsMaps.put("locationCode", reqParams.getLocationCode());*/

            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/contactAddress/query", interfaceConfig.getBaseUrl());

            String jsonStr = HttpClientUtil.post(customerApiUrl, paramsMaps);
            if (StringUtils.isEmpty(jsonStr)){
                return Lists.newArrayList();
            }
            JSONObject parseObject = JSONObject.parseObject(jsonStr);
            String rows = parseObject.getString("rows");
            if (StringUtils.isEmpty(rows)){
                return Lists.newArrayList();
            }
            List<ContactAddressCustomerRsp> contactAddressCustomerRspList = JSONArray.parseArray(rows, ContactAddressCustomerRsp.class);
            return contactAddressCustomerRspList;
        }catch (Exception ex){
            logger.error("CustomerClient.getContactAddressCustomerList 信息异常：{}.", ex.getMessage(), ex);
        }finally {
            stopWatch.stop();
            logger.info("CustomerClient.getContactAddressCustomerList 请求接口耗时：{}.", stopWatch.getTime());
        }
        return null;
    }

    public BuyerAndAgentWeightDTO queryWeightByBuyerAndAgent(BuyerAndAgentWeightDTO reqDTO) {
        BuyerAndAgentWeightDTO resDTO = null;
        String buyerGroupCode = reqDTO.getBuyerGroupCode();
        String agentGroupCode = reqDTO.getAgentGroupCode();
        if(StringUtils.isBlank(buyerGroupCode) || StringUtils.isBlank(agentGroupCode)){
            logger.info("buyer或者agent code参数为空");
            return reqDTO;
        }
        Map<String,Object> params = new HashMap<>();
        params.put("agentGroupCode",agentGroupCode);
        params.put("buyerGroupCode",buyerGroupCode);
        String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/queryCustomerGroupConfigMatrix", interfaceConfig.getBaseUrl());
        try {
            String jsonStr=HttpClientUtil.doPost(customerApiUrl, params);
            if(StringUtils.isNotEmpty(jsonStr)){
                try{
                    JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                    String array = jsonObject.getString("result");
                    List<BuyerAndAgentWeightDTO> list = JSONArray.parseArray(array, BuyerAndAgentWeightDTO.class);
                    if(list!=null && list.size()>0){
                        resDTO = list.get(0);
                    }else{
                        return reqDTO;
                    }
                }catch (Exception e){
                    logger.info("转换接口返回数据异常");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return resDTO;
    }

    public List<CustomerDepartmentDTO> queryDepartmentList(CustomerDepartmentDTO reqDTO) {
        List<CustomerDepartmentDTO> customerDepartmentDTOS = Lists.newArrayList();
        Map<String, Object> map = Maps.newHashMap();
        map.put("accountID", reqDTO.getAccountID());
        String customerApiUrl = String.format("%s/CustomerApi/customerDepartment/queryDepartmentList", interfaceConfig.getBaseUrl());
        try {
            List<CustomerDepartmentFacadeDTO> customerDepartmentForCustomerDTOS=HttpClientUtil.post(customerApiUrl,map,CustomerDepartmentFacadeDTO.class);
            if(CollectionUtils.isNotEmpty(customerDepartmentForCustomerDTOS)){
                CustomerDepartmentDTO customerDepartmentDTO = null;
                for (CustomerDepartmentFacadeDTO customerDepartmentForCustomerDTO : customerDepartmentForCustomerDTOS) {
                    customerDepartmentDTO = new CustomerDepartmentDTO();
                    customerDepartmentDTO.setDepartmentCode(customerDepartmentForCustomerDTO.getDepartmentCode());
                    customerDepartmentDTO
                            .setDepartmentDescription(customerDepartmentForCustomerDTO.getDepartmentDestription());
                    customerDepartmentDTOS.add(customerDepartmentDTO);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }

        return customerDepartmentDTOS;
    }

    /**
     *
     * @param paramsMaps
     * @return
     */
    public List<ContactAddressDTO> getCustomerList(Map<String, Object> paramsMaps){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            List<ContactAddressDTO> contactAddressDTOs = Lists.newArrayList();
            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/customerList/query", interfaceConfig.getBaseUrl());

            Map<String, Object> head = new HashMap<>();
            head.put("Content-Type", "application/json;charset=UTF-8");
            String body = JSONObject.toJSONString(paramsMaps);
            org.apache.http.HttpEntity entity = HttpClientPool.httpPost(
                    customerApiUrl, head, body);
            String jsonStr = null;
            if (entity != null) {
                try {
                    jsonStr = EntityUtils.toString(entity, "UTF-8");
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }

            if (StringUtils.isEmpty(jsonStr)){
                return Lists.newArrayList();
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            String result = jsonObject.getString("result");
            contactAddressDTOs = JSONObject.parseArray(result, ContactAddressDTO.class);
            return contactAddressDTOs;
        }catch (Exception ex){
            logger.error(ex.getMessage(),ex);
        }finally {
            stopWatch.stop();
        }
        return null;
    }

    /**
     * 单独获取Ext customer的数据
     * @return
     */
    public  List<CustomerExtDTO> getCustomerExt(String customerId, String locationCode,String buCode){

        String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/customerExt/query?customerID=" + customerId, interfaceConfig.getBaseUrl());
        List<CustomerExtDTO> resultList = new ArrayList<>();
        String result = HttpClient.httpGet(customerApiUrl);
        if (StringUtils.isNotBlank(result)) {
            List<CustomerExtDTO> customerExtDTOs = JSONObject.parseArray(result, CustomerExtDTO.class);
            if (CollectionUtils.isNotEmpty(customerExtDTOs)) {
                List<CustomerExtDTO> customerExtLocationList = customerExtDTOs.stream().filter(customerExtDTO -> StringUtils.equals(customerExtDTO.getExtBuCode(), buCode)
                        && StringUtils.equals(customerExtDTO.getLocationCode(), locationCode)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(customerExtLocationList)) {
                    return  customerExtLocationList;
                } else {
                    if (!StringUtils.equals(locationCode, "HK")) {
                        List<CustomerExtDTO> customerExtCNList = customerExtDTOs.stream().filter(customerExt -> {
                            return StringUtils.equals(customerExt.getExtBuCode(), buCode)
                                    && StringUtils.equals(customerExt.getLocationCode(), "CN");
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(customerExtCNList)) {
                            return customerExtCNList;
                        }
                    }
                }
            }
        }
        return resultList;
    }
    /**
     *
     * @param reqParams
     * @return
     */
    public CustomerWeightInfo getCustomerWeightInfo(BuyerAndAgentWeightReq reqParams){
        try {
            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/queryCustomerGroupConfigMatrix", interfaceConfig.getBaseUrl());

            CustomerWeightRsp rspObject = HttpClientUtil.sendPost(customerApiUrl, reqParams, new TypeReference<CustomerWeightRsp>(){});
            if (rspObject == null){
                return null;
            }
            List<CustomerWeightInfo> customerWeights = rspObject.getResult();
            if (customerWeights == null || customerWeights.isEmpty()){
                return null;
            }
            return customerWeights.get(0);
        }catch (Exception ex){
            logger.error("CustomerClient.getCustomerWeightInfo 获取queryCustomerGroupConfigMatrix信息异常：{}.", ex.getMessage(), ex);
        }
        return null;
    }

    /*
        查询客户主信息
     */
    public List<CustomerBaseDTO> queryBaseCustomerList(CustomerBaseQueryReq reqParams){
        List<CustomerBaseDTO> customerBaseList = Lists.newArrayList();

        String customerApiUrl = String.format("%s/CustomerApi/customer/PageInfo/queryCustomerList", interfaceConfig.getBaseUrl());
        logger.info("queryCustomerList:"+reqParams);
        try {
            String jsonStr=HttpClientUtil.postJson(customerApiUrl, reqParams);
            logger.info("queryBaseCustomerList返回值----------->>>>>>>>>>"+jsonStr);
            if (Func.isNotEmpty(jsonStr)){
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                String rows = jsonObject.getString("rows");
                customerBaseList = JSONArray.parseArray(rows,CustomerBaseDTO.class);
            }
        }catch (Exception e){
            logger.info("queryCustomerList失败！");
            e.printStackTrace();
        }
        return customerBaseList;
    }



    /**
     * 获取其他类型信息 对比最新数据
     * @param reqParams
     * @return
     */
    public List<ContactAddressDTO> queryCustomerList(CustomerListQueryReq reqParams){
        try {
            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/customerList/query", interfaceConfig.getBaseUrl());
            logger.info("CustomerClient.queryCustomerList 获取queryCustomerList信息请求参数：{}.",JSON.toJSONString(reqParams));
            CustomerListQueryRsp rspObject = HttpClientUtil.sendPost(customerApiUrl, reqParams, new TypeReference<CustomerListQueryRsp>(){});
            if (rspObject == null){
                return null;
            }
            List<ContactAddressDTO> customerWeights = rspObject.getResult();
            if (customerWeights == null || customerWeights.isEmpty()){
                return null;
            }
            logger.info("CustomerClient.queryCustomerList 获取queryCustomerList信息返回结果：{}.",JSON.toJSONString(customerWeights));
            return customerWeights;
        }catch (RpcException rpcException){
            logger.error("call CustomerApi error:{}",rpcException);
            throw new BizException("调用接口获取客户信息超时，请稍后重试!");
        } catch (Exception ex){
            logger.error("CustomerClient.queryCustomerList 获取queryCustomerList信息异常：{}.", ex.getMessage(), ex);
        }
        return null;
    }

    /**
     *
     * @param customerID
     * @return
     */
    public List<CustomerExtDTO> queryCustomerExt(String customerID){
        try {
            String customerApiUrl = String.format("%s/CustomerApi/preOrderCustomer/customerExt/query", interfaceConfig.getBaseUrl(), customerID);

            Map<String, String> paramMaps = Maps.newHashMap();
            paramMaps.put("customerID", customerID);
            String result = HttpClientUtil.sendGet(customerApiUrl, paramMaps);
            if (StringUtils.isBlank(result)) {
                return null;
            }
            List<CustomerExtDTO> customerExtDTOs = JSONObject.parseArray(result, CustomerExtDTO.class);
            if (CollectionUtils.isEmpty(customerExtDTOs)){
                return null;
            }
            return customerExtDTOs;
        }catch (Exception ex){
            logger.error("CustomerClient.queryCustomerExt 获取queryCustomerExt信息异常：{}.", ex.getMessage(), ex);
        }
        return null;
    }


    public List<CustomerDepartmentFacadeDTO> queryCustomerDepartmentDTOList(String accountID) {
        try {
            return HttpClientUtil.post(String.format("%s/CustomerApi/customerDepartment/queryDepartmentList", interfaceConfig.getBaseUrl()),
                    ImmutableMap.<String, Object>builder().put("accountID", accountID).build(),
                    CustomerDepartmentFacadeDTO.class);
        } catch (Exception e) {
            logger.error("CustomerClient.queryDepartmentList 获取queryDepartmentList信息异常：{}.", e.getMessage(), e);
        }
        return null;
    }


    /**
     * 查询客户下salesPerson
     * @param param
     * @return
     */
    public String queryCustomerHostingSales(Map<String,Object> param){
        String salesPerson = null;

        String customerApiUrl = String.format("%s/CustomerApi/hosting/queryCustomerHostingSales", interfaceConfig.getBaseUrl());
        logger.info("查询客户下sales信息url:"+customerApiUrl);
        logger.info("查询客户下sales信息参数"+param);
        try {
            String jsonStr=HttpClientUtil.doPost(customerApiUrl, param);
            //logger.info("查询客户下sales信息返回值----------->>>>>>>>>>"+jsonStr);
            if (Func.isNotEmpty(jsonStr)){
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                String rows = jsonObject.getString("rows");
                List<QueryCustomerHostingSalesRsp> queryCustomerHostingSalesRsps = JSONArray.parseArray(rows,QueryCustomerHostingSalesRsp.class);
                if (Func.isNotEmpty(queryCustomerHostingSalesRsps)){
                    String sales = queryCustomerHostingSalesRsps.get(0).getSalesPerson();
                    if (Func.isNotEmpty(sales)){
                        //处理数据
                        String[] salesArray = sales.split("\\\\");
                        if (salesArray.length > 0){
                            salesPerson = salesArray[1];
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.info("查询客户下sales失败！");
            e.printStackTrace();

        }
        return salesPerson;
    }

    /**
     * 基于客户查询TAT列表
     * @param customerTatReq
     * @return
     */
    public List<CustomerTatRes> queryCustomerTAT(CustomerTatReq customerTatReq){
        List<CustomerTatRes> customerTatList = Lists.newArrayList();
        if(Func.isEmpty(customerTatReq.getCustomerIds()) || Func.isEmpty(customerTatReq.getBuCode())
                || Func.isEmpty(customerTatReq.getLocationCode())){
                return customerTatList;
        }
        String url = String.format("%s/CustomerApi/preOrderCustomer/getCustomerServiceTypeTAT", interfaceConfig.getBaseUrl());
        try {
            logger.info("getCustomerServiceTypeTAT 入参：{}",JSON.toJSONString(customerTatReq));
            String jsonStr=HttpClientUtil.postJson(url,customerTatReq);
            logger.info("getCustomerServiceTypeTAT 返回：{}",jsonStr);
            if(Func.isNotEmpty(jsonStr)){
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                String data = jsonObject.getString("data");
                if(Func.isNotEmpty(data)){
                    customerTatList = JSONObject.parseArray(data, CustomerTatRes.class);
                }
            }
        }catch (Exception exception){
            exception.printStackTrace();
            logger.error(exception.getMessage());
        }
        return customerTatList;
    }

}
