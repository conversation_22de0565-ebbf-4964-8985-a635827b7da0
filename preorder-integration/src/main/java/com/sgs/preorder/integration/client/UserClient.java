package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.customer.facade.domain.hosting.SalesHostingDTO;
import com.sgs.customer.facade.domain.hosting.SalesHostingQueryVO;
import com.sgs.customer.facade.facadeService.ICustomerFacadeService;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.Constants;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.common.CSResult;
import com.sgs.preorder.facade.model.info.user.UserBuLocationLabInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.rsp.UserBuLocationLabRsp;
import com.sgs.user.facade.domain.dimention.*;
import com.sgs.user.facade.domain.userInfo.UserDTO;
import com.sgs.user.facade.domain.userInfo.UserVO;
import com.sgs.user.facade.domain.utils.BaseResponse;
import com.sgs.user.facade.facadeService.IUserFacadeService;
import com.sgs.usermanagement.req.QueryEmployeeInfoReq;
import com.sgs.usermanagement.req.QuerySalesDiscountReq;
import com.sgs.usermanagement.req.QueryUserInfoReq;
import com.sgs.usermanagement.rsp.QuerySalesDiscountRsp;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.util.List;
import java.util.Map;


@Component
public class UserClient {
    private static final Logger logger = LoggerFactory.getLogger(UserClient.class);
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private IUserFacadeService userFacadeService;
    @Autowired
    private ICustomerFacadeService customerFacadeService;

    /**
     *
     * @return
     */
    public UserLabBuInfo getUserLabBuInfo(){
        return this.getUserLabBuInfo(SystemContextHolder.getSgsToken());
    }

    /**
     *
     * @param token
     * @return
     */
    public UserLabBuInfo getUserLabBuInfo(String token){
        UserDimentionDTO userDimention = null;
        try{
            userDimention = userFacadeService.getUserDimention(token);
        }catch (Exception ex){
            logger.error("UserClient.getUserDimention 请求异常：", ex);
        }
        if (userDimention == null){
            return null;
        }
        UserLabBuInfo lab = new UserLabBuInfo();
        Laboratory userLab = userDimention.getLaboratory();
        if (userLab != null){
            lab.setLabId(userLab.getLabId());
            lab.setLabCode(userLab.getLabCode());
            lab.setLabName(userLab.getLabName());
            lab.setLabAddress(userLab.getLabAddress());
            lab.setOtherCode(userLab.getOtherCode());
        }
        Location location = userDimention.getLocation();
        if (location != null){
            lab.setLocationId(location.getLocationId());
            lab.setLocationCode(location.getLocationCode());
            lab.setLocationName(location.getLocationName());
        }
        ProductLine productLine = userDimention.getProductLine();
        if (productLine != null){
            lab.setProductLineId(productLine.getProductLineID());
            lab.setProductLineCode(productLine.getProductLineCode());
            lab.setProductLineName(productLine.getProductLineName());
            lab.setShortCode(productLine.getShortCode());
        }
        UserTeam team = userDimention.getTeam();
        if (team != null){
            lab.setTeamCode(team.getTeamCode());
            lab.setTeamName(team.getTeamName());

            lab.setProductTypeCode(team.getProductTypeCode());
            if (!StringUtils.isBlank(team.getProductTypePostfix())){
                lab.setPostfix(team.getProductTypePostfix().split(";"));
            }
        }
        LegalEntity legalEntity = userDimention.getBossLegalEntity();
        if (legalEntity != null){
            lab.setLegalEntityCode(legalEntity.getLegalEntityCode());
            //lab.setLegalEntityName(legalEntity.getLegalEntityName());
            lab.setOrganizationID(legalEntity.getOrganizationID());
            lab.setOrganizationName(legalEntity.getOrganizationName());
            //lab.setCountryCode(legalEntity.getCountryCode());
        }

        return lab;
    }

    /**
     *
     * @param token
     * @return
     */
    public UserBuLocationLabInfo getUserBuLocationLabInfo(String token){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            String userManagementUrl = String.format("%s%s/%s", interfaceConfig.getBaseUrl(), Constants.USER_MANAGEMENT.URL.QUERY_USER_BU_LOCATION_LAB_INFO, token);
            //logger.info("UserClient.getUserBuLocationLabInfo 请求地址：{}",userManagementUrl);
            UserBuLocationLabRsp rspUser = HttpClientUtil.sendGet(userManagementUrl, null, UserBuLocationLabRsp.class);
            if (rspUser == null){
                return null;
            }
            return rspUser.getResult();
        }catch (Exception ex){
            logger.error("UserClient.getUserBuLocationLabInfo 请求异常：", ex);
        }finally {
            stopWatch.stop();
            //logger.info("UserClient.getUserBuLocationLabInfo 请求接口耗时：{}.", stopWatch.getTime());
        }
        return null;
    }

    public PageInfo<SalesHostingDTO> querySalesHostingList(SalesHostingQueryVO queryVO){
        queryVO.setPage(1);
        queryVO.setRows(1001);
        if(StringUtils.isNotBlank(queryVO.getCustomerName())){
            queryVO.setRows(10);
        }
        PageInfo<SalesHostingDTO> salesHostingDTOPageInfo = new PageInfo<>();
        try {
            salesHostingDTOPageInfo = customerFacadeService.querySalesHostList(queryVO);

        }catch (Exception e){
            logger.error("查询hosting 列表异常");
            e.printStackTrace();
        }
        return salesHostingDTOPageInfo;
    }

    /**
     *
     * @param domainANDuser
     * @param password
     * @return
     */
    public UserDTO getUserLogin(String domainANDuser, String password){
        try {
            UserVO user = new UserVO();
            user.setUsername(domainANDuser);
            user.setPassword(password);
            BaseResponse<UserDTO> rspResult = userFacadeService.getUserInfo(user);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("UserClient.getUserLogin({}) 信息异常：{}.", domainANDuser, ex);
        }
        return null;
    }

    /**
     *
     * @param userId
     * @return
     */
    public UserDTO getUserInfoById(String userId){
        try {
            BaseResponse<UserDTO> rspResult = userFacadeService.getUserInfoByID(userId);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("UserClient.getUserInfoById({}) 信息异常：{}.", userId, ex);
        }
        return null;
    }

    /**
     * 查询Sales 折扣信息
     * @param querySalesDiscountReq
     * @return
     */
    public QuerySalesDiscountRsp getSalesDiscount(QuerySalesDiscountReq querySalesDiscountReq){
        try {
            String userManagementUrl = String.format("%s%s", interfaceConfig.getBaseUrl(), Constants.USER_MANAGEMENT.URL.GET_SALES_DISCOUNT);
            logger.info("UserClient.getSalesDiscount 请求地址：{}-{}",userManagementUrl,JSON.toJSONString(querySalesDiscountReq));
            CSResult<QuerySalesDiscountRsp> result = HttpClientUtil.sendPost(userManagementUrl, querySalesDiscountReq,new TypeReference<CSResult<QuerySalesDiscountRsp>>(){});
            //logger.info("UserClient.getSalesDiscount 返回结果：{}",result==null?"":JSON.toJSONString(result));
            //jira-11232
            if (result!=null){
                return result.getResult();
            }
        }catch (Exception ex){
            logger.error("UserClient.getSalesDiscount 请求异常：", ex);
        }
        return null;
    }
    /**
     * 查询Sales 折扣信息
     * @param queryEmployeeInfoReq
     * @return
     */
    public JSONObject queryEmployeeInfo(QueryEmployeeInfoReq queryEmployeeInfoReq){
        try {
            logger.info("UserClient.queryEmployeeInfo 请求参数：{}",JSON.toJSONString(queryEmployeeInfoReq));
            String userManagementUrl = String.format("%s%s", interfaceConfig.getBaseUrl(), Constants.USER_MANAGEMENT.URL.QUERY_EMPLOYEE_INFO);
            //logger.info("UserClient.queryEmployeeInfo 请求地址：{}",userManagementUrl);
            JSONObject jsonObject = HttpClientUtil.sendPost(userManagementUrl, queryEmployeeInfoReq,new TypeReference<JSONObject>(){});
            //logger.info("UserClient.queryEmployeeInfo 返回结果：{}",jsonObject==null?"":JSON.toJSONString(jsonObject));
            if(jsonObject ==null || !jsonObject.containsKey("rows") || jsonObject.getJSONArray("rows").size()==0){
                return null;
            }
            JSONObject row = jsonObject.getJSONArray("rows").getJSONObject(0);
            return row;
        }catch (Exception ex){
            logger.error("UserClient.queryEmployeeInfo 请求异常：", ex);
        }
        return null;
    }

    /**
     *
     * @param labCode
     * @return
     */
    public List<UserDTO> queryFinanceUserByLabCode(String labCode){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            BaseResponse<List<UserDTO>> userDTOList=userFacadeService.queryFinanceUserByLabCode(labCode);
            return userDTOList.getData();
        }catch (Exception ex){
            logger.error("UserClient.queryFinanceUserByLabCode 请求异常：", ex);
        }finally {
            stopWatch.stop();
            //logger.info("UserClient.queryFinanceUserByLabCode 请求接口耗时：{}.", stopWatch.getTime());
        }
        return Lists.newArrayList();
    }

    /**
     *
     * @param userName
     * @return
     */
    public UserDTO getUserInfoByUserName(String userName){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            UserVO objUserVO=new UserVO();
            BaseResponse<UserDTO> userDTOList=userFacadeService.getUserInfoByUserName(userName);
            return userDTOList.getData();
        }catch (Exception ex){
            logger.error("UserClient.queryFinanceUserByLabCode 请求异常：", ex);
        }finally {
            stopWatch.stop();
            //logger.info("UserClient.queryFinanceUserByLabCode 请求接口耗时：{}.", stopWatch.getTime());
        }
        return null;
    }

    /**
     * 根据用户名称模糊查询用户列表
     * @return
     */
    public PageInfo<UserInfo> queryUserByDimensionValue(QueryUserInfoReq queryUserInfoReq){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        PageInfo<UserInfo> userPage = new PageInfo<>();
        List<UserInfo> userInfoList;
        try {
            logger.info("UserClient.queryUserByDimensionValue 请求参数：{}",JSON.toJSONString(queryUserInfoReq));
            String userManagementUrl = String.format("%s%s", interfaceConfig.getBaseUrl(), Constants.USER_MANAGEMENT.URL.QUERY_USER_LIST_DIMENSION);
            //logger.info("UserClient.queryUserByDimensionValue 请求地址：{}",userManagementUrl);
            JSONObject jsonObject = HttpClientUtil.sendPost(userManagementUrl, queryUserInfoReq,new TypeReference<JSONObject>(){});
            //logger.info("UserClient.queryUserByDimensionValue 返回结果：{}",jsonObject==null?"":JSON.toJSONString(jsonObject));
            if(jsonObject ==null || !jsonObject.containsKey("rows") || Func.isEmpty(jsonObject.getJSONArray("rows"))){
                return userPage;
            }
            userInfoList = JSONObject.parseArray(jsonObject.get("rows").toString(),UserInfo.class);
            userPage.setList(userInfoList);
            userPage.setPageNum(Func.isNotEmpty(jsonObject.get("page"))?Integer.valueOf(jsonObject.get("page").toString()):1);
            userPage.setTotal(Func.isNotEmpty(jsonObject.get("records"))?Integer.valueOf(jsonObject.get("records").toString()):0);
        }catch (Exception ex){
            logger.error("UserClient.queryUserByDimensionValue 请求异常：", ex);
        }finally {
            stopWatch.stop();
            logger.info("UserClient.queryUserByDimensionValue 请求接口耗时：{}.", stopWatch.getTime());
        }
        return userPage;
    }



    public PageInfo<UserInfo> queryUserInfoList(QueryUserInfoReq queryUserInfoReq){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<UserInfo> userInfoList;
        PageInfo<UserInfo> userPage = new PageInfo<>();
        Map<String, Object> params = Maps.newHashMap();
        params.put("page",queryUserInfoReq.getPage());
        params.put("rows",queryUserInfoReq.getRows());
        params.put("regionAccount",queryUserInfoReq.getRegionAccount());
        if(Func.isNotEmpty(queryUserInfoReq.getLabCode())){
            params.put("labCode",queryUserInfoReq.getLabCode());
        }
        if(queryUserInfoReq.isAccurate()){
            params.put("accurateFlag",1);
        }
        if(Func.isNotEmpty(queryUserInfoReq.getIsAll())){
            params.put("isAll",queryUserInfoReq.getIsAll());
        }
        String userManagementUrl = String.format("%s%s%s", interfaceConfig.getBaseUrl(), Constants.USER_MANAGEMENT.URL.QUERY_USER_LIST,convertUrlParam(params));
        try {
            userManagementUrl = URLDecoder.decode(userManagementUrl,"UTF-8").replaceAll(" ","%20");
            logger.info("UserClient.queryUserInfoList 请求地址参数：{}",userManagementUrl);
            String result = HttpClientUtil.post(userManagementUrl, null);
            //logger.info("UserClient.queryUserInfoList 返回结果：{}",result==null?"":JSON.toJSONString(result));
            if(Func.isNotEmpty(result)){
                JSONObject jsonObject = JSON.parseObject(result);
                if (Func.isNotEmpty(jsonObject.get("list"))){
                    userInfoList = JSONObject.parseArray(jsonObject.get("list").toString(),UserInfo.class);
                    userPage.setList(userInfoList);
                    userPage.setPageNum(Func.isNotEmpty(jsonObject.get("pageNum"))?Integer.valueOf(jsonObject.get("pageNum").toString()):1);
                    userPage.setTotal(Func.isNotEmpty(jsonObject.get("total"))?Integer.valueOf(jsonObject.get("total").toString()):0);
                }
            }
        }catch (Exception exception){
                logger.error(exception.getMessage());
        }finally {
            stopWatch.stop();
            logger.info("UserClient.queryUserInfoList 请求接口耗时：{}.", stopWatch.getTime());
        }
        return userPage;
    }

    private String convertUrlParam(Map<String, Object> paramMaps){
        return "?".concat(Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(paramMaps));
    }


}
