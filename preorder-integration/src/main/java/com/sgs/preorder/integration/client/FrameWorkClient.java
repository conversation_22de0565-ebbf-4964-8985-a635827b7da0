package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.core.exception.SGSException;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.otsnotes.facade.model.common.BuReportWorkflowDTO;
import com.sgs.otsnotes.facade.model.common.DataLanguageRsp;
import com.sgs.otsnotes.facade.model.common.ResponseCode;
import com.sgs.otsnotes.facade.model.enums.UserInfoDefaultLanguageCodeEnums;
import com.sgs.otsnotes.facade.model.rsp.framework.UploadFileResult;
import com.sgs.preorder.core.config.BizConfigExt;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.core.thread.ThreadPoolContextTaskExecutor;
import com.sgs.preorder.core.util.BeanHelper;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.core.util.SysConstants;
import com.sgs.preorder.facade.model.common.CSResult;
import com.sgs.preorder.facade.model.dto.dm.DictDTO;
import com.sgs.preorder.facade.model.dto.framework.TrimsCountryDTO;
import com.sgs.preorder.facade.model.dto.framework.TrimsLocationDTO;
import com.sgs.preorder.facade.model.dto.holiday.HolidayDTO;
import com.sgs.preorder.facade.model.dto.order.FileDTO;
import com.sgs.preorder.facade.model.dto.order.LabDTO;
import com.sgs.preorder.facade.model.dto.order.NumberGenerateRuleDTO;
import com.sgs.preorder.facade.model.info.DataDictInfo;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.preorder.facade.model.info.ServiceTypeInfo;
import com.sgs.preorder.facade.model.info.UserManagementInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.info.user.UserLegalEntityInfo;
import com.sgs.preorder.facade.model.req.*;
import com.sgs.preorder.facade.model.req.framework.BossDataReq;
import com.sgs.preorder.facade.model.req.framework.GetLabSoftcopyDeliverToReq;
import com.sgs.preorder.facade.model.rsp.OrderParcelRsp;
import com.sgs.preorder.facade.model.rsp.ParcelInfoRsp;
import com.sgs.preorder.facade.model.rsp.QueryBuCodeRsp;
import com.sgs.preorder.facade.model.rsp.framework.BossDataRsp;
import com.sgs.preorder.facade.model.rsp.framework.GetLabSoftcopyDeliverToResult;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.CurrencyRateDTO;
import com.sgs.priceengine.facade.model.request.GetCurrencyRateReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Component
public class FrameWorkClient {
    private static final Logger logger = LoggerFactory.getLogger(FrameWorkClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private ThreadPoolContextTaskExecutor taskExecutor;
    @Autowired
    private BizConfigExt bizConfigExt;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private UserClient userClient;
    @Autowired
    private QuotationFacade quotationFacade;

    /**
     *
     * @param reqParams
     * @return
     */
    public UserInfo getUserLogin(HashMap<String, Object> reqParams){
        try {
            String frameWorkApi = String.format("%s/UserManagementApi/login/userLogin", interfaceConfig.getBaseUrl());
            UserManagementInfo userManagement = HttpClientUtil.sendPost(frameWorkApi, reqParams, UserManagementInfo.class);
            if (userManagement == null || StringUtils.isEmpty(userManagement.getSgsToken())){
                return null;
            }
            return tokenClient.getUser(userManagement.getSgsToken());
        }catch (Exception ex){
            logger.error("FrameWorkClient.getUserLogin 信息异常：{}.", ex);
        }
        return null;
    }
    /**
     *
     * @param labCode
     * @return
     */
    public Integer getLabIdByLabCode(String labCode){
        HashMap<String, Object> trimsParams = new HashMap();
        trimsParams.put("labCode", labCode);
        trimsParams.put("isAccurateQuery",1);
        LabDTO objlab = this.queryLabList(trimsParams, LabDTO.class);
        if(objlab!=null){
            return objlab.getLaboratoryID();
        }
        return null;
    }

    /**
     *
     * @param labCode
     * @return
     */
    public LabDTO getLabByLabCode(String labCode){
        HashMap<String, Object> trimsParams = new HashMap();
        trimsParams.put("labCode", labCode);
        trimsParams.put("isAccurateQuery",1);
        LabDTO objlab = this.queryLabList(trimsParams, LabDTO.class);
        return objlab;
    }


    public boolean isVnLab(String labCode){
        boolean isVnLab = false;
        if(Func.isEmpty(labCode)){
            labCode = SecurityUtil.getLabCode();
        }
        if(Func.isNotEmpty(labCode)){
            LabDTO labDTO = this.getLabByLabCode(SecurityUtil.getLabCode());
            if(Func.isNotEmpty(labDTO) && org.apache.commons.lang3.StringUtils.equalsIgnoreCase(labDTO.getCountryCode2(), Constants.COUNTRY_CODE.VN)){
                isVnLab = true;
            }
        }
        return isVnLab;
    }
    /**
     *
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T queryLabList(HashMap<String, Object> reqParams, Class<T> clazz){
        try {
            String frameWorkApi = String.format("%s/FrameWorkApi/trims/api/v1/queryLabList", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.post(frameWorkApi, reqParams);
            //logger.info("call queryLabList :{}"+jsonStr);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (jsonObject == null || !jsonObject.getBooleanValue("isSuccess")){
                return null;
            }
            JSONArray jsonArray = jsonObject.getJSONArray("rows");
            if (jsonArray == null || jsonArray.isEmpty()){
                return null;
            }
            // 如果入参中有LabCode,需要用laboratoryCode过滤
            String requestLab = Func.isNotEmpty(reqParams.get("labCode"))?reqParams.get("labCode").toString():null;
            if(Func.isNotEmpty(requestLab)){
                jsonArray = jsonArray.stream().filter(json -> {
                    JSONObject jsonObjectItem = (JSONObject) json;
                    return StringUtils.equalsIgnoreCase(jsonObjectItem.getString("laboratoryCode"), requestLab);
                }).collect(Collectors.toCollection(JSONArray::new));
                if (jsonArray == null || jsonArray.isEmpty()){
                    return null;
                }
            }
            return jsonArray.getObject(0, clazz);
        }catch (Exception ex){
            logger.error("FrameWorkClient.queryLabList 信息异常：{}.", ex);
        }
        return null;
    }

    public List<LabDTO> searchLabList(HashMap<String, Object> reqParams) {
        try {
            String frameWorkApi = String.format("%s/FrameWorkApi/trims/api/v1/queryLabList", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.post(frameWorkApi, reqParams);
            //logger.info("call queryLabList :{}"+jsonStr);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (jsonObject == null || !jsonObject.getBooleanValue("isSuccess")){
                return null;
            }
            JSONArray jsonArray = jsonObject.getJSONArray("rows");
            if (jsonArray == null || jsonArray.isEmpty()){
                return null;
            }
            return JSONObject.parseArray(jsonObject.getString("rows"), LabDTO.class);
        } catch (Exception ex) {
            logger.error("FrameWorkClient.queryLabList 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @return
     */
    public LabDTO queryLabByLabCode(String labCode){
        try {
            HashMap<String, Object> reqParams=new HashMap<>();
            reqParams.put("labCode",labCode);
            String frameWorkApi = String.format("%s/FrameWorkApi/trims/api/v1/queryLabList", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.post(frameWorkApi, reqParams);
            logger.info("call queryLabByLabCode :{}"+jsonStr);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if (jsonObject == null || !jsonObject.getBooleanValue("isSuccess")){
                return null;
            }
            String rowsStr=jsonObject.getString("rows");
            List<LabDTO> labDTOS=JSON.parseArray(rowsStr,LabDTO.class);
            if (labDTOS == null || labDTOS.isEmpty()){
                return null;
            }
            return labDTOS.stream().filter(e->e.getLaboratoryCode().equals(labCode)).findFirst().orElse(null);
        }catch (Exception ex){
            logger.error("FrameWorkClient.queryLabList 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @param keyGroup
     * @return
     */
    public HashMap<String, String> getDataDictList(String keyGroup){
        HashMap<String, String> hashMaps = new HashMap<String, String>();
        try {
            DataDictReq reqParams = new DataDictReq();
            reqParams.setBuId(1);
            reqParams.setSystemId(SgsSystem.GPO.getSgsSystemId());
            reqParams.setSysKeyGroup(keyGroup);

            List<DataDictInfo> dicts = this.getDataDictList(reqParams);
            if (dicts == null || dicts.isEmpty()){
                return null;
            }
            for (DataDictInfo dict: dicts){
                if (hashMaps.containsKey(dict.getSysKey())){
                    continue;
                }
                hashMaps.put(dict.getSysKey(), dict.getSysValue());
            }
        }catch (Exception ex){
            logger.error("FrameWorkClient.getDataDictList 信息异常：{}.", ex);
        }
        return hashMaps;
    }

    /**
     *
     * @param reqParams
     * @return
     */
    public DataDictInfo getDataDictInfo(DataDictReq reqParams){
        List<DataDictInfo> dicts = this.getDataDictList(reqParams);
        if (dicts == null || dicts.isEmpty()){
            return null;
        }
        return dicts.get(0);
    }

    /**
     *
     * @param reqParams
     * @return
     */
    public List<DataDictInfo> getDataDictList(DataDictReq reqParams){
        try {
            logger.info("getDataDictList from FrameWork params:{}",JSON.toJSONString(reqParams));
            String frameWorkApi = String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary", interfaceConfig.getBaseUrl());
            String jsonStr = HttpClientUtil.sendGet(frameWorkApi, BeanHelper.toList(reqParams));
            logger.info("getDataDictList from FrameWork result:{}",jsonStr);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            return JSONArray.parseArray(jsonStr, DataDictInfo.class);
        }catch (Exception ex){
            logger.error("FrameWorkClient.getDataDictList 信息异常：{}.", ex);
        }
        return null;
    }

    public String getQrcodeFlag(String labCode)
    {
        try {
            DataDictReq reqParams = new DataDictReq();
            reqParams.setSystemId(SgsSystem.GPO.getSgsSystemId());
            reqParams.setSysKey(labCode);
            reqParams.setSysKeyGroup("GeneralPreOrderQrcodeDefaultFlag");
            List<DataDictInfo> dicts = this.getDataDictList(reqParams);
            if (CollectionUtils.isNotEmpty(dicts)) {
                return dicts.get(0).getSysValue();
            }
        }catch (Exception e)
        {
            logger.error("=========FrameWorkClient.getQrcodeFlag 信息异常：{}=======",e);
        }
        return "";
    }

    /**
     *
     * @param keyGroup
     * @param sysKey
     * @return
     */
    public String getDataDictionaryValue(String keyGroup, Integer sysKey){
        if (sysKey == null){
            return "";
        }
        HashMap<String, String> hashMaps = getDataDictList(keyGroup);
        if (hashMaps == null || hashMaps.isEmpty()){
            return "";
        }
        return hashMaps.get(String.valueOf(sysKey));
    }

    /**
     *
     * @param reqParams
     * @return
     */
    public List<ServiceTypeInfo> getServiceTypeList(ServiceTypeReq reqParams){
        try {
            String frameWorkApi = String.format("%s%s", interfaceConfig.getBaseUrl(), com.sgs.preorder.core.util.Constants.FRAMEWORK.URL.GET_SERVICE_TYPE);
            HashMap<String,String> paramMap = Maps.newHashMap();
            paramMap.put("bUID",reqParams.getbUID());
            if(Func.isNotEmpty(reqParams.getLocationID())){
                paramMap.put("locationID",reqParams.getLocationID());
            }
            logger.info("getServiceTypeList Url: {}",frameWorkApi);
            return HttpClientUtil.get(frameWorkApi,paramMap,ServiceTypeInfo.class);
        }catch (Exception ex){
            logger.error("FrameWorkClient.getServiceTypeList 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @param buId
     * @param locationId
     * @return
     */
    public String getHolidayId(String buId, String locationId){
        Map<String, Object> paramsMaps = Maps.newHashMap();
        paramsMaps.put("buId", buId);
        paramsMaps.put("locationId", locationId);
        try {
            String frameWorkApi = String.format("%s/FrameWorkApi/holiday/api/v1/queryHolidayById", interfaceConfig.getBaseUrl());
            return HttpClientUtil.post(frameWorkApi, paramsMaps);
        }catch (Exception ex){
            logger.error("FrameWorkClient.getHolidayId 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @param holidayId
     * @return
     */
    public HolidayDTO getHolidayDetailInfo(String holidayId){
        // 调用系统接口查询下拉框的值
        /*HolidayDetailReq reqParams = new HolidayDetailReq();
        reqParams.setHolidayId(holidayId);*/
        Map<String, Object> paramsMaps = Maps.newHashMap();
        paramsMaps.put("holidayId", holidayId);
        try {
            String frameWorkApi = String.format("%s/FrameWorkApi/holiday/api/v1/get", interfaceConfig.getBaseUrl());
            return HttpClientUtil.doPost(frameWorkApi, paramsMaps, HolidayDTO.class);
        } catch(Exception ex) {
            logger.error( "FrameWorkClient.getHolidayDetail Error:{}", ex);
        }
        return null;
    }

    public JSONObject uploadFileToFramework(File file, String buid,Boolean pictureCompression) throws Exception {
        // TODO Auto-generated method stub

        String uploadFileUrl = String.format("%s/FrameWorkApi/file/doUpload", interfaceConfig.getBaseUrl());
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.addBinaryBody("file", file);
        multipartEntityBuilder.addTextBody("systemID", SgsSystem.GPO.getSgsSystemId()+"");
        multipartEntityBuilder.addTextBody("buID", buid);
        if (pictureCompression){
            multipartEntityBuilder.addTextBody("returnCompressPictureFlag", "true");
            multipartEntityBuilder.addTextBody("compressHeightLimit", Constants.COMPRESS_HEIGHT_LIMIT);
        }
        HttpEntity httpEntity = multipartEntityBuilder.build();
        logger.info("===============上传文件到framework，url:{},fileName:{}=================",uploadFileUrl,file.getName());
        HttpPost httpPost = new HttpPost(uploadFileUrl);
        httpPost.setEntity(httpEntity);

        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpResponse httpResponse = httpClient.execute(httpPost);
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        JSONObject jsonObject = null;
        logger.info("====上传文件到framework返回状态：{}=====",statusCode);
        if (statusCode == HttpStatus.SC_OK) {

            // 上传成功
            String resultStr = EntityUtils.toString(httpResponse.getEntity());
            jsonObject = JSONObject.parseObject(resultStr);
        }
        //jira-11232
        if (jsonObject!=null){
            logger.info("====上传文件到framework返回结果：{}=====",jsonObject.toString());
        }
        return jsonObject;
    }

    /**
     *
     * @param fileIds
     * @return
     */
    public void deleteFile(Set<String> fileIds){
        if (fileIds == null || fileIds.isEmpty()){
            return;
        }
        for (String fileId: fileIds){
            taskExecutor.execute(()->{
                this.deleteFile(fileId);
            });
        }
    }

    /**
     *
     * @param fileId
     * @return
     */
    public String deleteFile(String fileId){
        Map<String, Object> paramsMaps = Maps.newHashMap();
        paramsMaps.put("key", fileId);
        try {
            logger.info("请求接口FrameWorkClient.deleteFile({}).", fileId);
            String frameWorkApi = String.format("%s/FrameWorkApi/file/delete", interfaceConfig.getBaseUrl());
            return HttpClientUtil.doPost(frameWorkApi, paramsMaps, String.class);
        } catch(Exception ex) {
            logger.error( "FrameWorkClient.deleteFile Error:{}", ex);
        }
        return null;
    }


    /**
     *
     * @return
     */
    public void deleteBatchFile(String fileIds,String token){
        logger.info("请求接口FrameWorkClient.deleteBatch({}).", fileIds);
        String frameWorkApi = String.format("%s/FrameWorkApi/file/deleteBatch?sgsToken=" + token + "&ids=" + fileIds, interfaceConfig.getBaseUrl());
        HttpClientUtil.get(frameWorkApi);
    }

    /**
     *
     * @param reqParams
     * @return
     */
    public OrderParcelRsp associateParcel(ParcelReq reqParams){
        Map<String, Object> paramsMaps = BeanHelper.toHashMap(reqParams);
        try {
            String frameWorkApi = String.format("%s/FrameWorkApi/cmsApi/api/v1/associateParcel", interfaceConfig.getBaseUrl());
            return HttpClientUtil.doPost(frameWorkApi, paramsMaps, OrderParcelRsp.class);
        } catch(Exception ex) {
            logger.error( "FrameWorkClient.associateParcel Error:{}", ex);
        }
        return null;
    }

    /**
     * 调用 queryPackageSplitInfoByNo查询包裹信息
     */
    public ParcelInfoRsp queryParcelInfoByNo(ParcelInfoReq reqParams){
        if(Func.isEmpty(reqParams)){
            return new ParcelInfoRsp();
        }
        Map<String, String> paramsMaps = new HashMap<>();
        paramsMaps.put("packageNo",reqParams.getPackageNo());
        paramsMaps.put("packageSplitNo",reqParams.getPackageSplitNo());
        try {
            logger.info("请求接口FrameWorkClient.queryParcelInfoByNo({}).", JSON.toJSONString(paramsMaps));
            String frameWorkApi = String.format("%s/FrameWorkApi/cmsApi/api/v1/queryPackageSplitInfoByNo", interfaceConfig.getBaseUrl());
            String parcelStr = HttpClientUtil.requestGet(frameWorkApi,paramsMaps, null);
            logger.info("请求接口FrameWorkClient.queryParcelInfoByNo 返回：({}).", parcelStr);
            if(Func.isNotEmpty(parcelStr)){
                JSONObject jsonObject = JSONObject.parseObject(parcelStr);
                boolean isSuccess = jsonObject.getBoolean("isSuccess");
                if(isSuccess){
                    String parcelListStr = jsonObject.getString("result");
                    if(Func.isNotEmpty(parcelListStr)){
                        List<ParcelInfoRsp> parcelList = JSON.parseArray(parcelListStr, ParcelInfoRsp.class);
                        if(Func.isNotEmpty(parcelList)){
                            return parcelList.stream().sorted(Comparator.comparing(ParcelInfoRsp::getSubmitdate)).collect(Collectors.toList()).get(0);
                        }
                    }
                }
            }

        } catch(Exception ex) {
            logger.error( "FrameWorkClient.queryParcelInfoByNo Error:{}", ex);
        }
        return new ParcelInfoRsp();
    }

    /**
     * 参考viki :http://10.205.3.22:8090/pages/viewpage.action?pageId=31162458
     * @return
     */
    public List<LabInfo> getLabInfo(LabDTO labDTO){
        List<LabInfo> list = new ArrayList<>();
        try{
            String frameWorkApi = String.format("%s/FrameWorkApi/trims/api/v1/queryLabListForPage",interfaceConfig.getBaseUrl());
            Map<String, Object> param = new HashMap<>();
            param.put("buCode",labDTO.getBuCode());
            param.put("labCode",labDTO.getLabCode());
            param.put("page","1");
            param.put("rows","200");
            String post = HttpClientUtil.post(frameWorkApi, param);
            if(StringUtils.isNotBlank(post)){
                JSONObject object = JSONObject.parseObject(post);
                String rows = object.getString("rows");
                list = JSONArray.parseArray(rows, LabInfo.class);
            }
        }catch (Exception e){
            logger.error("FrameWorkClient.getLabInfo 信息异常：{}.",e);
        }
        return list;
    }

    public List<UserLegalEntityInfo> getUserLegalEntityList(String buCode, String locationCode){
        List<UserLegalEntityInfo> list = Lists.newArrayList();
        try{
            String frameWorkApi = String.format("%s%s",interfaceConfig.getBaseUrl(),com.sgs.preorder.core.util.Constants.FRAMEWORK.URL.GET_LEGAL_ENTITY_LIST);
            Map<String, Object> param = new HashMap<>();
            param.put("buCode",buCode);
            param.put("locationCode",locationCode);
            String post = HttpClientUtil.post(frameWorkApi, param);
            if(StringUtils.isNotBlank(post)){
                JSONObject object = JSONObject.parseObject(post);
                String rows = object.getString("data");
                list = JSONArray.parseArray(rows, UserLegalEntityInfo.class);
            }
        }catch (Exception e){
            logger.error("FrameWorkClient.getLabInfo 信息异常：{}.",e);
        }
        return list;
    }

    /**
     * 根据name查询
     * @param name
     * @return
     */
    public List<DictDTO> queryCountryAndRegionByName(String name,String languageCode) {
        List<DictDTO> dictDTOS = Collections.emptyList();
        try {
            Map<String, String> paramMaps = Maps.newHashMap();
            paramMaps.put("languageCode",languageCode);
            dictDTOS = HttpClientUtil.get(String.format("%s/FrameWorkApi/trims/api/v2/queryCountryAndRegion", interfaceConfig.getBaseUrl()),paramMaps,DictDTO.class);
        } catch (Exception e) {
            logger.error("trims/api/v2/queryLocationList接口转换出错",e);
        }
        List<DictDTO> dtos = dictDTOS.stream().filter(p -> StringUtils.equalsIgnoreCase(p.getValue(), name)).collect(Collectors.toList());
        return dtos;
    }


    /**
     *
     * @param fileIds
     * @return
     */
    public List<FileDTO> getFileListByFileIds(List<String> fileIds,List<String> newFileNames){
        Map<String, Object> params  = new HashMap<>();
        Map<String, Object> queryParams  = new HashMap<>();
        queryParams.put("ids",fileIds);
        if (Func.isNotEmpty(newFileNames)) {
            queryParams.put("newFileNames", newFileNames);
        }
        queryParams.put("compressPicture","all");
        //queryParams.put("systemID", String.valueOf(SgsSystem.GPO.getSgsSystemId()));
        params.put("queryParams",queryParams);
        try {
            logger.info("请求接口FrameWorkClient.getFileListByFileIds({}-{}).", fileIds,newFileNames);
            String frameWorkApi = String.format("%s/FrameWorkApi/file/query", interfaceConfig.getBaseUrl());
            return HttpClientUtil.post(frameWorkApi, params, FileDTO.class);
        } catch(Exception ex) {
            logger.error( "FrameWorkClient.getFileListByFileIds Error:{}", ex);
        }
        return null;
    }


    /**
     *
     * @param locationCode
     * @return
     */
    public String getLocationNameByLocationCode(String locationCode){
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("locationCode",locationCode);
            String frameWorkApi = String.format("%s%s", interfaceConfig.getBaseUrl(),"/FrameWorkApi/trims/api/v1/queryLocationList");
            String postJson = HttpClientUtil.postJson(frameWorkApi, JSONObject.toJSONString(param));
            JSONObject parse = (JSONObject) JSONArray.parse(postJson);
            JSONArray rows = parse.getJSONArray("rows");
            List<TrimsLocationDTO> trimsLocationDTOS = JSON.parseArray(rows.toJSONString(), TrimsLocationDTO.class);
            //List<TrimsLocationDTO> trimsLocationDTOS=HttpClientUtil.post(frameWorkApi, param, TrimsLocationDTO.class);
            if(CollectionUtils.isNotEmpty(trimsLocationDTOS)){
                return trimsLocationDTOS.get(0).getLocationName();
            }
            return "";
        }catch (Exception ex){
            logger.error("FrameWorkClient.getLocationNameByLocationCode 信息异常：{}.", ex);
        }
        return "";
    }

    public String getCountryCode(String countryName){
        try {
            Map<String, String> param = new HashMap<>();
            String frameWorkApi = String.format("%s%s", interfaceConfig.getBaseUrl(),"/FrameWorkApi/trims/api/v1/queryCountryAndRegion");
            List<TrimsCountryDTO> trimsCountryDTOS=HttpClientUtil.get(frameWorkApi,param, TrimsCountryDTO.class);
            if(CollectionUtils.isNotEmpty(trimsCountryDTOS)){
                TrimsCountryDTO objTrimsCountryDTO=trimsCountryDTOS.stream().filter(e->e.getValue().equals(countryName)).findFirst().orElse(null);
                if(objTrimsCountryDTO!=null){
                    return objTrimsCountryDTO.getCode();
                }
            }
            return "";
        }catch (Exception ex){
            logger.error("FrameWorkClient.getLocationNameByLocationCode 信息异常：{}.", ex);
        }
        return "";
    }

    public String generateOrderNo(String buId, String locationId, String postfix,String locationCode,String shortCode){
//        if(!ProductLines.SOFTLINE.getCode().equals(CopyOrderUtils.getTargetProductLineCode())){//for gpo
            return this.generateOrderNoForGpo(buId,locationId,postfix,locationCode,shortCode);
//        }
//        //for sl
//        Map<String, String> locationMap = this.bizConfigExt.getLocationMap();
//        String key = "LOC" + locationId;
//        if (locationMap.get(key) == null) {
//            throw new SGSException("getLocationMap","获取location失败！");
//        }
//        String referenceCode = locationMap.get(key).toString();
//        String postfixTemp = StringUtils.isEmpty(postfix) ? "TX" : postfix;
//        String systemId=SgsSystem.GPO.getSgsSystemId()+"";//for sl systemid
///*        if(!ProductLines.SOFTLINE.getCode().equals(ProductLineContextHolder.getProductLineCode())){
//            systemId=Constants.GPO_SYSTEM;
//        }*/
//        String frameWorkApi = String.format("%s%s", interfaceConfig.getBaseUrl(),"/FrameWorkApi/numberRule/api/v1/get/orderNo?systemID="+systemId+"&bUID=" + buId
//                + "&locationID=" + locationId + "&referenceCode=" + referenceCode + "&postfix=" + postfixTemp+"&prefix="+ ProductLineContextHolder.getProductLineCode().toUpperCase());
//        String orderNo=HttpClientUtil.get(frameWorkApi);
//        return orderNo;
    }

    /**
     *
     * @param buId
     * @return
     */
    public String generateOrderNoForGpo(String buId, String locationId, String postfix,String locationCode,String shortCode){
        if(StringUtils.isEmpty(buId)||StringUtils.isEmpty(locationId)){
            throw new SGSException("generateOrderNoForGpo", "buId or locationId is null");
        }
        // 此逻辑出自SL的单号生成规则，GPO不需要默认后缀的判断
        //String postfixTemp = StringUtils.isEmpty(postfix) ? "TX" : postfix;
        NumberGenerateRuleDTO objNumberGenerateRuleDTO=new NumberGenerateRuleDTO();
        objNumberGenerateRuleDTO.setBuId(Integer.valueOf(buId));
        objNumberGenerateRuleDTO.setLocationId(Integer.valueOf(locationId));
        objNumberGenerateRuleDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
        objNumberGenerateRuleDTO.setNumberRuleCode("GPOGeneralOrderNo");
        Map numberSegment=new HashMap();
//        UserLabBuInfo userLabBuInfo = userClient.getUserLabBuInfo();
//        if (userLabBuInfo == null) {
//            throw new SGSException("getUserFail", "get UserFail");
//        }

        numberSegment.put("prefix",locationCode+shortCode.toUpperCase());
        numberSegment.put("postfix",postfix);
        objNumberGenerateRuleDTO.setNumberSegment(numberSegment);
        String frameWorkApi = String.format("%s/FrameWorkApi/numberRule/api/v1/get/number", interfaceConfig.getBaseUrl());
        String orderNumber= null;
        try {
            orderNumber=HttpClientUtil.postJson(frameWorkApi, objNumberGenerateRuleDTO);
        } catch (Exception e) {
            throw new SGSException("Generate Order NO", "Generate Order NO is error");
        }
        return orderNumber;
    }

    public String getOrderNoForEnquiry(String buId, String locationId, String enquiryNo){
        if(StringUtils.isEmpty(buId)||StringUtils.isEmpty(locationId)){
            throw new SGSException("getOrderNoForEnquiry", "buId or locationId is null");
        }
        NumberGenerateRuleDTO objNumberGenerateRuleDTO=new NumberGenerateRuleDTO();
        objNumberGenerateRuleDTO.setBuId(Integer.valueOf(buId));
        objNumberGenerateRuleDTO.setLocationId(Integer.valueOf(locationId));
        objNumberGenerateRuleDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
        objNumberGenerateRuleDTO.setNumberRuleCode("EnquiryOrderMatrix");
        Map numberSegment=new HashMap();
        numberSegment.put("transactionNo",enquiryNo + "_" + enquiryNo);
        objNumberGenerateRuleDTO.setNumberSegment(numberSegment);
        String frameWorkApi = String.format("%s/FrameWorkApi/numberRule/api/v1/get/number", interfaceConfig.getBaseUrl());
        String orderNumber= null;
        try {
            orderNumber=HttpClientUtil.postJson(frameWorkApi, objNumberGenerateRuleDTO);
        } catch (Exception e) {
            throw new SGSException("Generate Order NO", "Generate Order NO is error");
        }
        return orderNumber;
    }

    public String quereyHolidayId(String buId, String locationId) {
        // 调用系统接口查询下拉框的值
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("buId", buId);
        paramsMap.put("locationId", locationId);

        String holidayId = null;
        try {
            holidayId = HttpClientUtil.post(interfaceConfig.getBaseUrl() + "/FrameWorkApi/holiday/api/v1/queryHolidayById",paramsMap);
        } catch(Exception e) {
            logger.error( "queryHolidayId error:{}", e.getMessage(), e);
            throw new SGSException("REMOTE_SERVICE_INVALID", "FRAMEWORK_API/holiday/api/v1/queryHolidayById api调用失败！");
        }
        return holidayId;
    }

    public HolidayDTO queryHolidayDetail(String holidayId) throws SGSException {
        // 调用系统接口查询下拉框的值
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("holidayId", holidayId);
        String holidayDetail = null;

        try {
            holidayDetail = HttpClientUtil.post(interfaceConfig.getBaseUrl() + "/FrameWorkApi/holiday/api/v1/get", paramsMap);
        } catch(Exception e) {
            logger.error( "queryHolidayDetail error:{}", e.getMessage(), e);
            throw new SGSException("REMOTE_SERVICE_INVALID", "FRAMEWORK_API/holiday/api/v1/get api调用失败！");
        }

        HolidayDTO holidayDTO = null;
        try {
            holidayDTO = JSONObject.parseObject(holidayDetail,HolidayDTO.class);
        } catch (Exception e) {
            logger.error("queryHolidayDetail error:" + e.getMessage(), e);
            throw new SGSException("JSON_PARSE_ERROR", "FRAMEWORK_API/holiday/api/v1/get返回结果转换失败");
        }
        return holidayDTO;
    }

    public String downloadByCloudID(Integer systemId,String cloudId,String networkType,String fileName, boolean inline) {
        // TODO Auto-generated method stub
        Map<String, Object> params = new HashMap<>();
        // systemID 必填
        params.put("systemID", systemId);
        params.put("cloudID",cloudId);
        params.put("networkType",networkType);
        if(Func.isNotEmpty(fileName)){
            params.put("newFileName",fileName);
        }
        if(inline){
            params.put("openWith","inline");
        }
        // 调http接口，并缓存结果
        try {
            String url = String.format("%s/FrameWorkApi/file/downloadByCloudID", interfaceConfig.getBaseUrl());
            String ossUrl = HttpClientUtil.post(url, params);
            return ossUrl;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            logger.error(e.getMessage(),e);
        }

        return "";
    }

    /**
     *
     * @param buId
     * @return
     */
    public String generateEnquiryNo(String buId, String locationId, String postfix){
        if(StringUtils.isEmpty(buId)||StringUtils.isEmpty(locationId)){
            throw new SGSException("generateEnquiryNo", "buId or locationId is null");
        }
        String postfixTemp = StringUtils.isEmpty(postfix) ? "E" : postfix;
        NumberGenerateRuleDTO objNumberGenerateRuleDTO=new NumberGenerateRuleDTO();
        objNumberGenerateRuleDTO.setBuId(Integer.valueOf(buId));
        objNumberGenerateRuleDTO.setLocationId(Integer.valueOf(locationId));
        objNumberGenerateRuleDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
        objNumberGenerateRuleDTO.setNumberRuleCode("GPOEnquiryNo");
        Map numberSegment=new HashMap();
        UserLabBuInfo userLabBuInfo = userClient.getUserLabBuInfo();
        if (userLabBuInfo == null) {
            throw new SGSException("getUserFail", "get UserFail");
        }

        numberSegment.put("prefix",userLabBuInfo.getLocationCode()+userLabBuInfo.getShortCode().toUpperCase());
        numberSegment.put("postfix",postfixTemp);
        objNumberGenerateRuleDTO.setNumberSegment(numberSegment);
        String frameWorkApi = String.format("%s/FrameWorkApi/numberRule/api/v1/get/number", interfaceConfig.getBaseUrl());
        String enquiryNumber= null;
        try {
            enquiryNumber=HttpClientUtil.postJson(frameWorkApi, objNumberGenerateRuleDTO);
        } catch (Exception e) {
            throw new SGSException("Generate Order NO", "Generate Order NO is error");
        }
        return enquiryNumber;
    }

    public List<BuParamValueRsp> getBuParams(BuParamReq buParamReq){
        String frameWorkApi = String.format("%s/FrameWorkApi/busetting/get", interfaceConfig.getBaseUrl());
        String paramResult= null;
        try {
            paramResult=HttpClientUtil.postJson(frameWorkApi, buParamReq);
        } catch (Exception e) {
            throw new SGSException("getBuParams", "getBuParams is error");
        }
        List<BuParamValueRsp> paramValueDTOS = null;
        try {
            CSResult<BuParamValueRsp> buParamValueDTOCSResult = JSON.parseObject(paramResult,new TypeReference<CSResult<BuParamValueRsp>>(){});
            paramValueDTOS = buParamValueDTOCSResult.getRows();
        } catch (Exception e) {
            logger.error("getBuParams error:" + e.getMessage(), e);
            throw new SGSException("JSON_PARSE_ERROR", "FRAMEWORK_AP/busetting/get返回结果转换失败:"+e.getMessage());
        }
        return paramValueDTOS;
    }
    public <T> T getBuParam(BuParamReq buParamReq,TypeReference<T> typeReference){
        List<BuParamValueRsp> buParams = this.getBuParams(buParamReq);
        if(Func.isEmpty(buParams)){
            return null;
        }
        String paramValue = buParams.get(0).getParamValue();
        if(Func.isEmpty(paramValue)){
            return null;
        }
        return JSON.parseObject(paramValue,typeReference);
    }

    public String getPaidUpNo(String buId, String locationId,String productLineCode){
        if(StringUtils.isEmpty(buId)||StringUtils.isEmpty(locationId) || Func.isEmpty(productLineCode)){
            throw new SGSException("getPaidUpNo", "buId or locationId or productLineCode is null");
        }
        NumberGenerateRuleDTO objNumberGenerateRuleDTO=new NumberGenerateRuleDTO();
        objNumberGenerateRuleDTO.setBuId(Integer.valueOf(buId));
        objNumberGenerateRuleDTO.setLocationId(Integer.valueOf(locationId));
        objNumberGenerateRuleDTO.setSystemId(SgsSystem.GPO.getSgsSystemId());
        objNumberGenerateRuleDTO.setNumberRuleCode("GPOPaidupNo");
        Map numberSegment=new HashMap();
        numberSegment.put("prefix",productLineCode);
        numberSegment.put("postfix",Constants.PAID_UP_SUFFIX);
        objNumberGenerateRuleDTO.setNumberSegment(numberSegment);
        String frameWorkApi = String.format("%s/FrameWorkApi/numberRule/api/v1/get/number", interfaceConfig.getBaseUrl());
        String orderNumber= null;
        try {
            orderNumber=HttpClientUtil.postJson(frameWorkApi, objNumberGenerateRuleDTO);
        } catch (Exception e) {
            throw new SGSException("Paid Up NO", "Paid Up NO is error");
        }
        return orderNumber;
    }

    /**
     * 获取货币数据
     * @return
     */
    public String getCurrency(){
        String result = null;
        String url =  String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID=1&systemID=0&SysKeyGroup=currency", interfaceConfig.getBaseUrl());
        try {
            result = HttpClientUtil.get(url);
        }catch (Exception e){
            throw new SGSException("500", "get Currency fail");
        }
        //logger.info(url);
        logger.info(result);
        return result;
    }

    /**
     * 获取支付方式
     * @return
     */
    public String getPaymentMethod(){
        String result = null;
        String url =  String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup=PaymentMethod&systemID=15", interfaceConfig.getBaseUrl());
        try {
            result = HttpClientUtil.get(url);
        }catch (Exception e){
            throw new SGSException("500", "get Payment Method fail");
        }
        //logger.info(url);
        logger.info(result);
        return result;
    }

    /**
     * 获取productCategory、Code Mapping关系
     * @return
     */
    public String getProductCategoryAndCodeMapping(String BuCode){
        String result = null;
        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup=ProductCategoryAndCodeMapping&systemID=15&buCode=" + BuCode;
        try {
            result = HttpClientUtil.get(url);
        }catch (Exception e){
            throw new SGSException("500", "get Payment Method fail");
        }
        //logger.info(url);
        logger.info(result);
        return result;
    }

    /**
     * enquiry生成订单重新获取附件cloudId
     * @param fileId
     * @param enquiryId
     * @return
     */
    public String getCloudIdForGenerateOrder(String fileId,String enquiryId){
        Map<String, Object> params = new HashMap<>();
        params.put("id", fileId);
        if(Func.isNotEmpty(enquiryId)) {
            params.put("generalOrderID", enquiryId);
            params.put("objectID", enquiryId);
        }
        // 调http接口，并缓存结果
        String result = null;
        try {
            String url = String.format("%s/FrameWorkApi/file/copyTbfile", interfaceConfig.getBaseUrl());
            result = HttpClientUtil.post(url, params);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            logger.error(e.getMessage(),e);
        }
        return result;
    }
    public List<FileDTO> queryFilePathByCloudIds(Set<String> cloudIdList){
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("cloudIdList",cloudIdList);
            String url = String.format("%s/FrameWorkApi/file/queryFilesByIdsOrCloudIds", interfaceConfig.getBaseUrl());
            String body = HttpClientUtil.postJson(url, params);
            if(Func.isEmpty(body)){
                return null;
            }
            JSONObject jsonObject = JSON.parseObject(body);
            String  result = jsonObject.getString("result");
            return  JSON.parseArray(result, FileDTO.class);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            logger.error(e.getMessage(),e);
        }
        return null;

    }
    /**
     * 获取配置 orderAttachment
     * @return
     */
    public Map<String,String> getOrderAttachment(){
        Map<String,String> resultMap = new HashMap<>();
        String result = null;
        String url =  String.format("%s/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup=OrderAttachmentType&systemID=15", interfaceConfig.getBaseUrl());
        try {
            result = HttpClientUtil.get(url);
        }catch (Exception e){
            throw new SGSException("500", "get Payment Method fail");
        }
        if (Func.isEmpty(result)){
            return resultMap;
        }
        JSONArray jsonArray = JSONArray.parseArray(result);
        if (Func.isEmpty(jsonArray)){
            return resultMap;
        }
        for (int i = 0;i<jsonArray.size();i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            resultMap.put(jsonObject.getString("sysKey"),jsonObject.getString("sysValue"));
        }
        return resultMap;
    }

    public BossDataRsp queryBossDefaultData(BossDataReq bossDataReq){
        try {
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("buCode",bossDataReq.getBuCode());
            paramMap.put("locationCode",bossDataReq.getLocationCode());
            String frameWorkApi = String.format("%s/FrameWorkApi/toBoss/api/v1/queryBossDefaultData", interfaceConfig.getBaseUrl());
            String jsonStr=HttpClientUtil.post(frameWorkApi, paramMap);
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if(jsonObject.containsKey("result")){
                return jsonObject.getObject("result",BossDataRsp.class);
            }else{
                return null;
            }
        } catch (Exception e) {
            logger.error("getBossDefaultData happend expection:{}",e);
            return null;
        }
    }

    public List<DictDTO> queryCountryAndRegionV1(LanguageType languageType, boolean allLanguageCode){
        List<DictDTO> dictDTOS = Collections.emptyList();
        try {
            String url = String.format("%s/FrameWorkApi/trims/api/v1/queryCountryAndRegion", interfaceConfig.getBaseUrl());
            url+="?languageCode=";
            url+=languageType.getCode();
            if(allLanguageCode){
                url+="&allLanguage=yes";
            }
            dictDTOS = HttpClientUtil.get(url,Collections.emptyMap(),DictDTO.class);
        } catch (Exception e) {
            logger.error("call trims/api/v2/queryLocationList接口异常:{}",e);
        }
        return dictDTOS;
    }

    public CustomResult<QueryBuCodeRsp> queryBuCodeBySubcontractNo(String subcontractNo) {
        String url = String.format("%s%s", interfaceConfig.getBaseUrl(), Constants.QUERY_BU_CODE_URL);
        QueryBuCodeReq queryBuCodeReq = new QueryBuCodeReq();
        queryBuCodeReq.setObjectNo(subcontractNo);
        queryBuCodeReq.setRuleCode(Constants.QUERY_BU_CODE_RULE_CODE);
        queryBuCodeReq.setSystemId(SysConstants.SYSTEM_ID);
        try{
            BaseResponse result = HttpClientUtil.doPost(url, queryBuCodeReq, BaseResponse.class);
            if (result.getStatus() != ResponseCode.SUCCESS.getCode()) {
                return new CustomResult().fail(result.getMessage());
            }
            return new CustomResult(true).data(result.getData());
        }catch (Exception e){
            logger.error("FrameWorkClient.queryBuCodeBySubcontractNo 信息异常", e);
            return new CustomResult().fail("调用objectNoParse接口信息异常");
        }
    }

    /**
     * 用户名获取邮箱
     * @param regionAccount
     * @param labCode
     * @return
     */
    public String getEmailByRegionAccount(String regionAccount,String labCode){
        String email = null;
        Map<String, Object> params = Maps.newHashMap();
        params.put("page",1);
        params.put("rows",10);
        params.put("regionAccount",regionAccount);
//        params.put("labCode",labCode);
        params.put("accurateFlag",1);
        String url = String.format("%s/UserManagementApi/employee/queryUserInfoList", interfaceConfig.getBaseUrl());
        String result = HttpClientUtil.post(url, params);
        logger.info("getEmailByRegionAccount->/employee/queryUserInfoList返回值：{}",result);
        if(Func.isNotEmpty(result)){
            JSONObject jsonObject = JSON.parseObject(result);
            JSONArray jsonArray = JSONArray.parseArray(jsonObject.get("list").toString());
            if (Func.isNotEmpty(jsonArray)){
                JSONObject json = jsonArray.getJSONObject(0);
                email = json.getString("email");
            }
        }
        return email;
    }

    public boolean extendsReportMatrix(String labCode,String productLineCode) throws Exception{
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(com.sgs.preorder.core.util.Constants.BU_PARAM.SUBCONTRACT.GROUP);
        buParamReq.setParamCode(com.sgs.preorder.core.util.Constants.BU_PARAM.SUBCONTRACT.EXTENDS_REPORT_MATRIX);
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setLabCode(labCode);
        List<BuParamValueRsp> buParams = getBuParams(buParamReq);
        String extendsReportMatrix = StringPool.EMPTY;
        if(Func.isNotEmpty(buParams)){
            extendsReportMatrix = buParams.get(0).getParamValue();
        }
        if(StringUtils.equalsIgnoreCase(extendsReportMatrix, com.sgs.preorder.core.util.Constants.YES)){
            return true;
        }
        return false;
    }

    public String getPrimaryLanguageCode(String productLineCode){
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setParamCode("DataLanguage");
        buParamReq.setGroupCode("Display");
        List<BuParamValueRsp> buParams = this.getBuParams(buParamReq);
        if(Func.isEmpty(buParams)){
            return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
        }else{
            BuParamValueRsp buParam = buParams.get(0);
            String paramValue = buParam.getParamValue();
            try {
                DataLanguageRsp dataLanguageRsp = JSONObject.parseObject(paramValue, DataLanguageRsp.class);
                if(Func.isEmpty(dataLanguageRsp) || Func.isEmpty(dataLanguageRsp.getLanguages())){
                    logger.info("getPrimaryLanguageCode return empty");
                    return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
                }else{
                    List<DataLanguageRsp.Languages> languages = dataLanguageRsp.getLanguages();
                    if(Func.isNotEmpty(languages)){
                        DataLanguageRsp.Languages  primaryLanguage= languages.stream().filter(DataLanguageRsp.Languages::getPrimary).findFirst().orElse(null);
                        if(Func.isNotEmpty(primaryLanguage)){
                            return primaryLanguage.getLanguageCode();
                        }else{
                            return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
                        }
                    }else{
                        return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
                    }
                }
            } catch (Exception e) {
                logger.error("parse byBuParam Json error:{}",e);
                return UserInfoDefaultLanguageCodeEnums.en_us.getCode();
            }
        }
    }

    public BigDecimal getMainCurrencyRate(String orderId, String orgCurrencyCode){
        if(Func.isEmpty(orderId) || Func.isEmpty(orgCurrencyCode)){
            return null;
        }
        //获取主币种
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setParamCode(com.sgs.preorder.core.util.Constants.BU_PARAM.QUOTATION.MAIN_CURRENCY.CODE);
        buParamReq.setGroupCode(com.sgs.preorder.core.util.Constants.BU_PARAM.QUOTATION.GROUP);
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<BuParamValueRsp> buParamMainCurrencyList = this.getBuParams(buParamReq);
        GetCurrencyRateReq currencyRateReq = new GetCurrencyRateReq();
        currencyRateReq.setOrderId(orderId);
        currencyRateReq.setOrgCurrencyCode(orgCurrencyCode);
        if(Func.isNotEmpty(buParamMainCurrencyList)){
            currencyRateReq.setTargetCurrencyCode(buParamMainCurrencyList.get(0).getParamValue());
        }
        currencyRateReq.setRateDate(new Date());
        currencyRateReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
        currencyRateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<CurrencyRateDTO> currencyRateRsp = quotationFacade.getCurrencyRateDTOByOrderId(currencyRateReq);
        logger.info("getCurrencyRateDTOByOrderId res:{}",JSON.toJSONString(currencyRateRsp));
        BigDecimal mainCurrencyRate = null;
        if(Func.isNotEmpty(currencyRateRsp) && Func.isNotEmpty(currencyRateRsp.getData()) && Func.isNotEmpty(currencyRateRsp.getData().getCurrencyRate())){
            mainCurrencyRate = currencyRateRsp.getData().getCurrencyRate();

        }
        return mainCurrencyRate;
    }
    public UploadFileResult doUploadFile(File uploadFile, String buId, String objectId, Integer objectType){
        UploadFileResult uploadFileResult = new UploadFileResult();
        try {
            String uploadFileUrl = String.format("%s/FrameWorkApi/file/doUpload", interfaceConfig.getBaseUrl());
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            multipartEntityBuilder.setMode(HttpMultipartMode.RFC6532);
            multipartEntityBuilder.setCharset(Charset.forName("UTF-8"));
            multipartEntityBuilder.addBinaryBody("file", uploadFile);
            multipartEntityBuilder.addTextBody("systemID", SgsSystem.GPO.getSgsSystemId()+"");
            multipartEntityBuilder.addTextBody("buID", buId);
            multipartEntityBuilder.addTextBody("objectID", objectId);
            if(Func.isNotEmpty(objectType)){
                logger.info("FrameWorkApi/file/doUpload objectType:{}",objectType);
                multipartEntityBuilder.addTextBody("objectType", Func.toStr(objectType));
            }
            HttpEntity httpEntity = multipartEntityBuilder.build();
            logger.info("===============上传文件到framework，url:{},fileName:{}=================",uploadFileUrl,uploadFile.getName());
            HttpPost httpPost = new HttpPost(uploadFileUrl);
            httpPost.setEntity(httpEntity);
            CloseableHttpClient httpClient = HttpClientBuilder.create().build();
            HttpResponse httpResponse = httpClient.execute(httpPost);
            String resultStr = EntityUtils.toString(httpResponse.getEntity());
            uploadFileResult = JSONObject.parseObject(resultStr, UploadFileResult.class);
        } catch (IOException e) {
            logger.error("upload file exception:{}",e);
            return null;
        }
        return uploadFileResult;
    }
    public int deleteFileByCloudId(String cloudId){
        if(Func.isEmpty(cloudId)){
            logger.error("deleteFile fail,cloudId is empty");
            return 0;
        }
        Map<String, Object> allParams =  new HashMap<>();
        allParams.put("cloudID",cloudId);
        allParams.put("systemID",SgsSystem.GPO.getSgsSystemId()+"");
        String deleteFileUrl = String.format("%s/FrameWorkApi/file/deleteByCloudId", interfaceConfig.getBaseUrl());
        try {
            logger.info("deleteFile param:{}",JSON.toJSONString(allParams));
            String deleteCountStr = HttpClientUtil.postJson(deleteFileUrl, JSON.toJSONString(allParams));
            if(Func.isNotEmpty(deleteCountStr)){
                return Integer.parseInt(deleteCountStr);
            }else{
                return 0;
            }
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取配置的联系人邮箱地址
     * @param getLabSoftcopyDeliverToReq
     * @return
     */
    public String getLabSoftcopyDeliverToEmail(GetLabSoftcopyDeliverToReq getLabSoftcopyDeliverToReq){
        if(Func.isEmpty(getLabSoftcopyDeliverToReq) || Func.isEmpty(getLabSoftcopyDeliverToReq.getExecLab()) || Func.isEmpty(getLabSoftcopyDeliverToReq.getHostLab())){
            return "";
        }
        List<GetLabSoftcopyDeliverToReq> paramList = new ArrayList<>();
        paramList.add(getLabSoftcopyDeliverToReq);
        String getLabSoftcopyDeliverToUrl = String.format("%s/FrameWorkApi/getLabSoftcopyDeliverTo", interfaceConfig.getBaseUrl());
        try {
            String resultStr = HttpClientUtil.postJson(getLabSoftcopyDeliverToUrl, JSON.toJSONString(paramList));
            JSONObject jsonObject = JSONObject.parseObject(resultStr);
            if(jsonObject.containsKey("data")){
                String rows = jsonObject.getString("data");
                List<GetLabSoftcopyDeliverToResult> getLabSoftcopyDeliverToResults = JSONArray.parseArray(rows, GetLabSoftcopyDeliverToResult.class);
                if(Func.isNotEmpty(getLabSoftcopyDeliverToResults)){
                    return getLabSoftcopyDeliverToResults.get(0).getResponsibleEmail();
                }else{
                    return "";
                }
            }else{
                return "";
            }
        } catch (Exception e) {
           logger.error("getLabSoftcopyDeliverTo error:{}",e);
           return "";
        }
    }

    public BuParamValueRsp getBuParamValue(String productLineCode, String locationCode, String groupCode, String paramCode){
        Map<String, Object> params = new HashMap<>();
        params.put("productLineCode", productLineCode);
        params.put("locationCode", locationCode);
        params.put("groupCode", groupCode);
        params.put("paramCode", paramCode);
        logger.info("getBuParamValue 请求数据：{}",JSON.toJSONString(params));
        String frameWorkApi = String.format("%s/FrameWorkApi/busetting/get", interfaceConfig.getBaseUrl());
        String paramResult= null;
        List<BuParamValueRsp> paramValueDTOS = null;
        try {
            paramResult=HttpClientUtil.postJson(frameWorkApi, JSON.toJSONString(params));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return null;
        }
        try {
            CSResult<BuParamValueRsp> buParamValueDTOCSResult = JSON.parseObject(paramResult,new TypeReference<CSResult<BuParamValueRsp>>(){});
            paramValueDTOS = buParamValueDTOCSResult.getRows();
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return null;
        }
        logger.info("getBuParamValue 返回结果：{}",JSON.toJSONString(paramValueDTOS));
        return Func.isEmpty(paramValueDTOS)?null:paramValueDTOS.get(0);
    }
    public BuParamValueRsp getBuParamValueByLab(String productLineCode, String labCode, String groupCode, String paramCode){
        Map<String, Object> params = new HashMap<>();
        params.put("productLineCode", productLineCode);
        params.put("labCode", labCode);
        params.put("groupCode", groupCode);
        params.put("paramCode", paramCode);
        logger.info("getBuParamValueByLab 请求数据：{}",JSON.toJSONString(params));
        String frameWorkApi = String.format("%s/FrameWorkApi/busetting/get", interfaceConfig.getBaseUrl());
        String paramResult= null;
        List<BuParamValueRsp> paramValueDTOS = null;
        try {
            paramResult=HttpClientUtil.postJson(frameWorkApi, JSON.toJSONString(params));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return null;
        }
        try {
            CSResult<BuParamValueRsp> buParamValueDTOCSResult = JSON.parseObject(paramResult,new TypeReference<CSResult<BuParamValueRsp>>(){});
            paramValueDTOS = buParamValueDTOCSResult.getRows();
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return null;
        }
        logger.info("getBuParamValueByLab 返回结果：{}",JSON.toJSONString(paramValueDTOS));
        return Func.isEmpty(paramValueDTOS)?null:paramValueDTOS.get(0);
    }

    public BuReportWorkflowDTO getBuWorkFlow(String buCode, String locationCode){
        BuParamValueRsp buParamValueRsp = this.getBuParamValue(buCode,locationCode,
                com.sgs.preorder.core.util.Constants.BU_PARAM.REPORT.GROUP, com.sgs.preorder.core.util.Constants.BU_PARAM.REPORT.WORK_FLOW.CODE);
        if (Func.isEmpty(buParamValueRsp) || Func.isEmpty(buParamValueRsp.getParamValue())){
            return null;
        }
        BuReportWorkflowDTO buReportWorkflowDTO = JSONObject.parseObject(buParamValueRsp.getParamValue(),BuReportWorkflowDTO.class);
        return buReportWorkflowDTO;
    }

    public BigDecimal getCurrencyExchangeRate(String fromCurrency, String toCurrency, String conversionDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("fromCurrency", fromCurrency);
        params.put("toCurrency", toCurrency);
        params.put("conversionDate", conversionDate);
        try {
            String getCurrencyExchangeRateUrl = String.format("%s/FrameWorkApi/currencyExchangeRate/api/v1/get/list", interfaceConfig.getBaseUrl());
            String jsonStr=HttpClientUtil.post(getCurrencyExchangeRateUrl, params);
            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
            if(Func.isEmpty(jsonArray) || jsonArray.size()==0){
                throw new BizException("没有获取到汇率");
            }
            return (BigDecimal)jsonArray.getJSONObject(0).get("conversionRate");
        } catch (Exception e) {
            logger.error("获取汇率异常:{}",e);
            throw  new SGSException("ERROR",e.getMessage());
        }
    }

}
