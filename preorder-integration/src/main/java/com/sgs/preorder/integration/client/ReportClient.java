package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.otsnotes.facade.ReportFacade;
import com.sgs.otsnotes.facade.model.info.report.ReportSimplifyInfo;
import com.sgs.otsnotes.facade.model.req.report.ReportSimplifyInfoReq;
import com.sgs.otsnotes.facade.model.rsp.report.GetTestLineConclusionRsp;
import com.sgs.preorder.facade.model.ordercopy.TestMatrixInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
public class ReportClient {
    private static final Logger logger = LoggerFactory.getLogger(ReportClient.class);
    @Autowired
    private ReportFacade reportFacade;

    /**
     *
     * @param reportNo
     * @return
     */
    public CustomResult<ReportSimplifyInfo> getReportSimplifyInfo(String reportNo){
        CustomResult<ReportSimplifyInfo> rspResult = new CustomResult(false);
        try {
            ReportSimplifyInfoReq reqObject = new ReportSimplifyInfoReq();
            reqObject.setReportNo(reportNo);

            BaseResponse<ReportSimplifyInfo> baseResponse = reportFacade.getReportSimplifyInfo(reqObject);

            rspResult.setSuccess(baseResponse.getStatus() == 200);
            rspResult.setData(baseResponse.getData());
            rspResult.setMsg(baseResponse.getMessage());
            rspResult.setStackTrace(baseResponse.getStackTrace());
        }catch (Exception ex){
            logger.error("ReportClient.getReportSimplifyInfo 获取ReportNo({})信息异常：{}.", reportNo, ex.getMessage(), ex);
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }

    /**
     *
     * @param reportId
     * @return
     */
    public List<TestMatrixInfo> getMatrixInfoForAmend(String reportId){
        List<TestMatrixInfo> testMatrixInfos = Lists.newArrayList();
        try {
            ReportSimplifyInfoReq reqObject = new ReportSimplifyInfoReq();
            reqObject.setReportId(reportId);
            BaseResponse<List<com.sgs.otsnotes.facade.model.info.matrix.TestMatrixInfo>> baseResponse = reportFacade.getMatrixInfoForAmend(reqObject);

            if (baseResponse.getStatus() == 200 && CollectionUtils.isNotEmpty(baseResponse.getData())) {
                List<com.sgs.otsnotes.facade.model.info.matrix.TestMatrixInfo> testMatrixInfoData = baseResponse.getData();
                testMatrixInfoData.forEach(matrix -> {
                    TestMatrixInfo testMatrixInfo = new TestMatrixInfo();
                    BeanUtils.copyProperties(matrix, testMatrixInfo);
                    testMatrixInfos.add(testMatrixInfo);
                });
                return testMatrixInfos;
            }
        }catch (Exception ex){
            logger.error("ReportClient.getMatrixInfoForAmend 获取ReportNo({})信息异常：{}.", reportId, ex.getMessage(), ex);
        }
        return testMatrixInfos;
    }

    public GetTestLineConclusionRsp getTestLineConclusion(String reportNo) {
        try {
            logger.info("getTestLineConclusion_reportNo_{}.", reportNo);
            ReportSimplifyInfoReq req = new ReportSimplifyInfoReq();
            req.setReportNo(reportNo);
            BaseResponse<List<GetTestLineConclusionRsp>> resp = reportFacade.getTestLineConclusion(req);
            logger.info("getTestLineConclusion_resp.", JSON.toJSONString(resp));
            if (resp.getStatus() != 200) {
                logger.info("getTestLineConclusion_reportNo_{}, message: {}", reportNo,resp.getMessage());
                return null;
            }
            return Optional.ofNullable(resp.getData()).orElse(Collections.emptyList())
                    .stream()
                    .filter(dto -> StringUtils.equalsIgnoreCase(reportNo, dto.getReportNo()))
                    .findFirst()
                    .orElse(null);
        } catch (Exception e) {
            logger.error("getTestLineConclusion_reportNo_{} error:{}", reportNo, e.getMessage());
        }
        return null;
    }
}