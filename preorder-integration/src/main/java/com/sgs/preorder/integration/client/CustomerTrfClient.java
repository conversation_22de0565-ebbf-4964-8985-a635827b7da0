package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.integration.client.dto.SciTrfCheckCreateOrderRequest;
import com.sgs.preorder.integration.client.dto.TrfClientCheckResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomerTrfClient {
    @Autowired
    private InterfaceConfig interfaceConfig;


    public CustomResult<Boolean> checkCreateOrder(SciTrfCheckCreateOrderRequest checkRequest) {
        log.info(" SciTrfCheckCreateOrderRequest:{}", checkRequest);
        CustomResult<Boolean> customResult = new CustomResult<>();
        customResult.setSuccess(true);
        String customerBizUrl = interfaceConfig.getBaseUrl() + "/customerbiz/api/todoList/checkTrfInfo";
        Map<String, Object> headers = new HashMap<>();
        headers.put("sgsToken", checkRequest.getToken());
        headers.put("productLineCode", checkRequest.getProductLineCode());
        String responseBody = null;
        try {
            responseBody = HttpClientUtil.postJson(customerBizUrl, headers, JSON.toJSONString(checkRequest), null);
            log.info("SciTrfCheckCreateOrderResponse : {}", responseBody);
            if (Func.isEmpty(responseBody)) {
                customResult.setMsg("验证是否可建单时出现系统错误");
                customResult.setSuccess(false);
                return customResult;
            }
            BaseResponse<TrfClientCheckResult> baseResponse = JsonUtil.parse(responseBody,new com.fasterxml.jackson.core.type.TypeReference<BaseResponse<TrfClientCheckResult>>(){});
            if(Func.isEmpty(baseResponse)){
                customResult.setMsg("验证是否可建单时出现系统错误");
                customResult.setSuccess(false);
                return customResult;
            }
            TrfClientCheckResult checkResult  = baseResponse.getData();

            boolean passCondition =(Func.isEmpty(baseResponse.getData())&&Func.equalsSafe(baseResponse.getStatus(),ResponseCode.SUCCESS.getCode()))
                    || (Func.equalsSafe(checkResult.getCheckStatus(), ResponseCode.SUCCESS.getCode())
                    ||Func.equalsSafe(checkResult.getCheckStatus(),201));
            if (passCondition) {
                customResult.setData(true);
                customResult.setSuccess(true);
            }else{
                customResult.fail(checkResult.getCheckMsg());
            }
            return customResult;
        } catch (Exception e) {
            customResult.setSuccess(false);
            customResult.setMsg(e.getMessage());
            log.info("SciTrfCheckCreateOrderRequest ", e);
        }
        return customResult;

    }

    public static void main(String[] args) {
        BaseResponse<TrfClientCheckResult> baseResponse = JsonUtil.parse("{\"status\":200,\"message\":null,\"data\":{\"checkStatus\":201,\"checkMsg\":\"Notification：Applicant is not in Boss，Please add first\"},\"success\":true}",new com.fasterxml.jackson.core.type.TypeReference<BaseResponse<TrfClientCheckResult>>(){});
        TrfClientCheckResult r = baseResponse.getData();
        System.out.println(r.getCheckMsg());

    }



}
