package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.common.HttpResult;
import com.sgs.otsnotes.facade.model.dto.FileDTO;
import com.sgs.otsnotes.facade.model.req.framework.FrameworkUploadFileReq;
import com.sgs.otsnotes.facade.model.rsp.framework.UploadFileResult;
import com.sgs.preorder.core.config.GPOConfig;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.info.FileInfo;
import com.sgs.preorder.facade.model.info.UploadFileInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;

@Component
public class FileClient {
    private static final Logger logger = LoggerFactory.getLogger(FileClient.class);
    @Resource
    private InterfaceConfig interfaceConfig;
    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private GPOConfig gpoConfig;
    @Autowired
    private FrameWorkClient frameWorkClient;
    /**
     *
     * @param reportFiles
     * @param file
     * @return
     */
    public CustomResult uploadFile(ConcurrentHashMap<String, String> reportFiles, UploadFileInfo file){
        CustomResult result = new CustomResult();
        try{
            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()+""));
            reqParams.append(String.format("&buID=%s", file.getBuId()));
            reqParams.append(String.format("&locationID=%s", file.getLocationID()));
            reqParams.append(String.format("&orderID=%s", file.getOrderId()));
            reqParams.append(String.format("&objectID=%s", file.getId()));
            reqParams.append(String.format("&objectType=%s", 2));

            /*String uploadUrl = String.format("http://10.205.139.111/FrameWorkApi/file/doUpload?systemID=3&buID=3&locationID=11&orderID=%s&objectID=%s&objectType=2",testLine.getId(),testLine.getGeneralorderinstanceid());*/
            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());

            UrlResource resource = new UrlResource(java.net.URLDecoder.decode(file.getImageUrl(),"UTF-8"));
            if (!resource.exists()){
                result.setMsg("该文件不存在或已过期.");
                return result;
            }

            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            params.add("file", resource);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

            // 发送rest请求往FrameworkApi接口中存储新的文件
            String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
            BaseResponse<List<FileInfo>> rspResults = JSON.parseObject(jsonValue, new TypeReference<BaseResponse<List<FileInfo>>>() {});
            List<FileInfo> files = rspResults.getData();
            if (files == null || files.isEmpty()){
                result.setMsg("上传附件失败返回对象为空.");
                return result;
            }
            reportFiles.put(file.getImageUrl(), files.get(0).getId());
        }catch (Exception ex){
            logger.error("上传文件({})异常：",file.getId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", file.getId(), ex.getMessage()));
        }
        return result;
    }

    /**
     *
     * @return
     */
    public void deleteBatchFile(String fileIds,String token){
        logger.info("请求接口FrameWorkClient.deleteBatch({}).", fileIds);
        String frameWorkApi = String.format("%s/FrameWorkApi/file/deleteBatch?sgsToken=" + token + "&ids=" + fileIds, interfaceConfig.getBaseUrl());
        restTemplate.delete(frameWorkApi);
    }

    /**
     *
     * @param file
     * @param resource
     * @param function
     * @return
     */
    public CustomResult uploadFile(UploadFileInfo file, AbstractResource resource, Function<FileInfo, CustomResult> function){
        CustomResult result = new CustomResult();
        try{
            StringBuffer reqParams = new StringBuffer();
            reqParams.append("systemID=").append(SgsSystem.GPO.getSgsSystemId()+"");
            reqParams.append("&buID=").append(file.getBuId());
            reqParams.append("&locationID=").append(file.getLocationID());
            reqParams.append("&orderID=").append(file.getOrderId());
            reqParams.append("&objectID=").append(file.getObjectId());
            reqParams.append("&objectType=").append(file.getObjectType());

            /*String uploadUrl = String.format("http://10.205.139.111/FrameWorkApi/file/doUpload?systemID=3&buID=3&locationID=11&orderID=%s&objectID=%s&objectType=2",testLine.getId(),testLine.getGeneralorderinstanceid());*/
            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());
            // 发送rest请求往FrameworkApi接口中存储新的文件
            return CompletableFuture.supplyAsync(() -> {
                MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
                params.add("file", resource);
                if (file.getReturnCompressPictureFlag() != null && file.getReturnCompressPictureFlag()){
                    params.add("returnCompressPictureFlag", file.getReturnCompressPictureFlag());
                }
                if (file.getCompressWidthLimit() != null){
                    params.add("compressWidthLimit", file.getCompressWidthLimit());
                }
                if (file.getCompressHeightLimit() != null){
                    params.add("compressHeightLimit", file.getCompressHeightLimit());
                }

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

                String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
                if(Func.isBlank(jsonValue)){
                    return null;
                }
                logger.info(jsonValue);
                JSONObject jsonObject=JSON.parseObject(jsonValue);
                String arrayListStr=jsonObject.getString("data");
                List<FileInfo> files=JSON.parseArray(arrayListStr,FileInfo.class);
/*                BaseResponse<List<FileInfo>> rspResults = JSON.parseObject(jsonValue, new TypeReference<BaseResponse<List<FileInfo>>>() {});
                List<FileInfo> files = rspResults.getData();*/
                if (files != null && !files.isEmpty()){
                    return files.get(0);
                }
                return null;
            }).thenApply(function).get(30000, TimeUnit.SECONDS);
        }catch (Exception ex){
            logger.error("上传文件({})异常：",file.getOrderId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", file.getOrderId(), ex.getMessage()));
        }
        return result;
    }

    public CustomResult<List<FileInfo>> uploadFileAndCallback(UploadFileInfo file, AbstractResource resource,Function<List<FileInfo>, CustomResult> callback){
        logger.info("调用uploadFileAndCallback上传，入参："+JSON.toJSONString(file));
        CustomResult<List<FileInfo>> result = new CustomResult();
        StringBuffer reqParams = new StringBuffer();
        reqParams.append("systemID=").append(SgsSystem.GPO.getSgsSystemId()+"");
        reqParams.append("&buID=").append(file.getBuId());
        reqParams.append("&locationID=").append(file.getLocationID());
        reqParams.append("&orderID=").append(file.getOrderId());
        reqParams.append("&objectID=").append(file.getObjectId());
        reqParams.append("&objectType=").append(file.getObjectType());
        String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("file", resource);
        if (file.getReturnCompressPictureFlag() != null && file.getReturnCompressPictureFlag()){
            logger.info("file.objectid="+file.getObjectId()+",returnCompressPictureFlag="+file.getReturnCompressPictureFlag());
            params.add("returnCompressPictureFlag", file.getReturnCompressPictureFlag());
        }
        if (file.getCompressWidthLimit() != null){
            logger.info("file.objectid="+file.getObjectId()+",compressWidthLimit="+file.getCompressWidthLimit());
            params.add("compressWidthLimit", file.getCompressWidthLimit());
        }
        if (file.getCompressHeightLimit() != null){
            logger.info("file.objectid="+file.getObjectId()+",compressHeightLimit="+file.getCompressHeightLimit());
            params.add("compressHeightLimit", file.getCompressHeightLimit());
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

        try {
            result = CompletableFuture.supplyAsync(() -> {
                String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
                logger.info("调用url=[{}],返回值jsonValue=[{}]",uploadUrl,jsonValue);
                BaseResponse<List<FileInfo>> rspResults = JSON.parseObject(jsonValue, new TypeReference<BaseResponse<List<FileInfo>>>() {});
                return rspResults.getData();
            }).thenApplyAsync(callback).get(30000, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            logger.error("上传文件["+file.getOrderId()+"]InterruptedException异常", ex);
            //jira-11232
            Thread.currentThread().interrupt();
        } catch (ExecutionException ex) {
            logger.error("上传文件["+file.getOrderId()+"]ExecutionException异常：", ex);
        } catch (TimeoutException ex) {
            logger.error("上传文件["+file.getOrderId()+"]TimeoutException异常：", ex);
        }
        return result;
    }

    /**
     *
     * @param file
     * @return
     */
    public CustomResult copyFile(FileInfo file){
        CustomResult rspResult = new CustomResult();
        try{
            HashMap<String, Object> reqParams = new HashMap();
            reqParams.put("id", file.getId());
            reqParams.put("generalOrderID", file.getOrderId());
            reqParams.put("objectID", file.getObjectID());
            if(Func.isEmpty(file.getId())&&Func.isNotEmpty(file.getCloudID())){
                reqParams.put("cloudID", file.getCloudID());
            }
            String frameWorkApi = String.format("%s/FrameWorkApi/file/copyTbfile", interfaceConfig.getBaseUrl());

            HttpResult<FileInfo> fileResult = HttpClientUtil.post(frameWorkApi, reqParams, new TypeReference<HttpResult<FileInfo>>() {});
            if (fileResult == null || !fileResult.isSuccess()){
                return null;
            }
            FileInfo rspFile = fileResult.getResult();
            rspResult.setData(rspFile);
            rspResult.setSuccess(rspFile != null);
        }catch (Exception ex){
            logger.error("copy文件({})异常：",file.getId(), ex);
            rspResult.setMsg(String.format("copy文件(%s)异常：%s", file.getId(), ex.getMessage()));
        }
        return rspResult;
    }
    /**
     * 批量上传文件到frameWork,只有全部成功时才返回
     * 上传过称中出现失败，则将前面上传成功的文件删除掉
     * @param frameworkUploadFileReq
     * @param multipartFiles
     * @return
     */
    public BaseResponse<List<UploadFileResult.Data>> uploadFileToFramework(FrameworkUploadFileReq frameworkUploadFileReq, MultipartFile[] multipartFiles){
        if(Func.isEmpty(frameworkUploadFileReq) || Func.isEmpty(frameworkUploadFileReq.getParentDirName()) || Func.isEmpty(frameworkUploadFileReq.getBuId())){
            return BaseResponse.newFailInstance("upload Param is Empty");
        }
        //执行上传
        String tempFilePath = gpoConfig.getRootPath() + File.separator + "file" +File.separator +"gpn"+ File.separator +"reworkfile";
        List<UploadFileResult.Data> uploadSuccessFileList = new ArrayList<>();
        if(Func.isNotEmpty(multipartFiles)){
            for (MultipartFile multipartFile : multipartFiles) {
                String originalFilename = multipartFile.getOriginalFilename();
                File fileSourcePath = new File(tempFilePath);
                File uploadFile = new File(fileSourcePath, originalFilename);
//                InputStream ins = null;
//                OutputStream os = null;
                try {
//                    ins = multipartFile.getInputStream();
                    if (!fileSourcePath.exists()) {
                        fileSourcePath.mkdirs();
                    }
                    if (uploadFile.exists()) {
                        uploadFile.delete();
                    }
                    multipartFile.transferTo(uploadFile);
                    UploadFileResult uploadFileResult = frameWorkClient.doUploadFile(uploadFile, frameworkUploadFileReq.getBuId() + "", frameworkUploadFileReq.getObjectId(), frameworkUploadFileReq.getObjectType());
                    //上传失败
                    if(Func.isEmpty(uploadFileResult) || uploadFileResult.getStatus()!=200 || Func.isEmpty(uploadFileResult.getData()) || Func.isEmpty(uploadFileResult.getData().get(0).getCloudID())){
                        //把其他的文件删除
                        if(Func.isNotEmpty(uploadSuccessFileList)){
                            //调用接口删除
                            for (UploadFileResult.Data item : uploadSuccessFileList) {
                                if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getCloudID())){
                                    int deleteFileCount = frameWorkClient.deleteFileByCloudId(item.getCloudID());
                                }
                            }
                        }
                        return BaseResponse.newFailInstance("upload File Fail");
                    }else{
                        String cloudID = uploadFileResult.getData().get(0).getCloudID();
                        uploadSuccessFileList.add(uploadFileResult.getData().get(0));
                    }
                } catch (Exception e) {
                    //把其他的文件删除
                    for (UploadFileResult.Data item : uploadSuccessFileList) {
                        if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getCloudID())){
                            int deleteFileCount = frameWorkClient.deleteFileByCloudId(item.getCloudID());
                        }
                    }
                } finally {
                    /*try {
                        if (os != null) {
                            os.flush();
                            os.close();
                        }
                        if (ins != null) {
                            ins.close();
                        }
                        FileUtils.deleteQuietly(uploadFile);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }*/

                }
            }
        }
        return BaseResponse.newSuccessInstance(uploadSuccessFileList);
    }
    public BaseResponse<List<FileDTO>> queryFile(List<String> objectIdList, String objectType){
        if(Func.isEmpty(objectIdList)){
            logger.info("queryFile from framework fail,objectId is empty");
            return  BaseResponse.newFailInstance("objectId can not empty");
        }

        List<FileDTO> fileDTOList = new ArrayList<>();
        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/file/query";
        Map<String,Object> params= Maps.newHashMap();
        Map<String,Object> value= Maps.newHashMap();
        List<Map<String,Object>>  objectList = new ArrayList<>();
        for (String objectId : objectIdList) {
            Map<String,Object> objectListMap= Maps.newHashMap();
            objectListMap.put("objectId",objectId);
            objectList.add(objectListMap);
        }
        value.put("systemID", SgsSystem.GPO.getSgsSystemId());
        value.put("objectList", JSON.toJSON(objectList));
        params.put("queryParams", value);

        String respStr = StringUtils.EMPTY;
        try {
            respStr = HttpClientUtil.postJson(url, JSONObject.toJSONString(params));
            fileDTOList = JSONArray.parseArray(respStr, FileDTO.class);
        } catch (Exception e) {
            logger.error("objectList="+JSON.toJSONString(objectIdList)+"+获取file异常:{}", e);
        }
        return BaseResponse.newSuccessInstance(fileDTOList);
    }


//    public static void main(String[] args) {
//        FileInfo fileInfo = new FileInfo();
////        fileInfo.setId("e20d6f14-9ac9-4594-9fae-815846dedace");
//        fileInfo.setOrderId("5a6c7165-5966-49cd-a556-0736012cd2e9");
//        fileInfo.setObjectID(fileInfo.getOrderId());
//        fileInfo.setCloudID("PreOrder/2022/09/GZPL2206003204PS02_1664426674854.pdf");
//        CustomResult r = copyFile2(fileInfo);
//        System.out.println(JSON.toJSONString(r));
//    }

}
