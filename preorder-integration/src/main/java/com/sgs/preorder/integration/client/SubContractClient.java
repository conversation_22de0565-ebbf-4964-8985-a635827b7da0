package com.sgs.preorder.integration.client;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.SubContractFacade;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractExternalRelationshipDTO;
import com.sgs.otsnotes.facade.model.info.subcontract.ExternalRelSubContractDTO;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.req.GPOSubContractReq;
import com.sgs.otsnotes.facade.model.req.GetOtsSubContractReq;
import com.sgs.otsnotes.facade.model.req.subcontract.GetSubContractInfo;
import com.sgs.otsnotes.facade.model.req.subcontract.GetSubContractListReq;
import com.sgs.otsnotes.facade.model.req.subcontract.QuerySubContractListReq;
import com.sgs.preorder.core.util.CopyOrderUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SubContractClient {
    private static final Logger logger = LoggerFactory.getLogger(SubContractClient.class);
    @Autowired
    private SubContractFacade subContractFacade;

    /**
     *
     * @param subContractNo
     * @return
     */
    public SubContractInfo getSubContractInfo(String subContractNo){
        GetSubContractInfo reqObject = new GetSubContractInfo();
        reqObject.setSubContractNo(subContractNo);
        reqObject.setProductLineCode(CopyOrderUtils.getSourceProductLineCode());//设置bu为source productlinecode
        try {
            BaseResponse<SubContractInfo> rspResult = subContractFacade.getSubContractInfo(reqObject);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("SubContractClient.getSubContractInfo 获取内部分包({})信息异常：{}.", subContractNo, ex.getMessage(), ex);
        }
        return null;
    }


    /**
     *
     * @param orderNo
     * @param token
     * @param productLineCode
     * @return
     */
    public List<SubContractExternalRelationshipDTO> querySubContractList(String orderNo, String token, String productLineCode){
        QuerySubContractListReq reqObject = new QuerySubContractListReq();
        reqObject.setOrderNo(orderNo);
        reqObject.setProductLineCode(productLineCode);
        reqObject.setToken(token);
        try {
            BaseResponse<List<SubContractExternalRelationshipDTO>> baseResponse = subContractFacade.querySubContractList(reqObject);
            return baseResponse.getData();
        }catch (Exception ex){
            logger.error("SubContractClient.getSubContractInfo 获取内部分包({})信息异常：{}.", orderNo, ex.getMessage(), ex);
        }
        return null;
    }
    /**
     *
     * @param subContractNoList
     * @return
     */
    public List<SubContractInfo> getSubContractInfoList(List<String> subContractNoList){
        GetSubContractListReq reqObject = new GetSubContractListReq();
        reqObject.setSubContractNoList(subContractNoList);
        reqObject.setProductLineCode(CopyOrderUtils.getSourceProductLineCode());//设置bu为source productlinecode
        try {
            BaseResponse<List<SubContractInfo>> rspResult = subContractFacade.getSubContractInfoList(reqObject);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("SubContractClient.getSubContractInfo 获取内部分包({})信息异常：{}.", StringUtils.join(subContractNoList,","), ex.getMessage(), ex);
        }
        return null;
    }

    /**
     *
     * @return
     */
    public List<ExternalRelSubContractDTO> getExternalSubContractInfo(){
        try {
            BaseResponse<List<ExternalRelSubContractDTO>> rspResult = subContractFacade.getExternalSubContractInfo();
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("SubContractClient.getExternalSubContractInfo 获取内部分包({})信息异常：{}.", ex.getMessage(), ex);
        }
        return null;
    }


    /**
     * 判断 order是否通过 subcontract 生成的
     * @param orderNo
     * @return
     */
    public SubContractInfo getSubContractByOrderNo(String orderNo){
        GPOSubContractReq reqObject = new GPOSubContractReq();
        reqObject.setOrderNo(orderNo);
        reqObject.setProductLineCode(CopyOrderUtils.getSourceProductLineCode());//设置bu为source productlinecode
        try {
            BaseResponse<SubContractInfo> rspResult = subContractFacade.queryOrderIsSubOrder(reqObject);
            if(ResponseCode.SUCCESS.getCode() == rspResult.getStatus())
            {
                return rspResult.getData();
            }else
            {
                return null;
            }
        }catch (Exception ex){
            logger.error("SubContractClient.getSubContractInfo 获取内部分包({})信息异常：{}.", orderNo, ex.getMessage(), ex);
        }
        return null;
    }

    public Integer getOtsSubcontractCount(String subcontractNo){
        GetOtsSubContractReq getOtsSubContractReq  = new GetOtsSubContractReq();
        getOtsSubContractReq.setSubContractNo(subcontractNo);
        BaseResponse<Integer>   baseResponse = subContractFacade.getOtsSubcontractCount(getOtsSubContractReq);
        if(Func.isEmpty(baseResponse.getData())){
            return 0;
        }else{
            return baseResponse.getData();
        }
    }
}
