package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.ExecuteResult;
import com.sgs.core.exception.SGSException;
import com.sgs.core.utils.HttpClientPool;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.otsnotes.facade.OrderFacade;
import com.sgs.otsnotes.facade.SampleFacade;
import com.sgs.otsnotes.facade.SubContractFacade;
import com.sgs.otsnotes.facade.TestMatrixFacade;
import com.sgs.otsnotes.facade.model.enums.OrderCopyType;
import com.sgs.otsnotes.facade.model.req.CancelOrderReq;
import com.sgs.otsnotes.facade.model.req.GPOSubContractReq;
import com.sgs.otsnotes.facade.model.req.OrderReq;
import com.sgs.otsnotes.facade.model.req.SampleReq;
import com.sgs.otsnotes.facade.model.req.gpn.MatrixListForConclusionReq;
import com.sgs.otsnotes.facade.model.req.gpn.MatrixListForOrderCopyReq;
import com.sgs.otsnotes.facade.model.rsp.SampleAndSampleGroupRsp;
import com.sgs.otsnotes.facade.model.rsp.SampleGroupRsp;
import com.sgs.otsnotes.facade.model.rsp.SampleRsp;
import com.sgs.preorder.core.config.BizConfigExt;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.dto.order.GeneralOrderDTO;
import com.sgs.preorder.facade.model.dto.report.AmendReportDTO;
import com.sgs.preorder.facade.model.dto.report.SliptReportCopyDTO;
import com.sgs.preorder.facade.model.dto.report.TestLineConclusionDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class NotesClient {
    private static final Logger logger = LoggerFactory.getLogger(NotesClient.class);

    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private BizConfigExt bizConfigExt;
    @Autowired
    private SubContractFacade subContractFacade;
    @Autowired
    private TestMatrixFacade gpnMatrixFacade;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private SampleFacade sampleFacade;
    @Autowired
    private TokenClient tokenClient;

    public BaseResponse deleteSample(String orderNo,String refSampleID){
        BaseResponse baseResponse=new BaseResponse();

        //todo old ots interface
        try {
            SampleReq sampleReq = new SampleReq();
            sampleReq.setOrderNo(orderNo);
            sampleReq.setRefSampleID(refSampleID);
            sampleReq.setSampleId(refSampleID);
            logger.info("delete sample，req：{}",Func.toJson(sampleReq));
            baseResponse = sampleFacade.deleteSample(sampleReq);
            logger.info("delete sample，rsp：{}",Func.toJson(baseResponse));
        } catch (Exception e) {
            baseResponse.setStatus(500);
            baseResponse.setMessage("delete otsNotes sample is failed");
            logger.error(e.getMessage(),e);
            return baseResponse;
        }
        return baseResponse;
    }

    public BaseResponse<List<String>> cancelOrder(GeneralOrderDTO generalOrderDTO){
        CustomResult executeResult=new CustomResult();
        try {
            CancelOrderReq cancelOrderReq = new CancelOrderReq();
            cancelOrderReq.setOrderNo(generalOrderDTO.getOrderNo());
            cancelOrderReq.setLab(generalOrderDTO.getLabCode());
            cancelOrderReq.setUserName(generalOrderDTO.getUserName());
            cancelOrderReq.setProductLineCode(generalOrderDTO.getBuCode());
            BaseResponse<List<String>> listBaseResponse = orderFacade.cancelOrder(cancelOrderReq);
            if(listBaseResponse==null||listBaseResponse.getStatus()!=200){
                return listBaseResponse;
            }
            return BaseResponse.newSuccessInstance(CollectionUtils.isEmpty(listBaseResponse.getData())? Lists.newArrayList():listBaseResponse.getData());
          /*  String url = String.format("%s/interface/api/v1/cancel/order", interfaceConfig.getOtsnotesApi());
            Map<String, Object> headers = Maps.newHashMap();
            headers.put("sgsToken",sgsToken);
            HashMap<String, Object> paramsMaps = Maps.newHashMap();
            paramsMaps.put("orderNo",orderNo);
            paramsMaps.put("sgsToken",sgsToken);
            BaseResponse rspObject = HttpUtils.httpPost(url, headers, paramsMaps, new TypeReference<BaseResponse>(){});
            if(rspObject==null||rspObject.getStatus()!=200){
                return null;
            }

            return rspObject.getData()==null? Lists.newArrayList():(List<String>)rspObject.getData();*/
        } catch (Exception e) {
            executeResult.setSuccess(false);
            executeResult.setMsg("delete otsNotes sample is failed");
            logger.error(e.getMessage(),e);
            return null;
        }
    }

    public List<TestLineConclusionDTO> getTestLineConclusion(String orderNo){
        //todo old ots interface
        String otsnotesApiUrl = String.format("%s/OTSNotesApi/report/api/v1/getTestLineConclusion?orderNo=" + orderNo, interfaceConfig.getBaseUrl());
        try {
            List<TestLineConclusionDTO> testLineConclusionDTOs = new ArrayList<TestLineConclusionDTO>();
            String rspMsg=HttpClientUtil.get(otsnotesApiUrl);
            ExecuteResult<Object> executeResult1 = JSONObject.parseObject(rspMsg, ExecuteResult.class);
            logger.info("getReportFileByOrderNo_OrderNo_{}，responseText：{}.", orderNo, rspMsg);
            if (executeResult1 != null && executeResult1.getResult() != null) {
                testLineConclusionDTOs = JSONObject.parseArray(executeResult1.getResult().toString(),
                        TestLineConclusionDTO.class);
                return testLineConclusionDTOs;
            }
            if (executeResult1==null || !executeResult1.getIsSuccess()) {
                return null;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return null;
        }
        return null;
    }


    /**
     *todo call old
     *
     * @param amendReport
     */
    public ExecuteResult<Object> syncCopyNotes(AmendReportDTO amendReport){
        ExecuteResult<Object> rspResult = new ExecuteResult<>();
        try {
            String otsnotesApiUrl = String.format("%s/OTSNotesApi/report/api/v1/copyAmendPeport", interfaceConfig.getBaseUrl());
            rspResult=HttpClientUtil.post(otsnotesApiUrl,amendReport,ExecuteResult.class);
            if(!rspResult.getIsSuccess()){
                logger.error("copyAmendPeport error : {}", rspResult.getErrorMessage());
                throw new BizException(rspResult.getErrorMessage());
            }
        } catch (Exception ex) {
            logger.error("copyAmendPeport error : {}", ex.getMessage(), ex);
            throw new BizException(String.format("调用copyAmendPeport API接口出错：%s", ex.getMessage()));
        }
        return rspResult;
    }

    /**
     *todo call old
     * @param amendReport
     * @return
     */
    //TODO 需要改造为 dubbo 接口
    public ExecuteResult<Object> syncNotesInfo(AmendReportDTO amendReport){
        ExecuteResult<Object> execResult = new ExecuteResult<>();
        execResult.setIsSuccess(false);
        try {
            amendReport.setCopyType(OrderCopyType.AmendReport.getCopyType());

            String otsnotesApiUrl = String.format("%s/order/copyOrderInfo?productLineCode="+ ProductLineContextHolder.getProductLineCode() +"&sgsToken="+amendReport.getSgsToken(), "http://localhost:8086/gpn-api/");
            BaseResponse rspResult=HttpClientUtil.post(otsnotesApiUrl,amendReport,BaseResponse.class);
            if (rspResult.getStatus() != 200) {
                logger.error("copyOrderInfo OrderNo({}) error : {}", amendReport.getOrderNo(), rspResult.getMessage());
                throw new BizException( rspResult.getMessage());
            }
            execResult.setIsSuccess(true);
        } catch (Exception ex) {
            logger.error("copyOrderInfo error : {}", ex.getMessage(), ex);
            throw new BizException(String.format("调用copyOrderInfo API接口出错：%s", ex.getMessage()));
        }
        return execResult;
    }

    /**
     *saveOrderCopyByType
     * @param paramsMaps
     * @return
     */
    public ExecuteResult newByCopyInfo(Map<String, Object> paramsMaps){
        ExecuteResult rspResult = new ExecuteResult();
        /*try {
            Map<String, Object> headers = Maps.newHashMap();
            headers.put("sgsToken", paramsMaps.get("sgsToken"));
            String otsApiUrl = String.format("%s/order/newByCopyInfo", interfaceConfig.getOtsnotes2Api());
            //otsApiUrl = "http://localhost:8086/otsnotes2api/order/newByCopyInfo";
            logger.info("正在处理订单newByCopyInfo_{}.",  paramsMaps.get("orderNO"));
            BaseResponse rspObject=HttpClientUtil.sendPost(otsApiUrl,headers,paramsMaps,BaseResponse.class);
           // BaseResponse rspObject = HttpUtils.httpPost(otsApiUrl, headers, paramsMaps, new TypeReference<BaseResponse>(){});
            rspResult.setIsSuccess(rspObject.getStatus() == 200);
            rspResult.setResult(rspObject.getData());
            rspResult.setErrorMessage(rspObject.getMessage());

            logger.info("已处理订单newByCopyInfo_{}.",  paramsMaps.get("orderNO"));
            if (rspObject.getStatus() != 200){
                throw new SGSException("OtsNotesNewClient.newByCopyInfo", String.format("调用newByCopyInfo API接口异常 ：%s", rspObject.getMessage()));
            }
        }catch (Exception ex){
            logger.error("OtsNotesNewClient.newByCopyInfo 信息异常：{}.", ex);
            throw new SGSException("OtsNotesNewClient.newByCopyInfo", String.format("调用newByCopyInfo API接口异常 ：%s", ex.getMessage()));
        }*/
        rspResult.setIsSuccess(true);
        return rspResult;
    }

    public void getProductInstanceFromNotes(String orderNo, Set<String> sampleSet, Set<String> rootSample){
        //todo old ots interface
        OrderReq orderReq = new OrderReq();
        orderReq.setOrderNo(orderNo);
        BaseResponse<SampleAndSampleGroupRsp> sampleAndSampleGroupByOrderNo = orderFacade.getSampleAndSampleGroupByOrderNo(orderReq);
        /*String otsnotesApiUrl = String.format("%s/Sample/api/v1/querySampleAndSampleGroupByOrderNo/"
                + orderNo, interfaceConfig.getOtsnotesApi());*/
        try {
            //String rspMsg=HttpClientUtil.get(otsnotesApiUrl);
            if (Func.isNotEmpty(sampleAndSampleGroupByOrderNo.getData())) {
                //ExecuteResult<Map<String, JSONArray>> executeResult = JSONObject.parseObject(rspMsg, ExecuteResult.class);
                //Map<String, JSONArray> map = executeResult.getResult();
                //JSONArray sampleList = map.get("sampleList");
                //JSONArray sampleGroupList = map.get("sampleGroupList");
                List<SampleGroupRsp> sampleGroupRsps = sampleAndSampleGroupByOrderNo.getData().getSampleGroupRsps();
                List<SampleRsp> sampleRsps = sampleAndSampleGroupByOrderNo.getData().getSampleRsps();
                if (sampleGroupRsps != null) {
                    for (int i = 0; i < sampleGroupRsps.size(); i++) {
                        //JSONObject sampleGroup = sampleGroupList.getJSONObject(i);

                        if (sampleSet.contains(sampleGroupRsps.get(i).getSampleID())) {
                            sampleSet.add(sampleGroupRsps.get(i).getSampleGroupID());
                        }
                    }
                }
                for (int i = 0; i < sampleRsps.size(); i++) {
                    SampleRsp sample = sampleRsps.get(i);
                    if (sampleSet.contains(sample.getId())) {
                        if (StringUtils.isNotEmpty(sample.getSampleParentID())) {
                            getOriginalSample(sample.getSampleID(), sampleRsps, rootSample);
                        } else {
                            if (101 == sample.getSampleType()) {
                                rootSample.add(sample.getId());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
    }

    private void getOriginalSample(String sampleID, List<SampleRsp> sampleList, Set<String> rootSample) {
        for (int i = 0; i < sampleList.size(); i++) {
            SampleRsp sample = sampleList.get(i);
            if (StringUtils.equals(sample.getId(), sampleID)) {
                if (StringUtils.isNotEmpty(sample.getSampleParentID())) {
                    getOriginalSample(sample.getSampleParentID(), sampleList, rootSample);
                } else {
                    if (101 == sample.getSampleType()) {
                        rootSample.add(sample.getId());
                    }
                }
            }
        }
    }

    /**
     *
     * @param copySliptReportDTO
     * @return
     */
    public ExecuteResult copyReportInfo(SliptReportCopyDTO copySliptReportDTO){

        ExecuteResult<Object> execResult = new ExecuteResult<>();
        execResult.setIsSuccess(false);
        try {
            copySliptReportDTO.setCopyType(OrderCopyType.SplitReport.getCopyType());
            String otsnotesApiUrl = String.format("%s/gpn-api/order/copyOrderInfo?productLineCode="+ ProductLineContextHolder.getProductLineCode()+"&sgsToken="+copySliptReportDTO.getSgsToken(), interfaceConfig.getBaseUrl());
            //String otsnotesApiUrl = String.format("%s/order/copyOrderInfo?productLineCode="+ProductLineContextHolder.getProductLineCode()+"&sgsToken="+copySliptReportDTO.getSgsToken(), "http://localhost:8086/gpn-api");
            BaseResponse rspResult=HttpClientUtil.post(otsnotesApiUrl,copySliptReportDTO,BaseResponse.class);
            if (rspResult.getStatus() != 200) {
                logger.error("copyOrderInfo OrderNo({}) error : {}", copySliptReportDTO.getOrderNo(), rspResult.getMessage());
                throw new BizException( rspResult.getMessage());
            }
            execResult.setIsSuccess(true);
        } catch (Exception ex) {
            logger.error("copyOrderInfo error : {}", ex.getMessage(), ex);
            throw new BizException(String.format("调用copyOrderInfo API接口出错：%s", ex.getMessage()));
        }
        return execResult;
    }

    public ExecuteResult<List<String>> getMatrixListFromNotesForNewSplit(String orderNo,Map<String, List<String>> testLineSampleList) {
        ExecuteResult<List<String>> executeResult = new ExecuteResult<>();
        MatrixListForConclusionReq objMatrixListForConclusionReq=new MatrixListForConclusionReq();
        objMatrixListForConclusionReq.setOrderNo(orderNo);
        objMatrixListForConclusionReq.setTestLineSampleList(testLineSampleList);
        BaseResponse<List<String>> baseResponse=gpnMatrixFacade.queryMatrixListForOrderCopySplitByConclusion(objMatrixListForConclusionReq);

        if (baseResponse==null||baseResponse.getStatus()!=200) {
            executeResult.setIsSuccess(false);
            return executeResult;
        }
        executeResult.setIsSuccess(true);
        executeResult.setResult(baseResponse.getData());
        return executeResult;
    }

    public ExecuteResult<List<String>> getMatrixListFromNotes(String orderNo, Set<String> sampleList) {
        ExecuteResult<List<String>> executeResult = new ExecuteResult<>();
        MatrixListForOrderCopyReq objMatrixListForOrderCopyReq=new MatrixListForOrderCopyReq();
        objMatrixListForOrderCopyReq.setOrderNo(orderNo);
        objMatrixListForOrderCopyReq.setSampleList(sampleList.stream().collect(Collectors.toList()));
        BaseResponse<List<String>> baseResponse=gpnMatrixFacade.queryMatrixListForOrderCopy(objMatrixListForOrderCopyReq);

        if (baseResponse==null||baseResponse.getStatus()!=200) {
            executeResult.setIsSuccess(false);
            return executeResult;
        }
        executeResult.setIsSuccess(true);
        executeResult.setResult(baseResponse.getData());
        return executeResult;
    }

    /**
     *
     * @param sgsToken
     * @param oldOrderNo
     * @param oldReportNo
     */
    public void cancelReport(String sgsToken, String oldOrderNo, String oldReportNo) {
        // ots原单的report单要cancel
        Map<String, Object> notesCancelReportMap = new HashMap<>();
        notesCancelReportMap.put("reportNo", oldReportNo);
        notesCancelReportMap.put("sgsToken", sgsToken);
        try {
            String otsApiUrl = String.format("%s/OTSNotesApi/report/api/v1/cancelNotesReportOnly?reportNo="
                    + oldReportNo, interfaceConfig.getBaseUrl());
            HttpEntity entity = HttpClientPool.httpGet(otsApiUrl, notesCancelReportMap);
            String resultString = EntityUtils.toString(entity, "UTF-8");
            logger.info("====调用notes cancel report 返回值：" + resultString);
        } catch (Exception e) {
            logger.error("saveCopyReport error : {}", e.getMessage(), e);
            throw new SGSException("call_notes_cancel_report_failed", "调用 notes cancel report 失败。。。。");
        }
    }




    public String cancelTestSample(String testSampleId,String buCode){
        String sampleApiUrl = String.format("%s/api/sgs-sample/sample/cancel-sample?productLineCode="+buCode+"&sgsToken="+tokenClient.getToken(), interfaceConfig.getBaseUrl());
        try {
            Map<String,String> paramMap= Maps.newHashMap();
            paramMap.put("id",testSampleId);
            paramMap.put("buCode",buCode);

            String rspMsg=HttpClientUtil.postJson(sampleApiUrl, JSON.toJSONString(paramMap));

            JSONObject jsonObj = JSON.parseObject(rspMsg);

            int status = jsonObj.get("status")==null?500:Integer.parseInt(jsonObj.get("status").toString()); // 接口是否成功
            if (status!=200) {
                return jsonObj.get("msg")==null?"cancel test sample is failed":jsonObj.get("msg").toString();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return "cancel test sample is failed";
        }
        return "";
    }
}
