package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.integration.client.dto.CancelPrePaymentReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class BackOfficeClient {
    private static final Logger logger = LoggerFactory.getLogger(BackOfficeClient.class);

    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private SystemLogHelper systemLogHelper;

    /**
     * 获取BackOffice待处理的文件列表
     *
     * @param map
     * @return
     */
    public List<JSONObject> getPaymentData(Map<String, Object> map) {
        List<JSONObject> objectList = new ArrayList<>();
        String url = interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.QUERY_BACK_OFFICE_INTERFACE_FILE;
//        String result = "[{\"systemId\":\"9\",\"fileName\":\"F619201_HL_Invoice_20210729152137.txt\",\"bu\":\"HL\",\"cloudID\":\"BackOffice/2021/07/F619201_HL_Invoice_20210729152137_1627543297348.txt\",\"dataType\":\"Invoice\",\"id\":\"9069f6c6f61b46fab5571c9443056e93\",\"fcode\":\"F619201\",\"fileID\":\"f5e5d711-df07-4ae9-9e28-f8501bfae98f\"}]";
        String result = null;
        //保存日志
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType("获取BackOffice待处理的文件列表");
        systemLog.setObjectNo("");
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(SystemLogType.API.getType());
        systemLog.setRemark("获取BackOffice待处理的文件列表");
        systemLog.setRequest(JSON.toJSON(map).toString());
        //logger.info("=========获取BackOffice待处理的文件列表. URL:{},参数：{}", interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.QUERY_BACK_OFFICE_INTERFACE_FILE, JSON.toJSON(map));
        try {
            result = HttpClientUtil.post(url, map);
        } catch (Exception e) {
            e.printStackTrace();
            systemLog.setResponse(e.getMessage());
            logger.info("=========获取BackOffice待处理的文件列表. URL:{},错误信息：{}", interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.QUERY_BACK_OFFICE_INTERFACE_FILE, e.getMessage());
        }
        //logger.info("=========获取BackOffice待处理的文件列表. URL:{},返回值：{}", interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.QUERY_BACK_OFFICE_INTERFACE_FILE, result);
        if (Func.isEmpty(systemLog.getResponse())){
            systemLog.setResponse(result);
        }
        systemLogHelper.save(systemLog);
        if (Func.isEmpty(result)) {
            return objectList;
        }
        objectList = JSONArray.parseArray(result, JSONObject.class);
        return objectList;
    }

    /**
     * 读取OSS服务器文件
     *
     * @param map
     * @return
     */
    public String getFile(Map<String, Object> map) {
        String fileText = null;
        String fileUrl = null;
        String url = interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.FILE_DOWNLOAD_BY_CLOUD_ID;
//        String url = "http://cnapp-uat.sgs.net/FrameWorkApi" + Constants.PAYMENT_SYNC.URL.FILE_DOWNLOAD_BY_CLOUD_ID;
        //logger.info("=========读取OSS服务器文件内容. URL:{},参数：{}", interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.FILE_DOWNLOAD_BY_CLOUD_ID, JSON.toJSON(map));
//        logger.info("=========读取OSS服务器文件内容. URL:{},参数：{}", "http://cnapp-uat.sgs.net/FrameWorkApi" + Constants.PAYMENT_SYNC.URL.FILE_DOWNLOAD_BY_CLOUD_ID, JSON.toJSON(map));
        //保存日志
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType("读取OSS服务器文件");
        systemLog.setObjectNo(map.get("cloudID").toString());
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(SystemLogType.API.getType());
        systemLog.setRemark("读取OSS服务器文件");
        systemLog.setRequest(JSON.toJSON(map).toString());
        try {
            fileUrl = HttpClientUtil.post(url, map);
        } catch (Exception e) {
            e.printStackTrace();
            systemLog.setResponse(e.getMessage());
            logger.info("=========读取OSS服务器文件内容. URL:{},错误信息：{}", interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.FILE_DOWNLOAD_BY_CLOUD_ID, e.getMessage());
//            logger.info("=========读取OSS服务器文件内容. URL:{},错误信息：{}", "http://cnapp-uat.sgs.net/FrameWorkApi" + Constants.PAYMENT_SYNC.URL.FILE_DOWNLOAD_BY_CLOUD_ID, e.getMessage());
        }
        if (Func.isEmpty(systemLog.getResponse())){
            systemLog.setResponse(fileUrl);
        }
        systemLogHelper.save(systemLog);
        //判断获取的url是否为空
        if (Func.isEmpty(fileUrl)) {
            return null;
        }
        //保存日志
        SystemLog getFileLog = new SystemLog();
        getFileLog.setObjectType("访问OSS服务器文件地址");
        getFileLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        getFileLog.setType(SystemLogType.API.getType());
        getFileLog.setRemark("fileUrl:"+fileUrl);
        getFileLog.setRequest(fileUrl);
        try {
            fileText = HttpClientUtil.get(fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
            getFileLog.setResponse(e.getMessage());
            logger.info("=========访问OSS服务器文件地址. URL:{},错误信息：{}", fileUrl, e.getMessage());
        }
        if (Func.isEmpty(getFileLog.getResponse())){
            getFileLog.setResponse(fileText);
        }
        systemLogHelper.save(getFileLog);
        return fileText;
    }

    /**
     * 文件处理成功之后回传状态
     *
     * @param map
     */
    public void fileStatusUpdate(Map<String, Object> map) {
        //保存日志
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType("调用backoffice处理文件完成接口");
        systemLog.setObjectNo(null);
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(SystemLogType.API.getType());
        systemLog.setRemark("调用backoffice处理文件完成接口");
        systemLog.setRequest(JSON.toJSON(map).toString());
        String url = interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.UPDATE_BACK_OFFICE_RESULT_STATE;
        String result = null;
        //logger.info("=========调用backoffice处理文件完成接口. URL:{},参数：{}", interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.UPDATE_BACK_OFFICE_RESULT_STATE, JSON.toJSON(map));
        try {
            result = HttpClientUtil.post(url, map);
        } catch (Exception e) {
            e.printStackTrace();
            systemLog.setResponse(e.getMessage());
            logger.info("=========调用backoffice处理文件完成接口. URL:{},错误信息：{}", interfaceConfig.getBaseUrl() + Constants.PAYMENT_SYNC.URL.UPDATE_BACK_OFFICE_RESULT_STATE, e.getMessage());
        }
        if (Func.isEmpty(systemLog.getResponse())){
            systemLog.setResponse(result);
        }
        systemLogHelper.save(systemLog);
    }

    public boolean cancelPrePayment(CancelPrePaymentReq req){
        try {
            String backOfficeApiUrl = String.format("%s/backoffice-api/receiptMatching/cancelOrder", interfaceConfig.getBaseUrl());
            String response = HttpClientUtil.postJson(backOfficeApiUrl, JSON.toJSONString(req));
            BaseResponse res = JSONObject.parseObject(response, BaseResponse.class);
            logger.info("BackOfficeApi.receiptMatching rep : {}",response);
            if(res.isSuccess()){
                return true;
            }
            return false;
        } catch (Exception ex) {
            logger.error("CustomerClient.addCustomerInfo error : {}", ex.getMessage(), ex);
        }
        return false;
    }
}