package com.sgs.preorder.integration.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QuotationTestLineInstanceDto {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.order_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.pp_version_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer ppVersionId;

    private String ppName;

    private Integer ppNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.test_line_version_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    @JsonProperty("testlineVersionId")
    private Integer testLineVersionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.test_line_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    @JsonProperty("testlineId")
    private Integer testLineId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.test_line_evaluation
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String testLineEvaluation;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.test_line_status
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String testLineStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.test_line_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String testLineName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.citation_type_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    @JsonProperty("citationType")
    private Integer citationTypeId;



    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.product_line
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String productLine;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.regulation_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String regulationName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.currency
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String currency;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.price_attribute
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String priceAttribute;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.unit_price
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private BigDecimal unitPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.evaluation_alias
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String evaluationAlias;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.standard_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    @JsonProperty("citationId")
    private Integer standardId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.standard_version_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    @JsonProperty("citationVersionId")
    private Integer standardVersionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.standard_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    @JsonProperty("citationName")
    private String standardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.section_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer sectionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.section_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String sectionName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.tat
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String tat;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.sample_size
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String sampleSize;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.remark
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.active_indicator
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Boolean activeIndicator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.created_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Date createdDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.created_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String createdBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.modified_date
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Date modifiedDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.modified_by
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String modifiedBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String serviceItemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.method_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String methodName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.regulation_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer regulationId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.regulation_version_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer regulationVersionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.test_line_version
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer testLineVersion;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.parent_pp_version
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private Integer parentPpVersion;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.quotation_service_item_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String quotationServiceItemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.bu_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String buCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_test_line_instance.lab_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String labCode;

    private String locationCode;


    private List<QuotationAnalyteInstanceDto> quotationAnalyteInstanceDtoList;
}