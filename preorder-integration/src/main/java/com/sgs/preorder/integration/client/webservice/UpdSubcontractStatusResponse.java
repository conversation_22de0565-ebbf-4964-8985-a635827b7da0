
package com.sgs.preorder.integration.client.webservice;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex typeJava
 * 
 * <p>
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="UpdSubcontractStatusResult" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "updSubcontractStatusResult"
})
@XmlRootElement(name = "UpdSubcontractStatusResponse")
public class UpdSubcontractStatusResponse {

    @XmlElement(name = "UpdSubcontractStatusResult")
    protected int updSubcontractStatusResult;

    /**
     * updSubcontractStatusResult
     * 
     */
    public int getUpdSubcontractStatusResult() {
        return updSubcontractStatusResult;
    }

    /**
     * updSubcontractStatusResult
     * 
     */
    public void setUpdSubcontractStatusResult(int value) {
        this.updSubcontractStatusResult = value;
    }

}
