package com.sgs.preorder.integration.client.gpn;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.facade.TestLineInstanceFacade;
import com.sgs.otsnotes.facade.model.req.testLine.OrderTestLineReq;
import com.sgs.otsnotes.facade.model.rsp.testLine.OrderTestLineRsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class GpnTestLineInstanceClient {
    private static final Logger logger = LoggerFactory.getLogger(GpnTestLineInstanceClient.class);
    @Autowired
    private TestLineInstanceFacade gpnTestLineInstanceFacade;

    /**
     *
     * @param jobNos
     * @return
     */
    public List<OrderTestLineRsp> getTestLineListByOrderId(OrderTestLineReq var1){
        try {
            BaseResponse<List<OrderTestLineRsp>> rspResult = gpnTestLineInstanceFacade.getTestLineListByOrderId(var1);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("TestLineInstanceClient.getTestLineListByOrderId({})信息异常：{}.", var1, ex.getMessage(), ex);
        }
        return null;
    }

}
