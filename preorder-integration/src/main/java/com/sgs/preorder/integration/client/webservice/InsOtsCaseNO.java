
package com.sgs.preorder.integration.client.webservice;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex typeJava
 * 
 * <p>
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CaseNoStr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "caseNoStr"
})
@XmlRootElement(name = "InsOtsCaseNO")
public class InsOtsCaseNO {

    @XmlElement(name = "CaseNoStr")
    protected String caseNoStr;

    /**
     * ȡcaseNoStr
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCaseNoStr() {
        return caseNoStr;
    }

    /**
     * caseNoStr
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCaseNoStr(String value) {
        this.caseNoStr = value;
    }

}
