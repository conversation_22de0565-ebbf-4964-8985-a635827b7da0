
package com.sgs.preorder.integration.client.webservice;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex typeJavaࡣ
 * 
 * <p>
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="UpdJobLabOutResult" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "updJobLabOutResult"
})
@XmlRootElement(name = "UpdJobLabOutResponse")
public class UpdJobLabOutResponse {

    @XmlElement(name = "UpdJobLabOutResult")
    protected int updJobLabOutResult;

    /**
     * ȡupdJobLabOutResult
     * 
     */
    public int getUpdJobLabOutResult() {
        return updJobLabOutResult;
    }

    /**
     * updJobLabOutResult
     * 
     */
    public void setUpdJobLabOutResult(int value) {
        this.updJobLabOutResult = value;
    }

}
