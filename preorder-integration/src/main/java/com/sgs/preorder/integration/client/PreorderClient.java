package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.req.preorder.OrderPaidReq;
import com.sgs.preorder.facade.model.req.preorder.PreOrderAmountReq;
import com.sgs.preorder.facade.model.rsp.preorder.PreOrderAmountRsp;
import com.sgs.preorder.integration.client.dto.OrderListQueryForSLReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @title: PreorderClient
 * @projectName preorder-service
 * @description: TODO
 * @date 2023/2/159:13
 */
@Component
@Slf4j
public class PreorderClient {
    @Autowired
    private InterfaceConfig interfaceConfig;
    public BaseResponse processOrderPaids(List<OrderPaidReq> orderPaidReqList){
        HashMap<String, Object> reqParams = new HashMap<>();
        reqParams.put("data",orderPaidReqList);
        log.info("call processOrderPaids Req:{}", JSON.toJSONString(reqParams));
        String frameWorkApi = String.format("%s/preorder2api/order/processOrderPaids", interfaceConfig.getBaseUrl());
        BaseResponse baseResponse = new BaseResponse();
        try {
            baseResponse = HttpClientUtil.sendPost(frameWorkApi, reqParams, BaseResponse.class);
        } catch (Exception e) {
            log.error("call preorder processOrderPaids error {}",e);
           return BaseResponse.newFailInstance(e.getMessage());
        }
        log.info("call processOrderPaids Res:{}", JSON.toJSONString(baseResponse));
        return baseResponse;
    }

    public BaseResponse<List<PreOrderAmountRsp>> queryOrderAmount(PreOrderAmountReq preOrderAmountReq){
        if(Func.isEmpty(preOrderAmountReq) || Func.isEmpty(preOrderAmountReq.getOrderNos())){
            return BaseResponse.newFailInstance("call queryOrderAmount orderNos param is null");
        }
        BaseResponse<List<PreOrderAmountRsp>> baseResponse = null;
        try {
            log.info("call preorder2api queryOrderAmount req:{}",Func.toJson(preOrderAmountReq));
            String queryOrderAmountUrl = String.format("%s/preorder2api/order/queryOrderAmount", interfaceConfig.getBaseUrl());
            String response = HttpClientUtil.postJson(queryOrderAmountUrl, preOrderAmountReq);
            log.info("call preorder2api queryOrderAmount res,json:{}",response);
            baseResponse=JsonUtil.parse(response, new com.fasterxml.jackson.core.type.TypeReference<BaseResponse<List<PreOrderAmountRsp>>>(){});
            //log.info("call preorder2api queryOrderAmount res,object:{}",baseResponse);
        } catch (Exception e) {
            log.error("get preorder amount error:" + e.getMessage(), e);
            return BaseResponse.newFailInstance("call preorder amount error:"+e.getMessage());
        }
        return baseResponse;
    }

    public BaseResponse getOrderListForSL(OrderListQueryForSLReq req){
        log.info("getOrderListForSL Req:{}", JSON.toJSONString(req));
        String frameWorkApi = String.format("%s/preorder2api/order/getOrderList", interfaceConfig.getBaseUrl());
        BaseResponse baseResponse = new BaseResponse();
        try {
            baseResponse = HttpClientUtil.doPost(frameWorkApi, req, BaseResponse.class);
        } catch (Exception e) {
            log.error("getOrderListForSL error {}",e);
            return BaseResponse.newFailInstance(e.getMessage());
        }
        log.info("getOrderListForSL Res:{}", JSON.toJSONString(baseResponse));
        return baseResponse;
    }
}
