package com.sgs.preorder.integration.client;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.util.SpringUtil;
import com.sgs.framework.security.context.SecurityContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Component
public class TokenClient {
    /**
     * SGS Token Key
     */
    public final String SGS_TOKEN = "sgsToken";

    /**
     *
     * @return
     */
    public HttpServletRequest getRequestContext() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
        //jira-11232
//        if (sra == null) {
//            return null;
//        }
        return sra.getRequest();
    }

    /**
     *
     * @param request
     * @return
     */
    public String getToken(HttpServletRequest request) {
        return SecurityContextHolder.getSgsToken();
    }

    /**
     * Get SGS UserInfo
     * @param token - token generated by user management
     * @return UserInfo
     */
    public UserInfo getUser(String token) {
        @SuppressWarnings("unchecked")
        RedisTemplate<String, Object> redisTemplate = (RedisTemplate<String, Object>)
                SpringUtil.getBean("redisTemplate");
        Object val = redisTemplate.opsForValue().get(token);
        return (UserInfo)val;
    }

    /**
     *
     * @return
     */
    public String getToken(){
        return getToken(this.getRequestContext());
    }

    /**
     * Get SGS UserInfo
     * @param request
     * @return
     */
    public UserInfo getUser(HttpServletRequest request) {
        String sgsToken = getToken(request);
        if (StringUtils.isNotBlank(sgsToken)) {
            return getUser(sgsToken);
        }
        return null;
    }

    public String getUserLabCode(String token){
        UserInfo user = getUser(token);
        return user == null ? "" : user.getCurrentLabCode();
    }

    public String getUserProductLineCodeByLab(String labCode) {
        String[] splitLabs = labCode.split(" ");
        if (splitLabs != null && splitLabs.length > 1){
            //bizLog.setLab(splitLabs[0].trim());
            return splitLabs[1].trim();
        }

        return org.apache.commons.lang3.StringUtils.EMPTY;
    }

    /**
     *
     * @return
     */
    public UserInfo getUser() {
        String sgsToken = getToken(this.getRequestContext());
        if (StringUtils.isNotBlank(sgsToken)) {
            return getUser(sgsToken);
        }
        return SecurityContextHolder.getUserInfo();
    }
}
