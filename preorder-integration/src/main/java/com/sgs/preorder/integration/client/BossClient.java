package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.core.domain.ExecuteResult;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.info.BossResponseData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Component
public class BossClient {
	private static final Logger logger = LoggerFactory.getLogger(BossClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;
    public List<BossResponseData> getBossResponseData(HashMap<String, Object> reqParams){
    	try {
            String frameWorkApi = String.format("%s/FrameWorkApi/toBoss/api/v1/getBossResponseData", interfaceConfig.getBaseUrl());
            ExecuteResult<JSONObject> executeResult = HttpClientUtil.sendPost(frameWorkApi, reqParams, ExecuteResult.class);
            if (executeResult == null || !executeResult.getIsSuccess()){
                return null;
            }
//        	JSONObject result=executeResult.getResult();
        	List<BossResponseData> bossResponseDatas= JSONObject.parseArray(JSON.toJSONString(executeResult.getResult()), BossResponseData.class);
            return bossResponseDatas;
        }catch (Exception ex){
            logger.error("FrameWorkClient.getBossResponseData 信息异常：{}.", ex);
        }
        return null;
    }

	
}
