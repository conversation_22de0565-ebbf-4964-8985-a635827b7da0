
package com.sgs.preorder.integration.client.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "EMWebServiceSoap", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface EMWebServiceSoap {


    /**
     *
     * @param jobNo
     * @param labOutDate
     * @param caseNo
     * @return
     *     returns int
     */
    @WebMethod(operationName = "UpdJobLabOut", action = "http://tempuri.org/UpdJobLabOut")
    @WebResult(name = "UpdJobLabOutResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "UpdJobLabOut", targetNamespace = "http://tempuri.org/", className = "com.sgs.preorder.integration.client.webservice.UpdJobLabOut")
    @ResponseWrapper(localName = "UpdJobLabOutResponse", targetNamespace = "http://tempuri.org/", className = "com.sgs.preorder.integration.client.webservice.UpdJobLabOutResponse")
    public int updJobLabOut(
        @WebParam(name = "CaseNo", targetNamespace = "http://tempuri.org/")
        String caseNo,
        @WebParam(name = "JobNo", targetNamespace = "http://tempuri.org/")
        String jobNo,
        @WebParam(name = "LabOutDate", targetNamespace = "http://tempuri.org/")
        XMLGregorianCalendar labOutDate);

    /**
     *
     * @param caseNo
     * @param subcontractNo
     * @return
     *     returns int
     */
    @WebMethod(operationName = "UpdSubcontractStatus", action = "http://tempuri.org/UpdSubcontractStatus")
    @WebResult(name = "UpdSubcontractStatusResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "UpdSubcontractStatus", targetNamespace = "http://tempuri.org/", className = "com.sgs.preorder.integration.client.webservice.UpdSubcontractStatus")
    @ResponseWrapper(localName = "UpdSubcontractStatusResponse", targetNamespace = "http://tempuri.org/", className = "com.sgs.preorder.integration.client.webservice.UpdSubcontractStatusResponse")
    public int updSubcontractStatus(
        @WebParam(name = "CaseNo", targetNamespace = "http://tempuri.org/")
        String caseNo,
        @WebParam(name = "SubcontractNo", targetNamespace = "http://tempuri.org/")
        String subcontractNo);

    /**
     *
     * @param caseNoStr
     * @return
     *     returns int
     */
    @WebMethod(operationName = "InsOtsCaseNO", action = "http://tempuri.org/InsOtsCaseNO")
    @WebResult(name = "InsOtsCaseNOResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "InsOtsCaseNO", targetNamespace = "http://tempuri.org/", className = "com.sgs.preorder.integration.client.webservice.InsOtsCaseNO")
    @ResponseWrapper(localName = "InsOtsCaseNOResponse", targetNamespace = "http://tempuri.org/", className = "com.sgs.preorder.integration.client.webservice.InsOtsCaseNOResponse")
    public int insOtsCaseNO(
        @WebParam(name = "CaseNoStr", targetNamespace = "http://tempuri.org/")
        String caseNoStr);

}
