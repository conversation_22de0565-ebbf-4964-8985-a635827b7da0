package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.api.R;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.common.ResponseCode;
import com.sgs.preorder.facade.model.rsp.sgsmart.SampleRsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
@AllArgsConstructor
public class SGSMartClient {

    private InterfaceConfig interfaceConfig;

    /**
     * 基于订单号获取样品信息含子样、Mix样等；
     * @param orderNos
     * @return
     */
    public List<SampleRsp> getSampleListByOrderNos(List<String> orderNos){
        if(Func.isEmpty(orderNos)){
            return null;
        }
        try {
            String getSampleURL = String.format("%s/sgs-sample/sample/selectSampleByOrderNoList?productLineCode=%s&sgsToken=%s", interfaceConfig.getNewSgsMartApi(),ProductLineContextHolder.getProductLineCode(),SecurityUtil.getSgsToken());
            log.info("SGSMartClient.getSampleListByOrderNos 请求参数：{}", JSON.toJSON(orderNos));
            R<List<SampleRsp>> samplesRsp = HttpClientUtil.sendPost(getSampleURL, orderNos, new TypeReference<R<List<SampleRsp>>>(){});
            log.info("SGSMartClient.getSampleListByOrderNos 返回参数：{}", JSON.toJSON(samplesRsp));
            if(ResponseCode.SUCCESS.getCode()==samplesRsp.getCode()){
                return samplesRsp.getData();
            }else{
                return null;
            }
        }catch (Exception ex){
            log.error("SGSMartClient.getSampleListByOrderNos 信息异常：{}.", ex);
        }
        return null;
    }

}
