package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.facade.model.common.BaseResponse;
import com.sgs.preorder.core.common.PreEvent;
import com.sgs.preorder.facade.model.common.KafkaMessage;
import com.sgs.preorder.facade.model.enums.KafkaActionType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title: ScheduleTaskClient
 * @projectName preorder-service
 * @description: TODO
 * @date 2024/1/21 17:59
 */
@Component
@Slf4j
public class ScheduleTaskClient {
    /*@Autowired
    private ScheduleTaskFacade scheduleTaskFacade;*/
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public BaseResponse<Boolean> sendEventMessage(PreEvent preEvent, String labCode){
        if(Func.isEmpty(preEvent.getProductLineCode())){
            preEvent.setProductLineCode(SecurityUtil.getProductLine());
        }
        KafkaMessage<PreEvent> message = new KafkaMessage();
        message.setAction(KafkaActionType.event.getCode());
        message.setSgsToken(preEvent.getToken());
        message.setProductLineCode(preEvent.getProductLineCode());
        if(Func.isEmpty(labCode)){
            labCode = SecurityUtil.getLabCode();
        }
        message.setLabCode(labCode);
        message.setData(preEvent);
        log.info("sendEventMessage body:{}",JSON.toJSONString(message));
        kafkaTemplate.send(com.sgs.framework.core.constant.KafkaTopicConsts.TOPIC_EVENT,preEvent.getOrderNo(),JSON.toJSONString(message));
        return BaseResponse.newSuccessInstance(true);
    }
}
