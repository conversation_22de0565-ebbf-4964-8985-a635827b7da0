package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.info.toboss.BossOrderInvoceHeadVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @title: HLPreOrderClient
 * @projectName preorder-service
 * @description: TODO
 * @date 2021/12/1014:28
 */
@Component
@Slf4j
public class HLPreOrderClient {
    @Resource
    private InterfaceConfig interfaceConfig;

    public boolean existByOrderNo(String bossOrderNo){
        try {
            String existByOrderNoUrl = String.format("%s/GeneralPreOrderApi/open/existByOrderNo?bossOrderNo=%s", interfaceConfig.getBaseUrl(),bossOrderNo);
            log.error("call HL preorder existByOrderNo param:{}", existByOrderNoUrl);
            String result = HttpClientUtil.get(existByOrderNoUrl);
            log.error("call HL preorder existByOrderNo result:{}", result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            if(Func.isNotEmpty(jsonObject) && Boolean.parseBoolean(jsonObject.get("result").toString())){
                return true;
            }else{
                return false;
            }
        } catch (Exception e) {
            log.error("call HL preorder existByOrderNo error:{}",e);
            return false;
        }
    }

    public List<BossOrderInvoceHeadVO> getBossInfoByOrderNoList(String legalEntityCode,String buCode,List<String> bossOrderNo){
        try {
//            log.error("call HL preorder getBossInfoByOrderNoList param:{}", JSON.toJSONString(getBossInfoReq));
            String url = String.format("%s/GeneralPreOrderApi/open/getBossInfoByOrderNoList?legalEntityCode=%s&buCode=%s", interfaceConfig.getBaseUrl(),legalEntityCode,buCode);
            String result =HttpClientUtil.postJson(url, bossOrderNo);
            log.error("call HL preorder getBossInfoByOrderNoList result:{}", result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            if(jsonObject.containsKey("data")){
                List<BossOrderInvoceHeadVO> bossOrderInvoceHeadVOS = JSONObject.parseArray(jsonObject.getString("data"), BossOrderInvoceHeadVO.class);
                return bossOrderInvoceHeadVOS;
            }else{
                return null;
            }

        } catch (Exception e) {
            log.error("call HL preorder getBossInfoByOrderNoList error:{}",e);
            return null;
        }
    }



}
