package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.otsnotes.facade.OrderFacade;
import com.sgs.otsnotes.facade.ReportFacade;
import com.sgs.otsnotes.facade.model.dto.OrderForEMInfo;
import com.sgs.otsnotes.facade.model.req.CrossLabReq;
import com.sgs.otsnotes.facade.model.req.OrderInfoForEMReq;
import com.sgs.otsnotes.facade.model.req.ReportIdRequest;
import com.sgs.otsnotes.facade.model.req.order.*;
import com.sgs.otsnotes.facade.model.req.testLine.SaveTestLineReq;
import com.sgs.preorder.core.config.BizConfig;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.core.util.StringUtil;
import com.sgs.preorder.facade.model.dto.order.DataDictionary;
import com.sgs.preorder.facade.model.dto.order.FrameworkDataDictionaryQueryRequest;
import com.sgs.preorder.facade.model.dto.order.OrderDetailDto;
import com.sgs.preorder.facade.model.info.CustomerInfo;
import com.sgs.preorder.facade.model.info.OrderHeaderInfo;
import com.sgs.preorder.facade.model.info.ReportInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.req.GenerateOrderNoReq;
import com.sgs.preorder.integration.client.setting.buparam.IBUParamService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class OrderClient {
    private static final Logger logger = LoggerFactory.getLogger(OrderClient.class);

    @Autowired
    private OrderFacade notesOrderFacade;
    @Autowired
    private BizConfig bizConfig;
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private IBUParamService ibuParamService;
    @Autowired
    private ReportFacade notesReportFacade;


    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult createOrderInfo(SyncOrderInfo reqObject){
        CustomResult rspResult = new CustomResult();
        reqObject.setRequestId(Constants.getRequestId());
        try {
            BaseResponse baseResponse = notesOrderFacade.createOrderInfo(reqObject);
            rspResult.setSuccess(baseResponse.getStatus() == 200);
            rspResult.setData(baseResponse.getData());
            rspResult.setStackTrace(baseResponse.getStackTrace());
            rspResult.setMsg(baseResponse.getMessage());
        }catch (Exception ex){
            logger.error("OrderClient.createOrderInfo 创建订单({})信息异常：{}.", reqObject.getOrderNo(), ex.getMessage(), ex);
        }
        return rspResult;
    }

    /**
     *
     * @param orderNo
     * @return
     */
    public OrderForEMInfo getOrderInfoForEM(String orderNo){
        OrderInfoForEMReq orderReq = new OrderInfoForEMReq();
        orderReq.setRequestId(Constants.getRequestId());
        orderReq.setOrderNo(orderNo);
        try {
            BaseResponse<OrderForEMInfo> rspResult = notesOrderFacade.getOrderInfoForEM(orderReq);
            return rspResult.getData();
        }catch (Exception ex){
            logger.error("OrderClient.getOrderInfoForEM 获取订单({})信息异常：{}.", orderNo, ex.getMessage(), ex);
        }
        return null;
    }

    /**
     *
     * @param crossLab
     * @return
     */
    public CustomResult saveCrossLabInfo(CrossLabReq crossLab){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CustomResult result = new CustomResult();
        crossLab.setRequestId(Constants.getRequestId());
        try {
            BaseResponse rspResult = notesOrderFacade.saveCrossLabInfo(crossLab);
            result.setSuccess(rspResult.getStatus() == 200);
            result.setData(rspResult.getData());
            result.setMsg(rspResult.getMessage());
            logger.info("cross_order_{}，ExecType({})，sync notes system，rspResult：{}.", crossLab.getOrderNo(), crossLab.getExecType(), rspResult);
            return result;
        }catch (Exception ex){
            result.setMsg(ex.getMessage());
            logger.error("OrderClient.saveCrossLabInfo 获取订单(cross_order_{})信息异常：{}.", crossLab.getOrderNo(), ex.getMessage(), ex);
        }finally {
            stopWatch.stop();
            logger.info("OrderClient.saveCrossLabInfo 订单(cross_order_{})请求接口耗时：{}.", crossLab.getOrderNo(), stopWatch.getTime());
        }
        return result;
    }

    /**
     *
     * @param userLabBu
     * @param postfix
     * @return
     */
    public CustomResult generateOrderNo(UserLabBuInfo userLabBu, String postfix){
        String orderNoMode = ibuParamService.getOrderNoMode(userLabBu.getProductLineCode(),userLabBu.getLabCode()).getData();
        if(Constants.BU_PARAM.ObjectNo.OrderNo.VALUE.LYMSPC.equals(orderNoMode)) {
            CustomResult rspResult = new CustomResult();
            String orderNo= null;
            try {
                orderNo = frameWorkClient.generateOrderNo(String.valueOf(userLabBu.getProductLineId()),String.valueOf(userLabBu.getLocationId()),postfix,userLabBu.getLocationCode(),userLabBu.getShortCode());
                if(Func.isBlank(orderNo)){
                    rspResult.setMsg("locationMaps 对象为空.");
                    rspResult.setSuccess(false);
                    return rspResult;
                }
                rspResult.setSuccess(true);
                rspResult.setData(orderNo);
                return  rspResult;
            } catch (Exception e) {
                rspResult.setMsg("获取OrderNo失败:"+e.getMessage());
                rspResult.setSuccess(false);
                return rspResult;
            }
        } else {
            //目前只有SODA使用，SystemId先设置为SODA
            GenerateOrderNoReq orderNoReq = new GenerateOrderNoReq();
            orderNoReq.setBuId(userLabBu.getProductLineId());
            orderNoReq.setLocationId(userLabBu.getLocationId());
            orderNoReq.setPostfix(postfix);
            orderNoReq.setSystemId(SgsSystem.SODA.getSgsSystemId());
            return this.generateOrderNo(orderNoReq);
        }
    }

    /**
     *
     * @param reqParams
     * @return
     */
    public CustomResult generateOrderNo(GenerateOrderNoReq reqParams){
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(reqParams.getPostfix())){
            reqParams.setPostfix("TX");
        }
        Map<String, Object> locationMaps = (Map<String, Object>) JSON.parse(bizConfig.getLocationMap());
        if (locationMaps == null || locationMaps.isEmpty()){
            rspResult.setMsg("locationMaps 对象为空.");
            return rspResult;
        }
        String locationKey = String.format(Constants.LOCATION_KEY, reqParams.getLocationId());
        if (!locationMaps.containsKey(locationKey)){
            rspResult.setMsg("未找到对应的 locationMaps Key.");
            return rspResult;
        }
        Map<String, String> paramMaps = Maps.newHashMap();
        paramMaps.put("systemID", StringUtil.toString(reqParams.getSystemId()));
        paramMaps.put("bUID", StringUtil.toString(reqParams.getBuId()));
        paramMaps.put("locationID", StringUtil.toString(reqParams.getLocationId()));
        Object referenceCode = locationMaps.get(locationKey);
        paramMaps.put("referenceCode", referenceCode != null ? referenceCode.toString() : "");
        paramMaps.put("postfix", reqParams.getPostfix());

        String reqUrl = String.format("%s%s",interfaceConfig.getBaseUrl(), com.sgs.preorder.core.util.Constants.FRAMEWORK.URL.GET_ORDER_NO);
        try {
            String orderNo = HttpClientUtil.sendGet(reqUrl, paramMaps);
            rspResult.setData(orderNo);
            rspResult.setSuccess(StringUtils.isNotBlank(orderNo));
            if (!rspResult.isSuccess()){
                rspResult.setMsg("FrameWorkApi接口返回OrderNo为空.");
            }
        }catch (Exception ex){
            rspResult.setMsg(String.format("generateOrderNo 接口异常：%s", ex.getMessage()));
            logger.error("OrderClient.generateOrderNo 接口异常：{}", ex);
        }
        return rspResult;
    }
    /**
     *
     * @param request
     * @return
     */
    public CustomResult queryDataDictionaryList(FrameworkDataDictionaryQueryRequest request){
        CustomResult rspResult = new CustomResult();

        Map<String, Object> locationMaps = (Map<String, Object>) JSON.parse(bizConfig.getLocationMap());

        Map<String, String> params = Maps.newHashMap();
        if (request.getSystemId() != null) {
            params.put("systemID", request.getSystemId().toString());
        }

        if (request.getSysKeyGroup() != null) {
            params.put("sysKeyGroup", request.getSysKeyGroup().toString());
        }

        if (request.getBuId() != null) {
            params.put("bUID", request.getBuId().toString());
        }

        if (request.getLocationId() != null) {
            params.put("locationID", request.getLocationId().toString());
        }

        String reqUrl = String.format("%s%s",interfaceConfig.getBaseUrl(), "/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary");
        try {
            String json = HttpClientUtil.sendGet(reqUrl, params);
            List<DataDictionary> list1 = (List<DataDictionary>) JSONArray.parseArray(json,DataDictionary.class);
            rspResult.setData(list1);
            rspResult.setSuccess(StringUtils.isNotBlank(json));
        }catch (Exception ex){
            rspResult.setMsg(String.format("generateOrderNo 接口异常：%s", ex.getMessage()));
            logger.error("OrderClient.generateOrderNo 接口异常：{}", ex);
        }
        return rspResult;
    }

    /**
     *
     * @param order
     * @return
     */
    public Boolean createOrder(OrderDetailDto order, List<SaveTestLineReq> testLineReqs){
        CreateOrderReq createOrderReq = new CreateOrderReq();
        createOrderReq.setProductLineCode(order.getProductLineCode());
        if(Func.isEmpty(createOrderReq.getProductLineCode())){
            createOrderReq.setProductLineCode(order.getHeaders().getBuCode());
        }
        // 设置客户信息
        CustomerReq customerReq = new CustomerReq();
        CustomerInfo buyer = order.getBuyer();
        CustomerInfo applicant = order.getApplicant();
        if(Func.isNotEmpty(buyer)){
            customerReq.setClientName(buyer.getCustomerNameEN());
            customerReq.setClientCode(Func.toStr(buyer.getBossNumber()));
            customerReq.setGroupName(buyer.getBuyerGroupName());
            customerReq.setGroupCode(buyer.getBuyerGroup());
            createOrderReq.setClient(customerReq);
        }

        if(Func.isNotEmpty(applicant)){
            createOrderReq.setApplicantCustomerGroupName(StringUtils.isNotBlank(applicant.getBuyerGroupName()) ? applicant.getBuyerGroupName() : null);
            createOrderReq.setApplicantCustomerNameCN(StringUtils.isNotBlank(applicant.getCustomerNameCN()) ? applicant.getCustomerNameCN() : null);
            createOrderReq.setApplicantCustomerNameEn(StringUtils.isNotBlank(applicant.getCustomerNameEN()) ? applicant.getCustomerNameEN() : null);
            createOrderReq.setApplicantCustomerGroupCode(StringUtils.isNotBlank(applicant.getBuyerGroup()) ? applicant.getBuyerGroup() : null);
        }

        // 设置TS信息
        if (Func.isNotEmpty(order.getTsPerson())) {
            createOrderReq.setTechnicalSupporter(order.getTsPerson().getRegionAccount());
        }

        //设置样品信息
        List<SampleDTO> sampleArry = new ArrayList<>();
        if(Func.isNotEmpty(order.getProductSamples())){
            order.getProductSamples().forEach(productSampleInfo -> {
                SampleDTO sampleDTO = new SampleDTO();
                sampleDTO.setId(productSampleInfo.getRefSampleID());
                sampleDTO.setSampleNo(productSampleInfo.getSampleID());
                sampleDTO.setDescription(productSampleInfo.getOtherSampleInformation());
                if(Func.equalsSafe(order.getHeaders().getBuCode(), ProductLineType.MR.getProductLineAbbr())){
                    sampleDTO.setDescription(productSampleInfo.getSampleDescription());
                }
                sampleDTO.setColor(productSampleInfo.getProductColor());
                sampleDTO.setEndUse(productSampleInfo.getEndUse1());
                sampleDTO.setComposition(productSampleInfo.getFiberComposition());
                sampleDTO.setNoOfSample(productSampleInfo.getNoOfSample());
                sampleDTO.setSort(productSampleInfo.getSort());
                sampleArry.add(sampleDTO);
            });
        }
        createOrderReq.setSampleList(sampleArry);

        OrderHeaderInfo orderHeaderInfo = order.getHeaders();

        if(Func.isNotEmpty(orderHeaderInfo)){
            createOrderReq.setAction(orderHeaderInfo.getToTestFlag()==1?"UPDATE":"NEW");
            createOrderReq.setOrderId(orderHeaderInfo.getOrderId());
            createOrderReq.setOrderNo(orderHeaderInfo.getOrderNo());
            createOrderReq.setLocationId(orderHeaderInfo.getLocationId());
            createOrderReq.setProductLineId(orderHeaderInfo.getBuId());
            createOrderReq.setReportDueDate(orderHeaderInfo.getReportExpectDueDate());
            //实验室信息
            LabReq labReq = new LabReq();
            labReq.setLabId(orderHeaderInfo.getLabId().intValue());
            labReq.setLabName(orderHeaderInfo.getLabName());
            labReq.setLabCode(orderHeaderInfo.getLabCode());
            createOrderReq.setLab(labReq);
        }

        createOrderReq.setResponsibleTeamCode(StringUtils.isNotBlank(orderHeaderInfo.getResponsibleTeamCode())?orderHeaderInfo.getResponsibleTeamCode():null);
        createOrderReq.setCsName(Func.toStr(orderHeaderInfo.getcSName()));
        createOrderReq.setTestLines(testLineReqs);
        ReportInfo report = order.getReport();
        if(Func.isNotEmpty(report)){
            createOrderReq.setReportNo(report.getReportNo());
            createOrderReq.setReportId(report.getId());
        }
        createOrderReq.setSampleNOChange(1);
        createOrderReq.setSgsToken(Func.isEmpty(SecurityUtil.getSgsToken())?order.getToken():SecurityUtil.getSgsToken());
        createOrderReq.setSuffixNum(order.getHeaders().getSuffixNum());
        createOrderReq.setQrcodeFlag(order.getQrcodeFlag());
        logger.info("toTest----createOrder---TestLines,{}",JSON.toJSONString(createOrderReq.getTestLines()));
        logger.info("toTest-createOrder-req:{}",JSON.toJSONString(createOrderReq));
        return notesOrderFacade.createOrder(createOrderReq).getData();
    }

//    /**
//     *
//     * @param orderReq
//     * @return
//     */
//    public BaseResponse copyOrderInfo(CopyOrderReq orderReq){
//        try {
//            return notesOrderFacade.copyOrderInfo(orderReq);
//        }catch (Exception ex){
//            logger.error("OrderClient.copyOrderInfo copy订单({})信息异常：{}.", orderReq.getOldOrderNo(), ex.getMessage(), ex);
//        }
//        return null;
//    }

    /**
     *
     * @param orderReq
     * @return
     */
    public BaseResponse renew(ReportIdRequest orderReq){
        orderReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return notesReportFacade.renew(orderReq);
    }

}
