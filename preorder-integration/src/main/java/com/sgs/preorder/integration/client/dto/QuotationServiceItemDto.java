package com.sgs.preorder.integration.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QuotationServiceItemDto {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.id
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.order_id
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.charge_type
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Byte chargeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.charge_name
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    @JsonProperty("serviceItemName")
    private String chargeName;

    @JsonProperty("serviceItemNameCn")
    private String chargeNameCn;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.other_fee_type
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String otherFeeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.service_item_id
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String serviceItemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.service_item_type
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Byte serviceItemType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.sample_desc
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String sampleDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.org_unit_price
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal orgUnitPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.unit_price
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal unitPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.quantity
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Integer quantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.org_currency_code
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String orgCurrencyCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.currency_code
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String currencyCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.exchange_rate
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal exchangeRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.discount
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal discount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.sur_charge
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal surCharge;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.net_amount
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal netAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.vat
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal vat;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.tax_amount
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal taxAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.after_tax_amount
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal afterTaxAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.created_date
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Date createdDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.created_by
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String createdBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.modified_date
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Date modifiedDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.modified_by
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String modifiedBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.sales_unit_price
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal salesUnitPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.sales_discount
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal salesDiscount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.test_methods
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String testMethods;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.analyte_qty
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Short analyteQty;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.tat
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Short tat;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.suggest_sample_size
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String suggestSampleSize;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.unit
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String unit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.suggest_sample_size2
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Short suggestSampleSize2;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.unit2
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private BigDecimal unit2;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.remark
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.special_requirement
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String specialRequirement;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.bu_code
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String buCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.lab_code
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String labCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.location_code
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String locationCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.customer_remark
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String customerRemark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.sequence_no
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private Integer sequenceNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.cost_code
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String costCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.quotation_head_id
     *
     * @mbggenerated Sun Jul 11 13:11:08 CST 2021
     */
    private String quotationHeadId;

    private Integer source;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.item_number
     *
     * @mbggenerated Thu Aug 05 15:28:03 CST 2021
     */
    private Integer itemNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.item_version_id
     *
     * @mbggenerated Thu Aug 05 15:28:03 CST 2021
     */
    private Integer itemVersionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.standard_id
     *
     * @mbggenerated Thu Aug 05 15:28:03 CST 2021
     */
    private Integer standardId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.standard_version_id
     *
     * @mbggenerated Thu Aug 05 15:28:03 CST 2021
     */
    private Integer standardVersionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.standard_name
     *
     * @mbggenerated Thu Aug 05 15:28:03 CST 2021
     */
    private String standardName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_service_item.pp_version_id
     *
     * @mbggenerated Thu Aug 05 15:28:03 CST 2021
     */
    private Integer ppVersionId;

    private BigDecimal finalAmount;

    private BigDecimal mainCurrencyFinalAmount;

    private String clientStandard;

    private String remarkCn;

    @JsonProperty("testLines")
    private List<QuotationTestLineInstanceDto> testLineInstanceList;

    @JsonProperty("pps")
    private List<QuotationPpInstanceDto>  ppInstanceList;


}
