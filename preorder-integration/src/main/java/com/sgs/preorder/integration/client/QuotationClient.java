package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.BeanUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.facade.model.common.ResponseCode;
import com.sgs.preorder.integration.client.dto.*;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.PerformanceInvoiceFileDTO;
import com.sgs.priceengine.facade.model.request.CreateQuotationPerformanceRequest;
import com.sgs.priceengine.facade.model.request.GetQuotationDetailRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class QuotationClient {

    @Resource
    private InterfaceConfig interfaceConfig;

    @Resource
    private QuotationFacade quotationFacade;

    //    {
//        "orderId": "e36dcf56-dbee-4356-b2a2-6518a00b8236",
//            "buCode": "HL",
//            "systemId": "15",
//            "orderNo": "GZHL2211054234PS",
//            "enquiryFlag": 0,
//            "quotationHeadId": "995bfbdb-609d-40fd-8239-6fdefb8dc254",
//            "sgsToken": "2392998fb4a346779cf9e780066c3ffa"
//    }
    @SneakyThrows
    public QuotationHeadDto queryDetail(QuotationQueryDto queryRequest) {
        GetQuotationDetailRequest getQuotationDetailRequest = new GetQuotationDetailRequest();
        BeanUtil.copyProperties(queryRequest,getQuotationDetailRequest);
        getQuotationDetailRequest.setProductLineCode(queryRequest.getBuCode());
        BaseResponse baseResponse = quotationFacade.getQuotationDetail(getQuotationDetailRequest);
        String data = (String)baseResponse.getData();
        QuotationHeadDto quotationHeadDto = new QuotationHeadDto();
        JSONObject jsonObject = JSON.parseObject(data);
        if (Func.isNull(jsonObject)) {
            return quotationHeadDto;
        }
        JSONObject headObjectMap = jsonObject.getJSONObject("head");

        if (Func.isNotEmpty(headObjectMap)) {
            QuotationHeadDto headerObject = headObjectMap.getObject("headObject", QuotationHeadDto.class);
            if (Func.isNotEmpty(headerObject)) {
                quotationHeadDto = headerObject;
            }
        }
        List<QuotationServiceItemDto> serviceItemList = new ArrayList<>();
        JSONArray lineDataArray = jsonObject.getJSONArray("lineData");
        if (Func.isNotEmpty(lineDataArray)) {
            for (Object o : lineDataArray) {
                JSONObject lineObjectMap = (JSONObject) o;
                QuotationServiceItemDto serviceItemDto = lineObjectMap.getObject("lineObject", QuotationServiceItemDto.class);
                if (Func.isEmpty(serviceItemDto)) {
                    continue;
                }
                serviceItemList.add(serviceItemDto);
            }
        }
        quotationHeadDto.setServiceItems(serviceItemList);
        // MR 不需要执行生成报价单的附件
        if(!"MR".equalsIgnoreCase(queryRequest.getBuCode())){
            PerformanceInvoiceFileCreateRequest performanceInvoiceFileCreateRequest = new PerformanceInvoiceFileCreateRequest();
            performanceInvoiceFileCreateRequest.setQuotationVersion(quotationHeadDto.getQuotationVersion());
            performanceInvoiceFileCreateRequest.setCurrencyCode(quotationHeadDto.getCurrency());
            performanceInvoiceFileCreateRequest.setOrderId(quotationHeadDto.getOrderId());
            performanceInvoiceFileCreateRequest.setQuotationHeadId(quotationHeadDto.getId());
            performanceInvoiceFileCreateRequest.setBuCode(quotationHeadDto.getBuCode());
            performanceInvoiceFileCreateRequest.setProductLineCode(queryRequest.getBuCode());
            PerformanceInvoiceFileDto performanceInvoiceFileDto=  createPerformanceInvoiceFile(performanceInvoiceFileCreateRequest);
            quotationHeadDto.setPerformanceInvoiceFiles(Arrays.asList(performanceInvoiceFileDto));
        }
        return quotationHeadDto;
    }

    public PerformanceInvoiceFileDto createPerformanceInvoiceFile(PerformanceInvoiceFileCreateRequest createRequest){
        PerformanceInvoiceFileDto performanceInvoiceFileDto = new PerformanceInvoiceFileDto();
        CreateQuotationPerformanceRequest invoiceFileCreateReq = new CreateQuotationPerformanceRequest();
        invoiceFileCreateReq.setOrderId(createRequest.getOrderId());
        invoiceFileCreateReq.setQuotationHeadId(createRequest.getQuotationHeadId());
        invoiceFileCreateReq.setBuCode(createRequest.getBuCode());
        invoiceFileCreateReq.setTemplateSettingId("495");
        invoiceFileCreateReq.setCurrencyCode(createRequest.getCurrencyCode());
        invoiceFileCreateReq.setQuotationVersion(createRequest.getQuotationVersion());
        invoiceFileCreateReq.setQuotationFormLanguage("CHI");
        invoiceFileCreateReq.setTemplateSettingTypeID(1614);
        invoiceFileCreateReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
        invoiceFileCreateReq.setProductLineCode(createRequest.getProductLineCode());

        BaseResponse  baseResponse =  quotationFacade.createQuotationPerformanceInvoiceFile(invoiceFileCreateReq);
        PerformanceInvoiceFileDTO performanceInvoiceFileDTO = (PerformanceInvoiceFileDTO)baseResponse.getData();
        if(Func.isNotEmpty(performanceInvoiceFileDTO)){
            performanceInvoiceFileDto.setFilePath(performanceInvoiceFileDTO.getCloudId());
            performanceInvoiceFileDto.setFileName(performanceInvoiceFileDTO.getFileName());
        }
        return performanceInvoiceFileDto;
    }

    public SwitchNewQuotationConfigRsp getSwitchNewQuotationConfig(SwitchNewQuotationConfigReq req) {
        if (Func.isEmpty(req.getLocationCode())) {
            return null;
        }
        try {
            String url = String.format("%s/api/priceEngineApi/quotation/v1/getSwitchNewQuotationConfig", interfaceConfig.getBaseUrl());
            log.info("QuotationClient.getSwitchNewQuotationConfig 请求参数：{}", JSON.toJSON(req));
            BaseResponse response = HttpClientUtil.post(url, req, BaseResponse.class);
            log.info("QuotationClient.getSwitchNewQuotationConfig 返回参数：{}", JSON.toJSON(response));
            if (ResponseCode.SUCCESS.getCode() == response.getStatus() && Func.isNotEmpty(response.getData())) {
                SwitchNewQuotationConfigRsp rsp = JSON.parseObject(response.getData().toString(), SwitchNewQuotationConfigRsp.class);
                return rsp;
            } else {
                return null;
            }
        } catch (Exception ex) {
            log.error("QuotationClient.getSwitchNewQuotationConfig 信息异常：{}.", ex);
        }
        return null;
    }
}
