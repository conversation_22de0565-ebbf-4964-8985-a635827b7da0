package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.otsnotes.facade.model.req.processrecord.ProcessRecordForReportDTO;
import com.sgs.otsnotes.facade.model.req.tracking.TrackingProcessReq;
import com.sgs.otsnotes.facade.model.rsp.tracking.TrackingProcessRecord;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.req.TrackingDataSyncInfoReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * tracking api
 *
 */

@Component
public class TrackingClient {
    private static final Logger logger = LoggerFactory.getLogger(TrackingClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;


    public boolean checkInvoiceConfirm(String orderNo,String productLineCode) {
        if(Func.isEmpty(productLineCode)){
            productLineCode = ProductLineContextHolder.getProductLineCode();
        }
        boolean flag = true;//	check 订单是否做了 invoice confirm
        String url = interfaceConfig.getBaseUrl()+"/tracking2api/processrecord/checkInvoiceConfirmNew?productLineCode="+ productLineCode;
        Map<String, Object> params = Maps.newHashMap();
        params.put("orderNo",orderNo);
        String result = null;
        try {
            logger.info("[{}] call tracking checkInvoiceConfirm url:{}",orderNo,url);
            result = HttpClientUtil.postJson(url, JSONObject.toJSONString(params));
            logger.info("[{}] call tracking checkInvoiceConfirm result:{}",orderNo,result);
            JSONArray array = JSONArray.parseArray(result) ;
            if(null == array || array.size() == 0){//没有confirmInvoice 结束
                logger.info("orderNo {} does not have confirmInvoice",orderNo);
                flag = false;
            }
        } catch (Exception e) {
            logger.error("checkInvoiceConfirm error:{}",e);
            return false;
        }

        return flag;
    }


    /**
     * @param orderNos
     * @param
     * @return
     */
    public List<ProcessRecordForReportDTO> getProcessRecordForReport(List<String> orderNos, String productLineCode,String pointCode) {
        ProcessRecordForReportDTO processRecordForReportDTO = new ProcessRecordForReportDTO();
        processRecordForReportDTO.setOrderNos(orderNos);
        processRecordForReportDTO.setPointCode(pointCode);

        Map<String, Object> headers = Maps.newHashMap();
        headers.put("productLineCode", productLineCode);

        List<ProcessRecordForReportDTO> resultFromTracking = null;
        try {
            resultFromTracking = HttpClientUtil.sendPost(interfaceConfig.getBaseUrl() + "/tracking2api/processrecord/getPrecessRecordForReport",headers,processRecordForReportDTO,
                     ProcessRecordForReportDTO.class);
            return resultFromTracking;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<TrackingProcessRecord> queryProcessRecord(TrackingProcessReq trackingProcessReq) {
        List<TrackingProcessRecord> processRecordList = Lists.newArrayList();
        String url = String.format(interfaceConfig.getBaseUrl() + "/tracking2api/processrecord/queryprocessrecordpaged");
        logger.info("tracking queryProcessRecord request:{}", JSON.toJSONString(trackingProcessReq));
        try {
            BaseResponse baseResponse = HttpClientUtil.doPost(url, trackingProcessReq, BaseResponse.class);
            if(baseResponse.isSuccess() && Func.isNotEmpty(baseResponse.getData())){
                JSONObject jsonObject = JSON.parseObject(baseResponse.getData().toString());
                JSONArray jsonArray = jsonObject.getJSONArray("list");
                if(Func.isNotEmpty(jsonArray)){
                    processRecordList = JSONArray.parseArray(jsonArray.toJSONString(), TrackingProcessRecord.class);
                }
            }
        } catch (Exception e){
            logger.error("tracking queryProcessRecord error:{}", e.getMessage());
        }
        return processRecordList;
    }

    /**
     * 数据修复使用
     * @param trackingDataSyncInfoReq
     * @return
     */
    public BaseResponse repairProcessRecord(TrackingDataSyncInfoReq trackingDataSyncInfoReq) {
        String url = String.format(interfaceConfig.getBaseUrl() + "/tracking2api/test/processrecord/repairProcessRecord");
        try {
            BaseResponse baseResponse = HttpClientUtil.doPost(url, trackingDataSyncInfoReq, BaseResponse.class);
            return baseResponse;
        } catch (Exception e){
            logger.error("tracking queryProcessRecord error:{}", e.getMessage());
        }
        return BaseResponse.newFailInstance("repairProcessRecord error");
    }
}
