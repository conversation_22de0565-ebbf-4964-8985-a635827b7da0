
package com.sgs.preorder.integration.client.webservice;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex typeJava
 * 
 * <p>
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CaseNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SubcontractNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "caseNo",
    "subcontractNo"
})
@XmlRootElement(name = "UpdSubcontractStatus")
public class UpdSubcontractStatus {

    @XmlElement(name = "CaseNo")
    protected String caseNo;
    @XmlElement(name = "SubcontractNo")
    protected String subcontractNo;

    /**
     * caseNo
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCaseNo() {
        return caseNo;
    }

    /**
     * caseNo
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCaseNo(String value) {
        this.caseNo = value;
    }

    /**
     * subcontractNo
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSubcontractNo() {
        return subcontractNo;
    }

    /**
     * ubcontractNo
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSubcontractNo(String value) {
        this.subcontractNo = value;
    }

}
