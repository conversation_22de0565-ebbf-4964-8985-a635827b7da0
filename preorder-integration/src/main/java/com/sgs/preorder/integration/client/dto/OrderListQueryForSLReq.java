package com.sgs.preorder.integration.client.dto;

import com.sgs.preorder.facade.model.common.BaseRequest;
import lombok.Data;

@Data
public class OrderListQueryForSLReq extends BaseRequest {
    private Integer page;
    private Integer rows;
    private String orderStatus;
    private String orderNo;
    private String buyerName;
    private String applicantName;
    private String referenceNo;
    private String csName;
    private Long payerBossNumber;
    private String orderCreateDateFrom;
    private String orderCreateDateTo;
    private String orderExpectDueDateFrom;
    private String orderExpectDueDateTo;
    private String createdBy;
    private String sgsToken;
}
