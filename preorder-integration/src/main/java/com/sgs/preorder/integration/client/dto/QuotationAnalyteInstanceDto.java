package com.sgs.preorder.integration.client.dto;

public class QuotationAnalyteInstanceDto {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_analyte_instance.id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_analyte_instance.quotation_test_line_instance_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String quotationTestLineInstanceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_analyte_instance.order_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_analyte_instance.test_analyte_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String testAnalyteId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_analyte_instance.test_analyte_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String testAnalyteName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_analyte_instance.bu_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String buCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_analyte_instance.lab_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    private String labCode;

    private String locationCode;

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_analyte_instance.id
     *
     * @return the value of tb_pe_quotation_analyte_instance.id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_analyte_instance.id
     *
     * @param id the value for tb_pe_quotation_analyte_instance.id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_analyte_instance.quotation_test_line_instance_id
     *
     * @return the value of tb_pe_quotation_analyte_instance.quotation_test_line_instance_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getQuotationTestLineInstanceId() {
        return quotationTestLineInstanceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_analyte_instance.quotation_test_line_instance_id
     *
     * @param quotationTestLineInstanceId the value for tb_pe_quotation_analyte_instance.quotation_test_line_instance_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setQuotationTestLineInstanceId(String quotationTestLineInstanceId) {
        this.quotationTestLineInstanceId = quotationTestLineInstanceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_analyte_instance.order_id
     *
     * @return the value of tb_pe_quotation_analyte_instance.order_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_analyte_instance.order_id
     *
     * @param orderId the value for tb_pe_quotation_analyte_instance.order_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_analyte_instance.test_analyte_id
     *
     * @return the value of tb_pe_quotation_analyte_instance.test_analyte_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getTestAnalyteId() {
        return testAnalyteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_analyte_instance.test_analyte_id
     *
     * @param testAnalyteId the value for tb_pe_quotation_analyte_instance.test_analyte_id
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setTestAnalyteId(String testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_analyte_instance.test_analyte_name
     *
     * @return the value of tb_pe_quotation_analyte_instance.test_analyte_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_analyte_instance.test_analyte_name
     *
     * @param testAnalyteName the value for tb_pe_quotation_analyte_instance.test_analyte_name
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_analyte_instance.bu_code
     *
     * @return the value of tb_pe_quotation_analyte_instance.bu_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getBuCode() {
        return buCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_analyte_instance.bu_code
     *
     * @param buCode the value for tb_pe_quotation_analyte_instance.bu_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_pe_quotation_analyte_instance.lab_code
     *
     * @return the value of tb_pe_quotation_analyte_instance.lab_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public String getLabCode() {
        return labCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_pe_quotation_analyte_instance.lab_code
     *
     * @param labCode the value for tb_pe_quotation_analyte_instance.lab_code
     *
     * @mbggenerated Wed Jan 08 13:29:54 CST 2020
     */
    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }
}