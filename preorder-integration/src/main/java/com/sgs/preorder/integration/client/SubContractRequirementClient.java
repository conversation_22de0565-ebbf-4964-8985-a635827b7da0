package com.sgs.preorder.integration.client;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.facade.SubContractRequirementFacade;
import com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementInfo;
import com.sgs.otsnotes.facade.model.req.GPOSubContractReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SubContractRequirementClient {
    private static final Logger logger = LoggerFactory.getLogger(SubContractRequirementClient.class);
    @Autowired
    private SubContractRequirementFacade subContractRequirementFacade;

    /**
     *
     * @param productLineCode
     * @param subContractNo
     * @return
     */
    public SubcontractRequirementInfo subcontractRequirementInfo(String productLineCode, String subContractNo) {
        GPOSubContractReq reqObject = new GPOSubContractReq();
        reqObject.setSubContractNo(subContractNo);
        reqObject.setProductLineCode(productLineCode);
        try {
            BaseResponse<SubcontractRequirementInfo> rspResult = subContractRequirementFacade.getSubcontractRequirementInfo(reqObject);
            return rspResult.getData();
        } catch (Exception ex) {
            logger.error("subContractRequirementFacade.getSubcontractRequirementInfo 获取SubcontractRequirement({})信息异常：{}.", subContractNo, ex.getMessage(), ex);
        }
        return null;
    }
}
