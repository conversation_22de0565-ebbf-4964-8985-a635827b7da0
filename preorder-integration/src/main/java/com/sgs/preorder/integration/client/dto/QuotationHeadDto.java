package com.sgs.preorder.integration.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class QuotationHeadDto {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.id
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.order_id
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.currency
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    @JsonProperty("currency")
    private String currency;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.service_type
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String serviceType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.customer_reference
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String customerReference;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.sample_size
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String sampleSize;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.remark
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.report_no
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String reportNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.net_amount
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private BigDecimal netAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.tax_amount
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private BigDecimal taxAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.total_amount
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private BigDecimal totalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.special_discount
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private BigDecimal specialDiscount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.adjust_amount
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private BigDecimal adjustAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.final_amount
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private BigDecimal finalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.min_charge
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private Boolean minCharge;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.min_charge_amount
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private BigDecimal minChargeAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.bu_code
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String buCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.lab_code
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String labCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.location_code
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String locationCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.status
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.created_date
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private Date createdDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.created_by
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String createdBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.modified_date
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private Date modifiedDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.modified_by
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private String modifiedBy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_pe_quotation_head.confirmed_date
     *
     * @mbggenerated Thu Feb 27 15:15:40 CST 2020
     */
    private Date confirmedDate;

    private Integer priceMode;

    private String fcmQuotationNo;

    private String tat;

    private Date fcmQuotationDate;

    private Integer version;

    private BigDecimal orderDiscount;

    private boolean discountApplying;

    private Integer enquiryFlag;

    private String orderNo;

    private String quotationNo;
    private Integer toBossFlag;
    private BigDecimal subcontractFee;
    private String subcontractFeeCurrency;
    private String orderType;
    private Integer quotationVersion;
    private String payerEn;
    private String payerCn;
    private Boolean btnCancelDisabled;
    private Integer toTestFlag;

    @JsonProperty("payer")
    private QuotationCustomerInfoDto quotationPayer;

    private List<QuotationServiceItemDto> serviceItems;

    private List<PerformanceInvoiceFileDto> performanceInvoiceFiles;

}
