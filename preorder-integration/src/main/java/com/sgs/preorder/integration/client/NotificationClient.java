package com.sgs.preorder.integration.client;

import com.alibaba.fastjson.JSON;
import com.sgs.core.exception.SGSException;
import com.sgs.core.utils.HttpClient;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.util.HttpClientUtil;
import com.sgs.preorder.facade.model.dto.EmailAutoSendDTO;
import com.sgs.preorder.facade.model.dto.notification.SendEmailRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class NotificationClient {
    private static final Logger logger = LoggerFactory.getLogger(NotificationClient.class);
    @Resource
    private InterfaceConfig interfaceConfig;


    /**
     *
     * @param emailTemplatecode
     * @return
     */
    public String queryEmailTemplet(String emailTemplatecode){
       		/*
		 * 邮件模板 的 code：SL_CS_SendEmail，和common service 要保持一致
		 */
        String emailTemplatecodeUrl = String.format("%s/NotificationApi/notification/getEmailTempletByCode"+"/" + emailTemplatecode, interfaceConfig.getBaseUrl());

        String mailText = "";
        logger.info("===从 notification 获取 quotation 报价模板地址，URL={}", emailTemplatecodeUrl);
        try {
            net.sf.json.JSONObject emailTemplate = HttpClient.get(emailTemplatecodeUrl);
            //logger.info("===接口返回的模板内容：emailTemplate={}", emailTemplate.toString());
            mailText = emailTemplate.getString("mailText");
        } catch (Exception e) {
            logger.error("======获取 quotation 邮件模板失败====={}", e);
            throw new SGSException("EMAIL-TEMPLATE-ERROR", "获取邮件模板接口失败！！");
        }
        return mailText;
    }
    public boolean sendMail(SendEmailRequest sendEmailRequest) {
        String url = String.format("%s/NotificationApi/notification/sendMail", interfaceConfig.getBaseUrl());
        logger.info("===send mail，params={}", JSON.toJSONString(sendEmailRequest));
        try {
            String paramResult= null;
            try {
                paramResult= HttpClientUtil.postJson(url, sendEmailRequest);
            } catch (Exception e) {
                return false;
            }
            return "true".equals(paramResult);
        } catch (Exception e) {
            logger.error("======send mail fail====={}", e);
        }
        return false;
    }

    /**
     * 发送邮件（支持freemark）
     * @return
     */
    public String sendMailForGeneral(EmailAutoSendDTO emailAutoSendDTO) {
        emailAutoSendDTO.setSystemId(SgsSystem.GPO.getSgsSystemId()+"");
        String notificationApiUrl = String.format("%s/NotificationApi/notification/sendMailForGeneral", interfaceConfig.getBaseUrl());
        String result = null;
        try {
            //logger.info("=====send email pamams:{}======",JSON.toJSONString(emailAutoSendDTO));
            result =  HttpClientUtil.postJson(notificationApiUrl,emailAutoSendDTO);
            //logger.info("=====send email result:{}======",JSON.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("==SendEmail,error. url:{},msg:{}==",notificationApiUrl,e);
            return e.getMessage();
        }
        return result;
    }
}
