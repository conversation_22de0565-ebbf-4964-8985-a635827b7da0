<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sgs-preorder</artifactId>
        <groupId>com.sgs.gpo</groupId>
        <version>1.1.562</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${gpo.version}</version>
    <artifactId>preorder-integration</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>preorder-core</artifactId>
        </dependency>
       <dependency>
           <groupId>com.sgs.pse</groupId>
           <artifactId>general-preorder-priceengine-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sgs.gpo</groupId>
                    <artifactId>preorder-facade</artifactId>
                </exclusion>
            </exclusions>
       </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-log</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-facade</artifactId>
            <version>0.1.28</version>
        </dependency>
        <dependency>
            <groupId>com.sgs.extsystem</groupId>
            <artifactId>extsystem-facade-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sgs.preorder</groupId>
                    <artifactId>preorder-facade-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sgs.extsystem</groupId>
            <artifactId>extsystem-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>